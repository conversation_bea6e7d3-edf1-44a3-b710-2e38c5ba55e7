# 流程联动功能说明

## 问题解决方案

### 1. 布局高度齐平问题 ✅
- **问题**：左右两侧底面不齐，文件列表过长导致布局不一致
- **解决方案**：
  - 使用 `min-h-[calc(100vh-120px)]` 替代固定高度
  - 添加 `flex flex-col` 布局确保两侧对齐
  - 文件列表区域使用 `flex-1 overflow-y-auto` 实现滚动

### 2. 文件列表滚动功能 ✅
- **问题**：文件过多时超出边界
- **解决方案**：
  - 文件列表容器设置 `flex-1 overflow-y-auto min-h-0`
  - 确保在固定高度内滚动，不影响整体布局

### 3. 左右联动效果 ✅
- **问题**：左侧流程与右侧操作无联动
- **解决方案**：使用 React Context + Custom Hook 实现全局状态管理

## 联动实现方式

### 技术架构
```
App.tsx (ProcessFlowProvider)
├── ProcessFlow.tsx (消费状态)
└── FileUploadSimple.tsx (触发状态变更)
```

### 核心文件
1. **`hooks/useProcessFlow.ts`** - 流程状态管理逻辑
2. **`contexts/ProcessFlowContext.tsx`** - React Context 状态共享
3. **`components/ProcessFlow.tsx`** - 流程展示组件（消费状态）
4. **`components/FileUploadSimple.tsx`** - 文件操作组件（触发状态）

### 联动触发点

#### 1. 文件上传联动
```typescript
// 触发：文件上传完成
onFileUploaded() → 激活"文件上传"步骤 → 1秒后完成
```

#### 2. 文件验证联动
```typescript
// 触发：点击验证按钮
onFileValidation() → 激活"内容检测"步骤 → 进度条动画 → 完成
```

#### 3. 文件处理联动
```typescript
// 触发：点击智能处理按钮
onFileProcessing() → 激活"解析配置" → 完成 → 激活"智能分析" → 进度条 → 完成
```

#### 4. 报表生成联动
```typescript
// 触发：点击生成报表按钮
onReportGeneration() → 激活"报表生成" → 进度条 → 完成 → 激活"结果下载" → 完成
```

## 视觉效果

### 1. 步骤状态指示
- **pending（待处理）**：灰色图标，无动画
- **active（进行中）**：蓝色发光，旋转动画
- **completed（已完成）**：绿色对勾，缩放动画
- **error（错误）**：红色警告图标

### 2. 进度条效果
- **步骤级进度条**：显示当前步骤的处理进度
- **总体进度条**：显示整个流程的完成百分比
- **连接线动画**：步骤间的连接线根据状态变色

### 3. 亮灯效果
- 当前活动步骤会有发光边框（`animate-glow`）
- 已完成步骤保持绿色高亮
- 未开始步骤保持暗色调

## 配置方式

### 1. 修改步骤定义
在 `hooks/useProcessFlow.ts` 中修改 `initialSteps` 数组：
```typescript
const initialSteps: ProcessStep[] = [
  {
    id: 'upload',
    title: '文件上传',
    description: '上传入户数据和投诉数据文件',
    status: 'pending'
  },
  // 添加更多步骤...
];
```

### 2. 调整联动时机
在 `hooks/useProcessFlow.ts` 中修改联动方法：
```typescript
const onFileProcessing = useCallback(() => {
  activateStep('configure');
  // 自定义处理逻辑和时间
  setTimeout(() => {
    completeStep('configure');
    activateStep('analyze');
  }, 1000); // 调整延时
}, []);
```

### 3. 自定义进度动画
修改进度更新逻辑：
```typescript
// 模拟分析过程
let progress = 0;
const interval = setInterval(() => {
  progress += 10; // 调整进度增量
  updateStepProgress('analyze', progress);
  if (progress >= 100) {
    clearInterval(interval);
    completeStep('analyze');
  }
}, 300); // 调整更新频率
```

## 演示效果

### 完整流程演示
1. **上传文件** → 左侧"文件上传"步骤亮起并完成
2. **点击验证** → 左侧"内容检测"步骤激活，显示进度条
3. **点击处理** → 左侧"解析配置"和"智能分析"依次激活
4. **生成报表** → 左侧"报表生成"和"结果下载"依次激活
5. **总体进度** → 底部进度条实时更新到100%

### 视觉特效
- ✨ 步骤图标旋转动画
- 🌟 发光边框效果
- 📊 流畅的进度条动画
- 🔗 连接线状态变化
- 🎯 完成时的缩放动画

现在左右两侧完全联动，操作右侧任何功能都会实时反映在左侧流程面板上！
