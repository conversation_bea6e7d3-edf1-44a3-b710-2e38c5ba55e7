[{"D:\\pyworkspace\\analysisFlow\\frontend\\src\\index.tsx": "1", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\App.tsx": "2", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\Header.tsx": "3", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\ProcessFlow.tsx": "4", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSection.tsx": "5", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSimple.tsx": "6", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\contexts\\ProcessFlowContext.tsx": "7", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\hooks\\useProcessFlow.ts": "8", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\StreamOutput.tsx": "9", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\analyzeStage.ts": "10", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\configureStage.ts": "11", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\generateStage.ts": "12"}, {"size": 274, "mtime": 1751952682979, "results": "13", "hashOfConfig": "14"}, {"size": 2155, "mtime": 1751965832411, "results": "15", "hashOfConfig": "14"}, {"size": 2735, "mtime": 1751964606398, "results": "16", "hashOfConfig": "14"}, {"size": 9268, "mtime": 1751965988473, "results": "17", "hashOfConfig": "14"}, {"size": 14160, "mtime": 1751954656765, "results": "18", "hashOfConfig": "14"}, {"size": 22391, "mtime": 1751965944574, "results": "19", "hashOfConfig": "14"}, {"size": 1377, "mtime": 1751957877632, "results": "20", "hashOfConfig": "14"}, {"size": 9558, "mtime": 1751965916393, "results": "21", "hashOfConfig": "14"}, {"size": 8701, "mtime": 1751960250380, "results": "22", "hashOfConfig": "14"}, {"size": 2775, "mtime": 1751960168471, "results": "23", "hashOfConfig": "14"}, {"size": 1723, "mtime": 1751960138306, "results": "24", "hashOfConfig": "14"}, {"size": 4201, "mtime": 1751960205078, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tezzf1", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\pyworkspace\\analysisFlow\\frontend\\src\\index.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\App.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\Header.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\ProcessFlow.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSection.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSimple.tsx", ["62"], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\contexts\\ProcessFlowContext.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\hooks\\useProcessFlow.ts", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\StreamOutput.tsx", ["63"], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\analyzeStage.ts", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\configureStage.ts", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\generateStage.ts", [], [], {"ruleId": "64", "severity": 1, "message": "65", "line": 190, "column": 9, "nodeType": "66", "messageId": "67", "endLine": 190, "endColumn": 31}, {"ruleId": "68", "severity": 1, "message": "69", "line": 95, "column": 6, "nodeType": "70", "endLine": 95, "endColumn": 16, "suggestions": "71"}, "@typescript-eslint/no-unused-vars", "'downloadAnalysisReport' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'isCompleted', 'isPlaying', and 'startStreaming'. Either include them or remove the dependency array.", "ArrayExpression", ["72"], {"desc": "73", "fix": "74"}, "Update the dependencies array to be: [isActive, isCompleted, isPlaying, startStreaming]", {"range": "75", "text": "76"}, [2476, 2486], "[isActive, isCompleted, isPlaying, startStreaming]"]