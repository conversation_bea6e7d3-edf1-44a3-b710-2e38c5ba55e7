[{"D:\\pyworkspace\\analysisFlow\\frontend\\src\\index.tsx": "1", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\App.tsx": "2", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\Header.tsx": "3", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\ProcessFlow.tsx": "4", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSection.tsx": "5", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSimple.tsx": "6", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\contexts\\ProcessFlowContext.tsx": "7", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\hooks\\useProcessFlow.ts": "8", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\StreamOutput.tsx": "9", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\analyzeStage.ts": "10", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\configureStage.ts": "11", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\generateStage.ts": "12"}, {"size": 274, "mtime": 1751952682979, "results": "13", "hashOfConfig": "14"}, {"size": 1616, "mtime": 1751956281593, "results": "15", "hashOfConfig": "14"}, {"size": 2053, "mtime": 1751963425950, "results": "16", "hashOfConfig": "14"}, {"size": 9713, "mtime": 1751963875665, "results": "17", "hashOfConfig": "14"}, {"size": 14160, "mtime": 1751954656765, "results": "18", "hashOfConfig": "14"}, {"size": 17204, "mtime": 1751963847453, "results": "19", "hashOfConfig": "14"}, {"size": 1377, "mtime": 1751957877632, "results": "20", "hashOfConfig": "14"}, {"size": 8863, "mtime": 1751962157351, "results": "21", "hashOfConfig": "14"}, {"size": 8701, "mtime": 1751960250380, "results": "22", "hashOfConfig": "14"}, {"size": 2775, "mtime": 1751960168471, "results": "23", "hashOfConfig": "14"}, {"size": 1723, "mtime": 1751960138306, "results": "24", "hashOfConfig": "14"}, {"size": 4201, "mtime": 1751960205078, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tezzf1", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\pyworkspace\\analysisFlow\\frontend\\src\\index.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\App.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\Header.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\ProcessFlow.tsx", ["62", "63"], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSection.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSimple.tsx", ["64"], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\contexts\\ProcessFlowContext.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\hooks\\useProcessFlow.ts", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\StreamOutput.tsx", ["65"], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\analyzeStage.ts", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\configureStage.ts", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\generateStage.ts", [], [], {"ruleId": "66", "severity": 1, "message": "67", "line": 1, "column": 17, "nodeType": "68", "messageId": "69", "endLine": 1, "endColumn": 25}, {"ruleId": "66", "severity": 1, "message": "70", "line": 17, "column": 11, "nodeType": "68", "messageId": "69", "endLine": 17, "endColumn": 24}, {"ruleId": "66", "severity": 1, "message": "71", "line": 149, "column": 9, "nodeType": "68", "messageId": "69", "endLine": 149, "endColumn": 31}, {"ruleId": "72", "severity": 1, "message": "73", "line": 95, "column": 6, "nodeType": "74", "endLine": 95, "endColumn": 16, "suggestions": "75"}, "@typescript-eslint/no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'ProcessedFile' is defined but never used.", "'downloadAnalysisReport' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'isCompleted', 'isPlaying', and 'startStreaming'. Either include them or remove the dependency array.", "ArrayExpression", ["76"], {"desc": "77", "fix": "78"}, "Update the dependencies array to be: [isActive, isCompleted, isPlaying, startStreaming]", {"range": "79", "text": "80"}, [2476, 2486], "[isActive, isCompleted, isPlaying, startStreaming]"]