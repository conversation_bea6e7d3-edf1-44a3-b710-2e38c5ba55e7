[{"D:\\pyworkspace\\analysisFlow\\frontend\\src\\index.tsx": "1", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\App.tsx": "2", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\Header.tsx": "3", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\ProcessFlow.tsx": "4", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSection.tsx": "5", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSimple.tsx": "6", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\contexts\\ProcessFlowContext.tsx": "7", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\hooks\\useProcessFlow.ts": "8"}, {"size": 274, "mtime": 1751952682979, "results": "9", "hashOfConfig": "10"}, {"size": 1616, "mtime": 1751956281593, "results": "11", "hashOfConfig": "10"}, {"size": 2071, "mtime": 1751952705989, "results": "12", "hashOfConfig": "10"}, {"size": 7027, "mtime": 1751956389498, "results": "13", "hashOfConfig": "10"}, {"size": 14160, "mtime": 1751954656765, "results": "14", "hashOfConfig": "10"}, {"size": 18680, "mtime": 1751958043488, "results": "15", "hashOfConfig": "10"}, {"size": 1377, "mtime": 1751957877632, "results": "16", "hashOfConfig": "10"}, {"size": 8442, "mtime": 1751957863193, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tezzf1", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\pyworkspace\\analysisFlow\\frontend\\src\\index.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\App.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\Header.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\ProcessFlow.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSection.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSimple.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\contexts\\ProcessFlowContext.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\hooks\\useProcessFlow.ts", [], []]