[{"D:\\pyworkspace\\analysisFlow\\frontend\\src\\index.tsx": "1", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\App.tsx": "2", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\Header.tsx": "3", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\ProcessFlow.tsx": "4", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSection.tsx": "5", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSimple.tsx": "6", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\contexts\\ProcessFlowContext.tsx": "7", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\hooks\\useProcessFlow.ts": "8", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\StreamOutput.tsx": "9", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\analyzeStage.ts": "10", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\configureStage.ts": "11", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\generateStage.ts": "12", "D:\\pyworkspace\\analysisFlow\\frontend\\src\\utils\\eventEmitter.ts": "13"}, {"size": 274, "mtime": 1751952682979, "results": "14", "hashOfConfig": "15"}, {"size": 2273, "mtime": 1751969080248, "results": "16", "hashOfConfig": "15"}, {"size": 2735, "mtime": 1751964606398, "results": "17", "hashOfConfig": "15"}, {"size": 9647, "mtime": 1751970574295, "results": "18", "hashOfConfig": "15"}, {"size": 14160, "mtime": 1751954656765, "results": "19", "hashOfConfig": "15"}, {"size": 22214, "mtime": 1751970759615, "results": "20", "hashOfConfig": "15"}, {"size": 1593, "mtime": 1751970501638, "results": "21", "hashOfConfig": "15"}, {"size": 11404, "mtime": 1751974306805, "results": "22", "hashOfConfig": "15"}, {"size": 8701, "mtime": 1751960250380, "results": "23", "hashOfConfig": "15"}, {"size": 2775, "mtime": 1751960168471, "results": "24", "hashOfConfig": "15"}, {"size": 1723, "mtime": 1751960138306, "results": "25", "hashOfConfig": "15"}, {"size": 4201, "mtime": 1751960205078, "results": "26", "hashOfConfig": "15"}, {"size": 706, "mtime": 1751970546192, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tezzf1", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\pyworkspace\\analysisFlow\\frontend\\src\\index.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\App.tsx", ["67"], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\Header.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\ProcessFlow.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSection.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\FileUploadSimple.tsx", ["68"], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\contexts\\ProcessFlowContext.tsx", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\hooks\\useProcessFlow.ts", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\components\\StreamOutput.tsx", ["69"], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\analyzeStage.ts", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\configureStage.ts", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\data\\streamOutputs\\generateStage.ts", [], [], "D:\\pyworkspace\\analysisFlow\\frontend\\src\\utils\\eventEmitter.ts", [], [], {"ruleId": "70", "severity": 1, "message": "71", "line": 13, "column": 9, "nodeType": "72", "messageId": "73", "endLine": 13, "endColumn": 25}, {"ruleId": "70", "severity": 1, "message": "74", "line": 45, "column": 83, "nodeType": "72", "messageId": "73", "endLine": 45, "endColumn": 92}, {"ruleId": "75", "severity": 1, "message": "76", "line": 95, "column": 6, "nodeType": "77", "endLine": 95, "endColumn": 16, "suggestions": "78"}, "@typescript-eslint/no-unused-vars", "'isAnalysisActive' is assigned a value but never used.", "Identifier", "unusedVar", "'resetFlow' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'isCompleted', 'isPlaying', and 'startStreaming'. Either include them or remove the dependency array.", "ArrayExpression", ["79"], {"desc": "80", "fix": "81"}, "Update the dependencies array to be: [isActive, isCompleted, isPlaying, startStreaming]", {"range": "82", "text": "83"}, [2476, 2486], "[isActive, isCompleted, isPlaying, startStreaming]"]