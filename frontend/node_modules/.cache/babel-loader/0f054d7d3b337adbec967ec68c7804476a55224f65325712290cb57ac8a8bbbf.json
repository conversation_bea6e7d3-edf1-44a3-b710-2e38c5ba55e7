{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\components\\\\FileUploadSimple.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Upload, FileText, CheckCircle, AlertCircle, Settings, Play, Download, Trash2, Eye, Zap } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FileUploadSimple = () => {\n  _s();\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [selectedMonth, setSelectedMonth] = useState('2024-01');\n  const [selectedFileType, setSelectedFileType] = useState('household');\n  const handleFileUpload = event => {\n    const files = event.target.files;\n    if (files) {\n      Array.from(files).forEach(file => {\n        const newFile = {\n          id: Math.random().toString(36).substr(2, 9),\n          filename: file.name,\n          file_type: selectedFileType,\n          month: selectedMonth,\n          status: 'uploaded',\n          upload_time: new Date().toISOString(),\n          file_path: `/uploads/${file.name}`\n        };\n        setUploadedFiles(prev => [...prev, newFile]);\n      });\n    }\n  };\n  const validateFile = fileId => {\n    setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n      ...file,\n      status: 'validated'\n    } : file));\n  };\n  const processFile = fileId => {\n    setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n      ...file,\n      status: 'processing'\n    } : file));\n    setTimeout(() => {\n      setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n        ...file,\n        status: 'processed'\n      } : file));\n    }, 3000);\n  };\n  const deleteFile = fileId => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  };\n  const processAllFiles = () => {\n    setIsProcessing(true);\n    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');\n    validatedFiles.forEach((file, index) => {\n      setTimeout(() => {\n        processFile(file.id);\n      }, index * 1000);\n    });\n    setTimeout(() => {\n      setIsProcessing(false);\n    }, validatedFiles.length * 1000 + 2000);\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'uploaded':\n        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/30';\n      case 'validated':\n        return 'text-tech-green bg-tech-green/10 border-tech-green/30';\n      case 'processing':\n        return 'text-tech-cyan bg-tech-cyan/10 border-tech-cyan/30';\n      case 'processed':\n        return 'text-tech-purple bg-tech-purple/10 border-tech-purple/30';\n      case 'error':\n        return 'text-red-400 bg-red-400/10 border-red-400/30';\n      default:\n        return 'text-gray-400 bg-gray-400/10 border-gray-400/30';\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'uploaded':\n        return /*#__PURE__*/_jsxDEV(Upload, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 31\n        }, this);\n      case 'validated':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 32\n        }, this);\n      case 'processing':\n        return /*#__PURE__*/_jsxDEV(Settings, {\n          className: \"w-4 h-4 animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 33\n        }, this);\n      case 'processed':\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 32\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 28\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6 h-full flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-6\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-white\",\n          children: \"\\u6570\\u636E\\u6587\\u4EF6\\u4E0A\\u4F20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Zap, {\n            className: \"w-5 h-5 text-tech-cyan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-tech-cyan text-sm\",\n            children: \"\\u667A\\u80FD\\u5904\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300 mb-2\",\n            children: \"\\u6587\\u4EF6\\u7C7B\\u578B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedFileType,\n            onChange: e => setSelectedFileType(e.target.value),\n            className: \"tech-input w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"household\",\n              children: \"\\u5165\\u6237\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"complaint\",\n              children: \"\\u6295\\u8BC9\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300 mb-2\",\n            children: \"\\u6708\\u4EFD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"month\",\n            value: selectedMonth,\n            onChange: e => setSelectedMonth(e.target.value),\n            className: \"tech-input w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-2 border-dashed border-gray-600 hover:border-tech-cyan/50 rounded-lg p-8 text-center transition-all duration-300\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 bg-tech-cyan/20 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(Upload, {\n              className: \"w-8 h-8 text-tech-cyan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-medium text-white\",\n              children: \"\\u70B9\\u51FB\\u4E0A\\u4F20\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mt-1\",\n              children: \"\\u652F\\u6301 .xlsx, .xls \\u683C\\u5F0F\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            multiple: true,\n            accept: \".xlsx,.xls\",\n            onChange: handleFileUpload,\n            className: \"tech-button\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-6 flex-1 flex flex-col min-h-0\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.2\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-white\",\n          children: [\"\\u5DF2\\u4E0A\\u4F20\\u6587\\u4EF6 (\", uploadedFiles.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: processAllFiles,\n            disabled: isProcessing || uploadedFiles.filter(f => f.status === 'validated').length === 0,\n            className: \"tech-button text-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: [/*#__PURE__*/_jsxDEV(Play, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), \"\\u4E00\\u952E\\u5904\\u7406\\u5168\\u90E8\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3 flex-1 overflow-y-auto min-h-0\",\n        children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: uploadedFiles.map(file => /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"tech-border p-4 rounded-lg\",\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            exit: {\n              opacity: 0,\n              x: 20\n            },\n            layout: true,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-2 rounded-lg border ${getStatusColor(file.status)}`,\n                  children: getStatusIcon(file.status)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-white truncate\",\n                    children: file.filename\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: file.file_type === 'household' ? '入户数据' : '投诉数据'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: file.month\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-2 py-1 rounded-full text-xs ${getStatusColor(file.status)}`,\n                      children: [file.status === 'uploaded' && '已上传', file.status === 'validated' && '已验证', file.status === 'processing' && '处理中', file.status === 'processed' && '已处理', file.status === 'error' && '错误']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 239,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [file.status === 'uploaded' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => validateFile(file.id),\n                  className: \"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\",\n                  title: \"\\u9A8C\\u8BC1\\u6587\\u4EF6\",\n                  children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 23\n                }, this), file.status === 'validated' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => processFile(file.id),\n                  className: \"p-2 text-tech-cyan hover:bg-tech-cyan/10 rounded-lg transition-colors\",\n                  title: \"\\u667A\\u80FD\\u5904\\u7406\",\n                  children: /*#__PURE__*/_jsxDEV(Play, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 23\n                }, this), file.status === 'processed' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 text-tech-purple hover:bg-tech-purple/10 rounded-lg transition-colors\",\n                    title: \"\\u9884\\u89C8\\u7ED3\\u679C\",\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\",\n                    title: \"\\u4E0B\\u8F7D\\u7ED3\\u679C\",\n                    children: /*#__PURE__*/_jsxDEV(Download, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 283,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => deleteFile(file.id),\n                  className: \"p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors\",\n                  title: \"\\u5220\\u9664\\u6587\\u4EF6\",\n                  children: /*#__PURE__*/_jsxDEV(Trash2, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), file.status === 'processing' && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"mt-3\",\n              initial: {\n                opacity: 0,\n                height: 0\n              },\n              animate: {\n                opacity: 1,\n                height: 'auto'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-700 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"progress-bar h-2 rounded-full\",\n                  initial: {\n                    width: 0\n                  },\n                  animate: {\n                    width: '100%'\n                  },\n                  transition: {\n                    duration: 3,\n                    ease: \"easeInOut\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-tech-cyan mt-1\",\n                children: \"\\u6B63\\u5728\\u8FDB\\u884C\\u667A\\u80FD\\u5206\\u6790...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this)]\n          }, file.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), uploadedFiles.length === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"text-center py-12 text-gray-400\",\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u6682\\u65E0\\u4E0A\\u4F20\\u6587\\u4EF6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm mt-1\",\n            children: \"\\u8BF7\\u5148\\u4E0A\\u4F20\\u6570\\u636E\\u6587\\u4EF6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), uploadedFiles.some(f => f.status === 'processed') && /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-6\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.4\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-white mb-4\",\n          children: \"\\u751F\\u6210\\u5206\\u6790\\u62A5\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"tech-button text-lg px-8 py-3\",\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"w-5 h-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this), \"\\u751F\\u6210\\u667A\\u80FD\\u5206\\u6790\\u62A5\\u8868\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n};\n_s(FileUploadSimple, \"kDMKhOizcSL9D1K+XZ+Z1Msh+9c=\");\n_c = FileUploadSimple;\nexport default FileUploadSimple;\nvar _c;\n$RefreshReg$(_c, \"FileUploadSimple\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "Upload", "FileText", "CheckCircle", "AlertCircle", "Settings", "Play", "Download", "Trash2", "Eye", "Zap", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FileUploadSimple", "_s", "uploadedFiles", "setUploadedFiles", "isProcessing", "setIsProcessing", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMonth", "selectedFileType", "setSelectedFileType", "handleFileUpload", "event", "files", "target", "Array", "from", "for<PERSON>ach", "file", "newFile", "id", "Math", "random", "toString", "substr", "filename", "name", "file_type", "month", "status", "upload_time", "Date", "toISOString", "file_path", "prev", "validateFile", "fileId", "map", "processFile", "setTimeout", "deleteFile", "filter", "processAllFiles", "validatedFiles", "index", "length", "getStatusColor", "getStatusIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "div", "initial", "opacity", "y", "animate", "value", "onChange", "e", "type", "multiple", "accept", "transition", "delay", "onClick", "disabled", "f", "x", "exit", "layout", "title", "height", "width", "duration", "ease", "some", "_c", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/components/FileUploadSimple.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\nimport { \n  Upload, \n  FileText, \n  CheckCircle, \n  AlertCircle, \n  Settings, \n  Play, \n  Download,\n  Trash2,\n  Eye,\n  Zap\n} from 'lucide-react';\n\ninterface UploadedFile {\n  id: string;\n  filename: string;\n  file_type: 'household' | 'complaint';\n  month: string;\n  status: 'uploaded' | 'validated' | 'processing' | 'processed' | 'error';\n  upload_time: string;\n  file_path: string;\n  error_message?: string;\n}\n\nconst FileUploadSimple: React.FC = () => {\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [selectedMonth, setSelectedMonth] = useState('2024-01');\n  const [selectedFileType, setSelectedFileType] = useState<'household' | 'complaint'>('household');\n\n  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files;\n    if (files) {\n      Array.from(files).forEach(file => {\n        const newFile: UploadedFile = {\n          id: Math.random().toString(36).substr(2, 9),\n          filename: file.name,\n          file_type: selectedFileType,\n          month: selectedMonth,\n          status: 'uploaded',\n          upload_time: new Date().toISOString(),\n          file_path: `/uploads/${file.name}`\n        };\n        \n        setUploadedFiles(prev => [...prev, newFile]);\n      });\n    }\n  };\n\n  const validateFile = (fileId: string) => {\n    setUploadedFiles(prev => \n      prev.map(file => \n        file.id === fileId \n          ? { ...file, status: 'validated' }\n          : file\n      )\n    );\n  };\n\n  const processFile = (fileId: string) => {\n    setUploadedFiles(prev => \n      prev.map(file => \n        file.id === fileId \n          ? { ...file, status: 'processing' }\n          : file\n      )\n    );\n\n    setTimeout(() => {\n      setUploadedFiles(prev => \n        prev.map(file => \n          file.id === fileId \n            ? { ...file, status: 'processed' }\n            : file\n        )\n      );\n    }, 3000);\n  };\n\n  const deleteFile = (fileId: string) => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  };\n\n  const processAllFiles = () => {\n    setIsProcessing(true);\n    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');\n    \n    validatedFiles.forEach((file, index) => {\n      setTimeout(() => {\n        processFile(file.id);\n      }, index * 1000);\n    });\n\n    setTimeout(() => {\n      setIsProcessing(false);\n    }, validatedFiles.length * 1000 + 2000);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'uploaded': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/30';\n      case 'validated': return 'text-tech-green bg-tech-green/10 border-tech-green/30';\n      case 'processing': return 'text-tech-cyan bg-tech-cyan/10 border-tech-cyan/30';\n      case 'processed': return 'text-tech-purple bg-tech-purple/10 border-tech-purple/30';\n      case 'error': return 'text-red-400 bg-red-400/10 border-red-400/30';\n      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/30';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'uploaded': return <Upload className=\"w-4 h-4\" />;\n      case 'validated': return <CheckCircle className=\"w-4 h-4\" />;\n      case 'processing': return <Settings className=\"w-4 h-4 animate-spin\" />;\n      case 'processed': return <FileText className=\"w-4 h-4\" />;\n      case 'error': return <AlertCircle className=\"w-4 h-4\" />;\n      default: return <FileText className=\"w-4 h-4\" />;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6 h-full flex flex-col\">\n      {/* 文件上传区域 */}\n      <motion.div\n        className=\"tech-card p-6\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-xl font-bold text-white\">数据文件上传</h2>\n          <div className=\"flex items-center space-x-2\">\n            <Zap className=\"w-5 h-5 text-tech-cyan\" />\n            <span className=\"text-tech-cyan text-sm\">智能处理</span>\n          </div>\n        </div>\n\n        {/* 文件类型和月份选择 */}\n        <div className=\"grid grid-cols-2 gap-4 mb-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n              文件类型\n            </label>\n            <select\n              value={selectedFileType}\n              onChange={(e) => setSelectedFileType(e.target.value as 'household' | 'complaint')}\n              className=\"tech-input w-full\"\n            >\n              <option value=\"household\">入户数据</option>\n              <option value=\"complaint\">投诉数据</option>\n            </select>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n              月份\n            </label>\n            <input\n              type=\"month\"\n              value={selectedMonth}\n              onChange={(e) => setSelectedMonth(e.target.value)}\n              className=\"tech-input w-full\"\n            />\n          </div>\n        </div>\n\n        {/* 文件上传区域 */}\n        <div className=\"border-2 border-dashed border-gray-600 hover:border-tech-cyan/50 rounded-lg p-8 text-center transition-all duration-300\">\n          <div className=\"flex flex-col items-center space-y-4\">\n            <div className=\"p-4 bg-tech-cyan/20 rounded-full\">\n              <Upload className=\"w-8 h-8 text-tech-cyan\" />\n            </div>\n            <div>\n              <p className=\"text-lg font-medium text-white\">\n                点击上传文件\n              </p>\n              <p className=\"text-sm text-gray-400 mt-1\">\n                支持 .xlsx, .xls 格式文件\n              </p>\n            </div>\n            <input\n              type=\"file\"\n              multiple\n              accept=\".xlsx,.xls\"\n              onChange={handleFileUpload}\n              className=\"tech-button\"\n            />\n          </div>\n        </div>\n      </motion.div>\n\n      {/* 已上传文件列表 */}\n      <motion.div\n        className=\"tech-card p-6 flex-1 flex flex-col min-h-0\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.2 }}\n      >\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-white\">\n            已上传文件 ({uploadedFiles.length})\n          </h3>\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={processAllFiles}\n              disabled={isProcessing || uploadedFiles.filter(f => f.status === 'validated').length === 0}\n              className=\"tech-button text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <Play className=\"w-4 h-4 mr-2\" />\n              一键处理全部\n            </button>\n          </div>\n        </div>\n\n        <div className=\"space-y-3 flex-1 overflow-y-auto min-h-0\">\n          <AnimatePresence>\n            {uploadedFiles.map((file) => (\n              <motion.div\n                key={file.id}\n                className=\"tech-border p-4 rounded-lg\"\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: 20 }}\n                layout\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3 flex-1\">\n                    <div className={`p-2 rounded-lg border ${getStatusColor(file.status)}`}>\n                      {getStatusIcon(file.status)}\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <h4 className=\"font-medium text-white truncate\">\n                        {file.filename}\n                      </h4>\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-400\">\n                        <span>{file.file_type === 'household' ? '入户数据' : '投诉数据'}</span>\n                        <span>{file.month}</span>\n                        <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(file.status)}`}>\n                          {file.status === 'uploaded' && '已上传'}\n                          {file.status === 'validated' && '已验证'}\n                          {file.status === 'processing' && '处理中'}\n                          {file.status === 'processed' && '已处理'}\n                          {file.status === 'error' && '错误'}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2\">\n                    {file.status === 'uploaded' && (\n                      <button\n                        onClick={() => validateFile(file.id)}\n                        className=\"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\"\n                        title=\"验证文件\"\n                      >\n                        <CheckCircle className=\"w-4 h-4\" />\n                      </button>\n                    )}\n\n                    {file.status === 'validated' && (\n                      <button\n                        onClick={() => processFile(file.id)}\n                        className=\"p-2 text-tech-cyan hover:bg-tech-cyan/10 rounded-lg transition-colors\"\n                        title=\"智能处理\"\n                      >\n                        <Play className=\"w-4 h-4\" />\n                      </button>\n                    )}\n\n                    {file.status === 'processed' && (\n                      <>\n                        <button\n                          className=\"p-2 text-tech-purple hover:bg-tech-purple/10 rounded-lg transition-colors\"\n                          title=\"预览结果\"\n                        >\n                          <Eye className=\"w-4 h-4\" />\n                        </button>\n                        <button\n                          className=\"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\"\n                          title=\"下载结果\"\n                        >\n                          <Download className=\"w-4 h-4\" />\n                        </button>\n                      </>\n                    )}\n\n                    <button\n                      onClick={() => deleteFile(file.id)}\n                      className=\"p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors\"\n                      title=\"删除文件\"\n                    >\n                      <Trash2 className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                </div>\n\n                {file.status === 'processing' && (\n                  <motion.div\n                    className=\"mt-3\"\n                    initial={{ opacity: 0, height: 0 }}\n                    animate={{ opacity: 1, height: 'auto' }}\n                  >\n                    <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                      <motion.div\n                        className=\"progress-bar h-2 rounded-full\"\n                        initial={{ width: 0 }}\n                        animate={{ width: '100%' }}\n                        transition={{ duration: 3, ease: \"easeInOut\" }}\n                      />\n                    </div>\n                    <p className=\"text-xs text-tech-cyan mt-1\">正在进行智能分析...</p>\n                  </motion.div>\n                )}\n              </motion.div>\n            ))}\n          </AnimatePresence>\n\n          {uploadedFiles.length === 0 && (\n            <motion.div\n              className=\"text-center py-12 text-gray-400\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n            >\n              <FileText className=\"w-12 h-12 mx-auto mb-4 opacity-50\" />\n              <p>暂无上传文件</p>\n              <p className=\"text-sm mt-1\">请先上传数据文件</p>\n            </motion.div>\n          )}\n        </div>\n      </motion.div>\n\n      {/* 生成报表按钮 */}\n      {uploadedFiles.some(f => f.status === 'processed') && (\n        <motion.div\n          className=\"tech-card p-6\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n        >\n          <div className=\"text-center\">\n            <h3 className=\"text-lg font-semibold text-white mb-4\">\n              生成分析报表\n            </h3>\n            <button className=\"tech-button text-lg px-8 py-3\">\n              <FileText className=\"w-5 h-5 mr-2\" />\n              生成智能分析报表\n            </button>\n          </div>\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\nexport default FileUploadSimple;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AAEvD,SACEC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,WAAW,EACXC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,GAAG,EACHC,GAAG,QACE,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAatB,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAA4B,WAAW,CAAC;EAEhG,MAAM2B,gBAAgB,GAAIC,KAA0C,IAAK;IACvE,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,EAAE;MACTE,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,OAAO,CAACC,IAAI,IAAI;QAChC,MAAMC,OAAqB,GAAG;UAC5BC,EAAE,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;UAC3CC,QAAQ,EAAEP,IAAI,CAACQ,IAAI;UACnBC,SAAS,EAAElB,gBAAgB;UAC3BmB,KAAK,EAAErB,aAAa;UACpBsB,MAAM,EAAE,UAAU;UAClBC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACrCC,SAAS,EAAE,YAAYf,IAAI,CAACQ,IAAI;QAClC,CAAC;QAEDtB,gBAAgB,CAAC8B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEf,OAAO,CAAC,CAAC;MAC9C,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMgB,YAAY,GAAIC,MAAc,IAAK;IACvChC,gBAAgB,CAAC8B,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACnB,IAAI,IACXA,IAAI,CAACE,EAAE,KAAKgB,MAAM,GACd;MAAE,GAAGlB,IAAI;MAAEW,MAAM,EAAE;IAAY,CAAC,GAChCX,IACN,CACF,CAAC;EACH,CAAC;EAED,MAAMoB,WAAW,GAAIF,MAAc,IAAK;IACtChC,gBAAgB,CAAC8B,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACnB,IAAI,IACXA,IAAI,CAACE,EAAE,KAAKgB,MAAM,GACd;MAAE,GAAGlB,IAAI;MAAEW,MAAM,EAAE;IAAa,CAAC,GACjCX,IACN,CACF,CAAC;IAEDqB,UAAU,CAAC,MAAM;MACfnC,gBAAgB,CAAC8B,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACnB,IAAI,IACXA,IAAI,CAACE,EAAE,KAAKgB,MAAM,GACd;QAAE,GAAGlB,IAAI;QAAEW,MAAM,EAAE;MAAY,CAAC,GAChCX,IACN,CACF,CAAC;IACH,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMsB,UAAU,GAAIJ,MAAc,IAAK;IACrChC,gBAAgB,CAAC8B,IAAI,IAAIA,IAAI,CAACO,MAAM,CAACvB,IAAI,IAAIA,IAAI,CAACE,EAAE,KAAKgB,MAAM,CAAC,CAAC;EACnE,CAAC;EAED,MAAMM,eAAe,GAAGA,CAAA,KAAM;IAC5BpC,eAAe,CAAC,IAAI,CAAC;IACrB,MAAMqC,cAAc,GAAGxC,aAAa,CAACsC,MAAM,CAACvB,IAAI,IAAIA,IAAI,CAACW,MAAM,KAAK,WAAW,CAAC;IAEhFc,cAAc,CAAC1B,OAAO,CAAC,CAACC,IAAI,EAAE0B,KAAK,KAAK;MACtCL,UAAU,CAAC,MAAM;QACfD,WAAW,CAACpB,IAAI,CAACE,EAAE,CAAC;MACtB,CAAC,EAAEwB,KAAK,GAAG,IAAI,CAAC;IAClB,CAAC,CAAC;IAEFL,UAAU,CAAC,MAAM;MACfjC,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,EAAEqC,cAAc,CAACE,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;EACzC,CAAC;EAED,MAAMC,cAAc,GAAIjB,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,UAAU;QAAE,OAAO,uDAAuD;MAC/E,KAAK,WAAW;QAAE,OAAO,uDAAuD;MAChF,KAAK,YAAY;QAAE,OAAO,oDAAoD;MAC9E,KAAK,WAAW;QAAE,OAAO,0DAA0D;MACnF,KAAK,OAAO;QAAE,OAAO,8CAA8C;MACnE;QAAS,OAAO,iDAAiD;IACnE;EACF,CAAC;EAED,MAAMkB,aAAa,GAAIlB,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,UAAU;QAAE,oBAAO/B,OAAA,CAACX,MAAM;UAAC6D,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,WAAW;QAAE,oBAAOtD,OAAA,CAACT,WAAW;UAAC2D,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5D,KAAK,YAAY;QAAE,oBAAOtD,OAAA,CAACP,QAAQ;UAACyD,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvE,KAAK,WAAW;QAAE,oBAAOtD,OAAA,CAACV,QAAQ;UAAC4D,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD,KAAK,OAAO;QAAE,oBAAOtD,OAAA,CAACR,WAAW;UAAC0D,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxD;QAAS,oBAAOtD,OAAA,CAACV,QAAQ;UAAC4D,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClD;EACF,CAAC;EAED,oBACEtD,OAAA;IAAKkD,SAAS,EAAC,gCAAgC;IAAAK,QAAA,gBAE7CvD,OAAA,CAACb,MAAM,CAACqE,GAAG;MACTN,SAAS,EAAC,eAAe;MACzBO,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBAE9BvD,OAAA;QAAKkD,SAAS,EAAC,wCAAwC;QAAAK,QAAA,gBACrDvD,OAAA;UAAIkD,SAAS,EAAC,8BAA8B;UAAAK,QAAA,EAAC;QAAM;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxDtD,OAAA;UAAKkD,SAAS,EAAC,6BAA6B;UAAAK,QAAA,gBAC1CvD,OAAA,CAACF,GAAG;YAACoD,SAAS,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CtD,OAAA;YAAMkD,SAAS,EAAC,wBAAwB;YAAAK,QAAA,EAAC;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtD,OAAA;QAAKkD,SAAS,EAAC,6BAA6B;QAAAK,QAAA,gBAC1CvD,OAAA;UAAAuD,QAAA,gBACEvD,OAAA;YAAOkD,SAAS,EAAC,8CAA8C;YAAAK,QAAA,EAAC;UAEhE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtD,OAAA;YACE6D,KAAK,EAAElD,gBAAiB;YACxBmD,QAAQ,EAAGC,CAAC,IAAKnD,mBAAmB,CAACmD,CAAC,CAAC/C,MAAM,CAAC6C,KAAkC,CAAE;YAClFX,SAAS,EAAC,mBAAmB;YAAAK,QAAA,gBAE7BvD,OAAA;cAAQ6D,KAAK,EAAC,WAAW;cAAAN,QAAA,EAAC;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvCtD,OAAA;cAAQ6D,KAAK,EAAC,WAAW;cAAAN,QAAA,EAAC;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNtD,OAAA;UAAAuD,QAAA,gBACEvD,OAAA;YAAOkD,SAAS,EAAC,8CAA8C;YAAAK,QAAA,EAAC;UAEhE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtD,OAAA;YACEgE,IAAI,EAAC,OAAO;YACZH,KAAK,EAAEpD,aAAc;YACrBqD,QAAQ,EAAGC,CAAC,IAAKrD,gBAAgB,CAACqD,CAAC,CAAC/C,MAAM,CAAC6C,KAAK,CAAE;YAClDX,SAAS,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtD,OAAA;QAAKkD,SAAS,EAAC,yHAAyH;QAAAK,QAAA,eACtIvD,OAAA;UAAKkD,SAAS,EAAC,sCAAsC;UAAAK,QAAA,gBACnDvD,OAAA;YAAKkD,SAAS,EAAC,kCAAkC;YAAAK,QAAA,eAC/CvD,OAAA,CAACX,MAAM;cAAC6D,SAAS,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNtD,OAAA;YAAAuD,QAAA,gBACEvD,OAAA;cAAGkD,SAAS,EAAC,gCAAgC;cAAAK,QAAA,EAAC;YAE9C;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtD,OAAA;cAAGkD,SAAS,EAAC,4BAA4B;cAAAK,QAAA,EAAC;YAE1C;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNtD,OAAA;YACEgE,IAAI,EAAC,MAAM;YACXC,QAAQ;YACRC,MAAM,EAAC,YAAY;YACnBJ,QAAQ,EAAEjD,gBAAiB;YAC3BqC,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbtD,OAAA,CAACb,MAAM,CAACqE,GAAG;MACTN,SAAS,EAAC,4CAA4C;MACtDO,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BQ,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAb,QAAA,gBAE3BvD,OAAA;QAAKkD,SAAS,EAAC,wCAAwC;QAAAK,QAAA,gBACrDvD,OAAA;UAAIkD,SAAS,EAAC,kCAAkC;UAAAK,QAAA,GAAC,kCACxC,EAAClD,aAAa,CAAC0C,MAAM,EAAC,GAC/B;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtD,OAAA;UAAKkD,SAAS,EAAC,gBAAgB;UAAAK,QAAA,eAC7BvD,OAAA;YACEqE,OAAO,EAAEzB,eAAgB;YACzB0B,QAAQ,EAAE/D,YAAY,IAAIF,aAAa,CAACsC,MAAM,CAAC4B,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,WAAW,CAAC,CAACgB,MAAM,KAAK,CAAE;YAC3FG,SAAS,EAAC,qEAAqE;YAAAK,QAAA,gBAE/EvD,OAAA,CAACN,IAAI;cAACwD,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wCAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtD,OAAA;QAAKkD,SAAS,EAAC,0CAA0C;QAAAK,QAAA,gBACvDvD,OAAA,CAACZ,eAAe;UAAAmE,QAAA,EACblD,aAAa,CAACkC,GAAG,CAAEnB,IAAI,iBACtBpB,OAAA,CAACb,MAAM,CAACqE,GAAG;YAETN,SAAS,EAAC,4BAA4B;YACtCO,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEc,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCZ,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEc,CAAC,EAAE;YAAE,CAAE;YAC9BC,IAAI,EAAE;cAAEf,OAAO,EAAE,CAAC;cAAEc,CAAC,EAAE;YAAG,CAAE;YAC5BE,MAAM;YAAAnB,QAAA,gBAENvD,OAAA;cAAKkD,SAAS,EAAC,mCAAmC;cAAAK,QAAA,gBAChDvD,OAAA;gBAAKkD,SAAS,EAAC,oCAAoC;gBAAAK,QAAA,gBACjDvD,OAAA;kBAAKkD,SAAS,EAAE,yBAAyBF,cAAc,CAAC5B,IAAI,CAACW,MAAM,CAAC,EAAG;kBAAAwB,QAAA,EACpEN,aAAa,CAAC7B,IAAI,CAACW,MAAM;gBAAC;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACNtD,OAAA;kBAAKkD,SAAS,EAAC,gBAAgB;kBAAAK,QAAA,gBAC7BvD,OAAA;oBAAIkD,SAAS,EAAC,iCAAiC;oBAAAK,QAAA,EAC5CnC,IAAI,CAACO;kBAAQ;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACLtD,OAAA;oBAAKkD,SAAS,EAAC,mDAAmD;oBAAAK,QAAA,gBAChEvD,OAAA;sBAAAuD,QAAA,EAAOnC,IAAI,CAACS,SAAS,KAAK,WAAW,GAAG,MAAM,GAAG;oBAAM;sBAAAsB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC/DtD,OAAA;sBAAAuD,QAAA,EAAOnC,IAAI,CAACU;oBAAK;sBAAAqB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzBtD,OAAA;sBAAMkD,SAAS,EAAE,kCAAkCF,cAAc,CAAC5B,IAAI,CAACW,MAAM,CAAC,EAAG;sBAAAwB,QAAA,GAC9EnC,IAAI,CAACW,MAAM,KAAK,UAAU,IAAI,KAAK,EACnCX,IAAI,CAACW,MAAM,KAAK,WAAW,IAAI,KAAK,EACpCX,IAAI,CAACW,MAAM,KAAK,YAAY,IAAI,KAAK,EACrCX,IAAI,CAACW,MAAM,KAAK,WAAW,IAAI,KAAK,EACpCX,IAAI,CAACW,MAAM,KAAK,OAAO,IAAI,IAAI;oBAAA;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtD,OAAA;gBAAKkD,SAAS,EAAC,6BAA6B;gBAAAK,QAAA,GACzCnC,IAAI,CAACW,MAAM,KAAK,UAAU,iBACzB/B,OAAA;kBACEqE,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAACjB,IAAI,CAACE,EAAE,CAAE;kBACrC4B,SAAS,EAAC,yEAAyE;kBACnFyB,KAAK,EAAC,0BAAM;kBAAApB,QAAA,eAEZvD,OAAA,CAACT,WAAW;oBAAC2D,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CACT,EAEAlC,IAAI,CAACW,MAAM,KAAK,WAAW,iBAC1B/B,OAAA;kBACEqE,OAAO,EAAEA,CAAA,KAAM7B,WAAW,CAACpB,IAAI,CAACE,EAAE,CAAE;kBACpC4B,SAAS,EAAC,uEAAuE;kBACjFyB,KAAK,EAAC,0BAAM;kBAAApB,QAAA,eAEZvD,OAAA,CAACN,IAAI;oBAACwD,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CACT,EAEAlC,IAAI,CAACW,MAAM,KAAK,WAAW,iBAC1B/B,OAAA,CAAAE,SAAA;kBAAAqD,QAAA,gBACEvD,OAAA;oBACEkD,SAAS,EAAC,2EAA2E;oBACrFyB,KAAK,EAAC,0BAAM;oBAAApB,QAAA,eAEZvD,OAAA,CAACH,GAAG;sBAACqD,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACTtD,OAAA;oBACEkD,SAAS,EAAC,yEAAyE;oBACnFyB,KAAK,EAAC,0BAAM;oBAAApB,QAAA,eAEZvD,OAAA,CAACL,QAAQ;sBAACuD,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA,eACT,CACH,eAEDtD,OAAA;kBACEqE,OAAO,EAAEA,CAAA,KAAM3B,UAAU,CAACtB,IAAI,CAACE,EAAE,CAAE;kBACnC4B,SAAS,EAAC,mEAAmE;kBAC7EyB,KAAK,EAAC,0BAAM;kBAAApB,QAAA,eAEZvD,OAAA,CAACJ,MAAM;oBAACsD,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELlC,IAAI,CAACW,MAAM,KAAK,YAAY,iBAC3B/B,OAAA,CAACb,MAAM,CAACqE,GAAG;cACTN,SAAS,EAAC,MAAM;cAChBO,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEkB,MAAM,EAAE;cAAE,CAAE;cACnChB,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEkB,MAAM,EAAE;cAAO,CAAE;cAAArB,QAAA,gBAExCvD,OAAA;gBAAKkD,SAAS,EAAC,qCAAqC;gBAAAK,QAAA,eAClDvD,OAAA,CAACb,MAAM,CAACqE,GAAG;kBACTN,SAAS,EAAC,+BAA+B;kBACzCO,OAAO,EAAE;oBAAEoB,KAAK,EAAE;kBAAE,CAAE;kBACtBjB,OAAO,EAAE;oBAAEiB,KAAK,EAAE;kBAAO,CAAE;kBAC3BV,UAAU,EAAE;oBAAEW,QAAQ,EAAE,CAAC;oBAAEC,IAAI,EAAE;kBAAY;gBAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtD,OAAA;gBAAGkD,SAAS,EAAC,6BAA6B;gBAAAK,QAAA,EAAC;cAAW;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CACb;UAAA,GA9FIlC,IAAI,CAACE,EAAE;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+FF,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa,CAAC,EAEjBjD,aAAa,CAAC0C,MAAM,KAAK,CAAC,iBACzB/C,OAAA,CAACb,MAAM,CAACqE,GAAG;UACTN,SAAS,EAAC,iCAAiC;UAC3CO,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UAAAH,QAAA,gBAExBvD,OAAA,CAACV,QAAQ;YAAC4D,SAAS,EAAC;UAAmC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1DtD,OAAA;YAAAuD,QAAA,EAAG;UAAM;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACbtD,OAAA;YAAGkD,SAAS,EAAC,cAAc;YAAAK,QAAA,EAAC;UAAQ;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGZjD,aAAa,CAAC2E,IAAI,CAACT,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,WAAW,CAAC,iBAChD/B,OAAA,CAACb,MAAM,CAACqE,GAAG;MACTN,SAAS,EAAC,eAAe;MACzBO,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BQ,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAb,QAAA,eAE3BvD,OAAA;QAAKkD,SAAS,EAAC,aAAa;QAAAK,QAAA,gBAC1BvD,OAAA;UAAIkD,SAAS,EAAC,uCAAuC;UAAAK,QAAA,EAAC;QAEtD;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtD,OAAA;UAAQkD,SAAS,EAAC,+BAA+B;UAAAK,QAAA,gBAC/CvD,OAAA,CAACV,QAAQ;YAAC4D,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oDAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClD,EAAA,CAtUID,gBAA0B;AAAA8E,EAAA,GAA1B9E,gBAA0B;AAwUhC,eAAeA,gBAAgB;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}