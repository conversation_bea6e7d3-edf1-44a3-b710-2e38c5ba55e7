{"ast": null, "code": "/**\n * lucide-react v0.292.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst StopCircle = createLucideIcon(\"StopCircle\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"6\",\n  x: \"9\",\n  y: \"9\",\n  key: \"1wrtvo\"\n}]]);\nexport { StopCircle as default };\n//# sourceMappingURL=stop-circle.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}