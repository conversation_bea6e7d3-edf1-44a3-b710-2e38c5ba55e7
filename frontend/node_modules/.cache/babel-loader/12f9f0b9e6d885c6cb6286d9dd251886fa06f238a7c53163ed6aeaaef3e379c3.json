{"ast": null, "code": "/**\n * lucide-react v0.292.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Highlighter = createLucideIcon(\"Highlighter\", [[\"path\", {\n  d: \"m9 11-6 6v3h9l3-3\",\n  key: \"1a3l36\"\n}], [\"path\", {\n  d: \"m22 12-4.6 4.6a2 2 0 0 1-2.8 0l-5.2-5.2a2 2 0 0 1 0-2.8L14 4\",\n  key: \"14a9rk\"\n}]]);\nexport { Highlighter as default };\n//# sourceMappingURL=highlighter.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}