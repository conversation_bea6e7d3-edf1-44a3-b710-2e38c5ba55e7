{"ast": null, "code": "import { warnOnce } from '../../utils/warn-once.mjs';\nimport { useScroll } from '../use-scroll.mjs';\n\n/**\n * @deprecated useViewportScroll is deprecated. Convert to useScroll()\n */\nfunction useViewportScroll() {\n  if (process.env.NODE_ENV !== \"production\") {\n    warnOnce(false, \"useViewportScroll is deprecated. Convert to useScroll().\");\n  }\n  return useScroll();\n}\nexport { useViewportScroll };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}