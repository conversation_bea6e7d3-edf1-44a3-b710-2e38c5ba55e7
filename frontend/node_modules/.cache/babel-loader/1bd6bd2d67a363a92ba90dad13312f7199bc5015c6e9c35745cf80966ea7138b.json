{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\components\\\\FileUploadSimple.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\nimport { Upload, FileText, CheckCircle, AlertCircle, Settings, Play, Download, Trash2, Eye, Zap } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FileUploadSimple = () => {\n  _s();\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [selectedFileType, setSelectedFileType] = useState('household');\n  const [currentStreamStage, setCurrentStreamStage] = useState(null);\n  const fileInputRef = useRef(null);\n  const {\n    onFileUploaded,\n    onFileValidation,\n    onFileProcessing,\n    onReportGeneration,\n    resetFlow,\n    processState\n  } = useProcessFlowContext();\n  const handleFileUpload = event => {\n    const files = event.target.files;\n    if (files) {\n      Array.from(files).forEach(file => {\n        const newFile = {\n          id: Math.random().toString(36).substr(2, 9),\n          filename: file.name,\n          file_type: selectedFileType,\n          month: new Date().toISOString().slice(0, 7),\n          // YYYY-MM格式\n          status: 'uploaded',\n          upload_time: new Date().toISOString(),\n          file_path: `/uploads/${file.name}`\n        };\n        setUploadedFiles(prev => [...prev, newFile]);\n\n        // 触发流程联动\n        onFileUploaded(newFile.id, newFile.filename);\n      });\n    }\n  };\n  const validateFile = fileId => {\n    setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n      ...file,\n      status: 'validated'\n    } : file));\n\n    // 触发验证流程联动\n    onFileValidation(fileId);\n  };\n  const processFile = fileId => {\n    setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n      ...file,\n      status: 'processing'\n    } : file));\n\n    // 触发处理流程联动\n    onFileProcessing(fileId);\n    setTimeout(() => {\n      setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n        ...file,\n        status: 'processed'\n      } : file));\n    }, 3000);\n  };\n  const deleteFile = fileId => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  };\n  const processAllFiles = () => {\n    setIsProcessing(true);\n    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');\n\n    // 开始流式输出 - 解析配置阶段\n    setCurrentStreamStage('configure');\n\n    // 模拟处理流程\n    setTimeout(() => {\n      // 解析配置完成，开始智能分析\n      setCurrentStreamStage('analyze');\n\n      // 同时开始处理文件\n      validatedFiles.forEach((file, index) => {\n        setTimeout(() => {\n          processFile(file.id);\n        }, index * 1000);\n      });\n      setTimeout(() => {\n        // 智能分析完成，开始报表生成\n        setCurrentStreamStage('generate');\n        setTimeout(() => {\n          // 所有处理完成\n          setIsProcessing(false);\n          setCurrentStreamStage(null);\n        }, 8000); // 报表生成阶段时间\n      }, 6000); // 智能分析阶段时间\n    }, 4000); // 解析配置阶段时间\n  };\n\n  // 下载分析结果\n  const downloadAnalysisResult = (fileName, fileType) => {\n    // 模拟下载文件\n    const link = document.createElement('a');\n    link.href = '#'; // 实际项目中这里应该是后端API地址\n    link.download = `${fileName}_analysis_result.${fileType}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    // 显示下载成功提示\n    console.log(`下载 ${fileName} 的${fileType.toUpperCase()}分析结果`);\n  };\n\n  // 下载所有分析结果\n  const downloadAllResults = () => {\n    const processedFiles = uploadedFiles.filter(f => f.status === 'processed');\n    processedFiles.forEach(file => {\n      downloadAnalysisResult(file.filename, 'excel');\n    });\n\n    // 下载完成后重置流程\n    setTimeout(() => {\n      resetFlow();\n      setUploadedFiles([]);\n    }, 1000);\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'uploaded':\n        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/30';\n      case 'validated':\n        return 'text-tech-green bg-tech-green/10 border-tech-green/30';\n      case 'processing':\n        return 'text-tech-cyan bg-tech-cyan/10 border-tech-cyan/30';\n      case 'processed':\n        return 'text-tech-purple bg-tech-purple/10 border-tech-purple/30';\n      case 'error':\n        return 'text-red-400 bg-red-400/10 border-red-400/30';\n      default:\n        return 'text-gray-400 bg-gray-400/10 border-gray-400/30';\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'uploaded':\n        return /*#__PURE__*/_jsxDEV(Upload, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 31\n        }, this);\n      case 'validated':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 32\n        }, this);\n      case 'processing':\n        return /*#__PURE__*/_jsxDEV(Settings, {\n          className: \"w-4 h-4 animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 33\n        }, this);\n      case 'processed':\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 32\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 28\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 23\n        }, this);\n    }\n  };\n\n  // 获取文件的流程进度信息\n  const getFileProgress = fileId => {\n    return processState.fileProgresses.find(fp => fp.fileId === fileId);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6 h-full flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-6\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-white\",\n          children: \"\\u6570\\u636E\\u6587\\u4EF6\\u4E0A\\u4F20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Zap, {\n            className: \"w-5 h-5 text-tech-cyan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-tech-cyan text-sm\",\n            children: \"\\u667A\\u80FD\\u5904\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300 mb-2\",\n            children: \"\\u6587\\u4EF6\\u7C7B\\u578B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedFileType,\n            onChange: e => setSelectedFileType(e.target.value),\n            className: \"tech-input w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"household\",\n              children: \"\\u5165\\u6237\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"complaint\",\n              children: \"\\u6295\\u8BC9\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300 mb-2\",\n            children: \"\\u6708\\u4EFD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"month\",\n            value: selectedMonth,\n            onChange: e => setSelectedMonth(e.target.value),\n            className: \"tech-input w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-2 border-dashed border-gray-600 hover:border-tech-cyan/50 rounded-lg p-8 text-center transition-all duration-300\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 bg-tech-cyan/20 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(Upload, {\n              className: \"w-8 h-8 text-tech-cyan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-medium text-white\",\n              children: \"\\u70B9\\u51FB\\u4E0A\\u4F20\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mt-1\",\n              children: \"\\u652F\\u6301 .xlsx, .xls \\u683C\\u5F0F\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            multiple: true,\n            accept: \".xlsx,.xls\",\n            onChange: handleFileUpload,\n            className: \"tech-button\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-6 flex flex-col\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.2\n      },\n      style: {\n        height: '400px'\n      } // 固定高度\n      ,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-white\",\n          children: [\"\\u5DF2\\u4E0A\\u4F20\\u6587\\u4EF6 (\", uploadedFiles.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: processAllFiles,\n            disabled: isProcessing || uploadedFiles.filter(f => f.status === 'validated').length === 0,\n            className: \"tech-button text-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: [/*#__PURE__*/_jsxDEV(Play, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), \"\\u4E00\\u952E\\u5904\\u7406\\u5168\\u90E8\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto space-y-3 pr-2\",\n        style: {\n          maxHeight: '300px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: uploadedFiles.map(file => /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"tech-border p-4 rounded-lg\",\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            exit: {\n              opacity: 0,\n              x: 20\n            },\n            layout: true,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-2 rounded-lg border ${getStatusColor(file.status)}`,\n                  children: getStatusIcon(file.status)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-white truncate\",\n                    children: file.filename\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: file.file_type === 'household' ? '入户数据' : '投诉数据'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: file.month\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 306,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-2 py-1 rounded-full text-xs ${getStatusColor(file.status)}`,\n                      children: [file.status === 'uploaded' && '已上传', file.status === 'validated' && '已验证', file.status === 'processing' && '处理中', file.status === 'processed' && '已处理', file.status === 'error' && '错误']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 307,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 23\n                  }, this), (() => {\n                    const fileProgress = getFileProgress(file.id);\n                    if (fileProgress && fileProgress.stepProgress > 0) {\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between text-xs text-gray-400 mb-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            children: [\"\\u5F53\\u524D\\u6B65\\u9AA4: \", fileProgress.currentStep]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 323,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: [fileProgress.stepProgress, \"%\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 324,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 322,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-full bg-gray-700 rounded-full h-1\",\n                          children: /*#__PURE__*/_jsxDEV(motion.div, {\n                            className: \"bg-gradient-to-r from-blue-500 to-cyan-400 h-1 rounded-full\",\n                            initial: {\n                              width: 0\n                            },\n                            animate: {\n                              width: `${fileProgress.stepProgress}%`\n                            },\n                            transition: {\n                              duration: 0.3\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 327,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 326,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 321,\n                        columnNumber: 29\n                      }, this);\n                    }\n                    return null;\n                  })()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [file.status === 'uploaded' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => validateFile(file.id),\n                  className: \"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\",\n                  title: \"\\u9A8C\\u8BC1\\u6587\\u4EF6\",\n                  children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 23\n                }, this), file.status === 'validated' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => processFile(file.id),\n                  className: \"p-2 text-tech-cyan hover:bg-tech-cyan/10 rounded-lg transition-colors\",\n                  title: \"\\u667A\\u80FD\\u5904\\u7406\",\n                  children: /*#__PURE__*/_jsxDEV(Play, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 23\n                }, this), file.status === 'processed' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 text-tech-purple hover:bg-tech-purple/10 rounded-lg transition-colors\",\n                    title: \"\\u9884\\u89C8\\u7ED3\\u679C\",\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\",\n                    title: \"\\u4E0B\\u8F7D\\u7ED3\\u679C\",\n                    children: /*#__PURE__*/_jsxDEV(Download, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => deleteFile(file.id),\n                  className: \"p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors\",\n                  title: \"\\u5220\\u9664\\u6587\\u4EF6\",\n                  children: /*#__PURE__*/_jsxDEV(Trash2, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this), file.status === 'processing' && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"mt-3\",\n              initial: {\n                opacity: 0,\n                height: 0\n              },\n              animate: {\n                opacity: 1,\n                height: 'auto'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-700 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"progress-bar h-2 rounded-full\",\n                  initial: {\n                    width: 0\n                  },\n                  animate: {\n                    width: '100%'\n                  },\n                  transition: {\n                    duration: 3,\n                    ease: \"easeInOut\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-tech-cyan mt-1\",\n                children: \"\\u6B63\\u5728\\u8FDB\\u884C\\u667A\\u80FD\\u5206\\u6790...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 19\n            }, this)]\n          }, file.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), uploadedFiles.length === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"text-center py-12 text-gray-400\",\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u6682\\u65E0\\u4E0A\\u4F20\\u6587\\u4EF6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm mt-1\",\n            children: \"\\u8BF7\\u5148\\u4E0A\\u4F20\\u6570\\u636E\\u6587\\u4EF6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this), uploadedFiles.some(f => f.status === 'processed') && /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-6\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.4\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-white mb-4\",\n          children: \"\\u751F\\u6210\\u5206\\u6790\\u62A5\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"tech-button text-lg px-8 py-3\",\n            onClick: onReportGeneration,\n            children: [/*#__PURE__*/_jsxDEV(FileText, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this), \"\\u751F\\u6210\\u667A\\u80FD\\u5206\\u6790\\u62A5\\u8868\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 15\n          }, this), uploadedFiles.some(f => f.status === 'processed') && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"tech-card p-4 bg-green-900/20 border-green-500/30\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-green-400 font-semibold mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                className: \"w-5 h-5 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 21\n              }, this), \"\\u5206\\u6790\\u7ED3\\u679C\\u4E0B\\u8F7D\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [uploadedFiles.filter(f => f.status === 'processed').map(file => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between bg-gray-800/50 p-3 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: file.filename\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => downloadAnalysisResult(file.filename, 'excel'),\n                    className: \"text-xs bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(Download, {\n                      className: \"w-3 h-3 mr-1 inline\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 29\n                    }, this), \"Excel\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => downloadAnalysisResult(file.filename, 'pdf'),\n                    className: \"text-xs bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(Download, {\n                      className: \"w-3 h-3 mr-1 inline\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 474,\n                      columnNumber: 29\n                    }, this), \"PDF\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => downloadAnalysisResult(file.filename, 'json'),\n                    className: \"text-xs bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(Download, {\n                      className: \"w-3 h-3 mr-1 inline\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 29\n                    }, this), \"JSON\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 25\n                }, this)]\n              }, file.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 23\n              }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: downloadAllResults,\n                className: \"w-full mt-4 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-2 px-4 rounded-lg transition-all duration-300 flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(Download, {\n                  className: \"w-4 h-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 23\n                }, this), \"\\u4E0B\\u8F7D\\u6240\\u6709\\u5206\\u6790\\u7ED3\\u679C\\u5E76\\u91CD\\u7F6E\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 192,\n    columnNumber: 5\n  }, this);\n};\n_s(FileUploadSimple, \"mOsWkqGOrsjiHAa8nNRBAUYR51w=\", false, function () {\n  return [useProcessFlowContext];\n});\n_c = FileUploadSimple;\nexport default FileUploadSimple;\nvar _c;\n$RefreshReg$(_c, \"FileUploadSimple\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "motion", "AnimatePresence", "useProcessFlowContext", "Upload", "FileText", "CheckCircle", "AlertCircle", "Settings", "Play", "Download", "Trash2", "Eye", "Zap", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FileUploadSimple", "_s", "uploadedFiles", "setUploadedFiles", "isProcessing", "setIsProcessing", "selectedFileType", "setSelectedFileType", "currentStreamStage", "setCurrentStreamStage", "fileInputRef", "onFileUploaded", "onFileValidation", "onFileProcessing", "onReportGeneration", "resetFlow", "processState", "handleFileUpload", "event", "files", "target", "Array", "from", "for<PERSON>ach", "file", "newFile", "id", "Math", "random", "toString", "substr", "filename", "name", "file_type", "month", "Date", "toISOString", "slice", "status", "upload_time", "file_path", "prev", "validateFile", "fileId", "map", "processFile", "setTimeout", "deleteFile", "filter", "processAllFiles", "validatedFiles", "index", "downloadAnalysisResult", "fileName", "fileType", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "toUpperCase", "downloadAllResults", "processedFiles", "f", "getStatusColor", "getStatusIcon", "className", "_jsxFileName", "lineNumber", "columnNumber", "getFileProgress", "fileProgresses", "find", "fp", "children", "div", "initial", "opacity", "y", "animate", "value", "onChange", "e", "type", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMonth", "multiple", "accept", "transition", "delay", "style", "height", "length", "onClick", "disabled", "maxHeight", "x", "exit", "layout", "fileProgress", "stepProgress", "currentStep", "width", "duration", "title", "ease", "some", "_c", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/components/FileUploadSimple.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\nimport {\n  Upload,\n  FileText,\n  CheckCircle,\n  AlertCircle,\n  Settings,\n  Play,\n  Download,\n  Trash2,\n  Eye,\n  Zap\n} from 'lucide-react';\nimport { StreamOutput } from './StreamOutput';\nimport { configureStageOutput } from '../data/streamOutputs/configureStage';\nimport { analyzeStageOutput } from '../data/streamOutputs/analyzeStage';\nimport { generateStageOutput } from '../data/streamOutputs/generateStage';\n\ninterface UploadedFile {\n  id: string;\n  filename: string;\n  file_type: 'household' | 'complaint';\n  month: string;\n  status: 'uploaded' | 'validated' | 'processing' | 'processed' | 'error';\n  upload_time: string;\n  file_path: string;\n  error_message?: string;\n}\n\nconst FileUploadSimple: React.FC = () => {\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [selectedFileType, setSelectedFileType] = useState<'household' | 'complaint'>('household');\n  const [currentStreamStage, setCurrentStreamStage] = useState<'configure' | 'analyze' | 'generate' | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const { onFileUploaded, onFileValidation, onFileProcessing, onReportGeneration, resetFlow, processState } = useProcessFlowContext();\n\n  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files;\n    if (files) {\n      Array.from(files).forEach(file => {\n        const newFile: UploadedFile = {\n          id: Math.random().toString(36).substr(2, 9),\n          filename: file.name,\n          file_type: selectedFileType,\n          month: new Date().toISOString().slice(0, 7), // YYYY-MM格式\n          status: 'uploaded',\n          upload_time: new Date().toISOString(),\n          file_path: `/uploads/${file.name}`\n        };\n        \n        setUploadedFiles(prev => [...prev, newFile]);\n\n        // 触发流程联动\n        onFileUploaded(newFile.id, newFile.filename);\n      });\n    }\n  };\n\n  const validateFile = (fileId: string) => {\n    setUploadedFiles(prev =>\n      prev.map(file =>\n        file.id === fileId\n          ? { ...file, status: 'validated' }\n          : file\n      )\n    );\n\n    // 触发验证流程联动\n    onFileValidation(fileId);\n  };\n\n  const processFile = (fileId: string) => {\n    setUploadedFiles(prev =>\n      prev.map(file =>\n        file.id === fileId\n          ? { ...file, status: 'processing' }\n          : file\n      )\n    );\n\n    // 触发处理流程联动\n    onFileProcessing(fileId);\n\n    setTimeout(() => {\n      setUploadedFiles(prev =>\n        prev.map(file =>\n          file.id === fileId\n            ? { ...file, status: 'processed' }\n            : file\n        )\n      );\n    }, 3000);\n  };\n\n  const deleteFile = (fileId: string) => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  };\n\n  const processAllFiles = () => {\n    setIsProcessing(true);\n    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');\n\n    // 开始流式输出 - 解析配置阶段\n    setCurrentStreamStage('configure');\n\n    // 模拟处理流程\n    setTimeout(() => {\n      // 解析配置完成，开始智能分析\n      setCurrentStreamStage('analyze');\n\n      // 同时开始处理文件\n      validatedFiles.forEach((file, index) => {\n        setTimeout(() => {\n          processFile(file.id);\n        }, index * 1000);\n      });\n\n      setTimeout(() => {\n        // 智能分析完成，开始报表生成\n        setCurrentStreamStage('generate');\n\n        setTimeout(() => {\n          // 所有处理完成\n          setIsProcessing(false);\n          setCurrentStreamStage(null);\n        }, 8000); // 报表生成阶段时间\n      }, 6000); // 智能分析阶段时间\n    }, 4000); // 解析配置阶段时间\n  };\n\n  // 下载分析结果\n  const downloadAnalysisResult = (fileName: string, fileType: 'excel' | 'pdf' | 'json') => {\n    // 模拟下载文件\n    const link = document.createElement('a');\n    link.href = '#'; // 实际项目中这里应该是后端API地址\n    link.download = `${fileName}_analysis_result.${fileType}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    // 显示下载成功提示\n    console.log(`下载 ${fileName} 的${fileType.toUpperCase()}分析结果`);\n  };\n\n  // 下载所有分析结果\n  const downloadAllResults = () => {\n    const processedFiles = uploadedFiles.filter(f => f.status === 'processed');\n\n    processedFiles.forEach(file => {\n      downloadAnalysisResult(file.filename, 'excel');\n    });\n\n    // 下载完成后重置流程\n    setTimeout(() => {\n      resetFlow();\n      setUploadedFiles([]);\n    }, 1000);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'uploaded': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/30';\n      case 'validated': return 'text-tech-green bg-tech-green/10 border-tech-green/30';\n      case 'processing': return 'text-tech-cyan bg-tech-cyan/10 border-tech-cyan/30';\n      case 'processed': return 'text-tech-purple bg-tech-purple/10 border-tech-purple/30';\n      case 'error': return 'text-red-400 bg-red-400/10 border-red-400/30';\n      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/30';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'uploaded': return <Upload className=\"w-4 h-4\" />;\n      case 'validated': return <CheckCircle className=\"w-4 h-4\" />;\n      case 'processing': return <Settings className=\"w-4 h-4 animate-spin\" />;\n      case 'processed': return <FileText className=\"w-4 h-4\" />;\n      case 'error': return <AlertCircle className=\"w-4 h-4\" />;\n      default: return <FileText className=\"w-4 h-4\" />;\n    }\n  };\n\n  // 获取文件的流程进度信息\n  const getFileProgress = (fileId: string) => {\n    return processState.fileProgresses.find(fp => fp.fileId === fileId);\n  };\n\n  return (\n    <div className=\"space-y-6 h-full flex flex-col\">\n      {/* 文件上传区域 */}\n      <motion.div\n        className=\"tech-card p-6\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-xl font-bold text-white\">数据文件上传</h2>\n          <div className=\"flex items-center space-x-2\">\n            <Zap className=\"w-5 h-5 text-tech-cyan\" />\n            <span className=\"text-tech-cyan text-sm\">智能处理</span>\n          </div>\n        </div>\n\n        {/* 文件类型和月份选择 */}\n        <div className=\"grid grid-cols-2 gap-4 mb-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n              文件类型\n            </label>\n            <select\n              value={selectedFileType}\n              onChange={(e) => setSelectedFileType(e.target.value as 'household' | 'complaint')}\n              className=\"tech-input w-full\"\n            >\n              <option value=\"household\">入户数据</option>\n              <option value=\"complaint\">投诉数据</option>\n            </select>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n              月份\n            </label>\n            <input\n              type=\"month\"\n              value={selectedMonth}\n              onChange={(e) => setSelectedMonth(e.target.value)}\n              className=\"tech-input w-full\"\n            />\n          </div>\n        </div>\n\n        {/* 文件上传区域 */}\n        <div className=\"border-2 border-dashed border-gray-600 hover:border-tech-cyan/50 rounded-lg p-8 text-center transition-all duration-300\">\n          <div className=\"flex flex-col items-center space-y-4\">\n            <div className=\"p-4 bg-tech-cyan/20 rounded-full\">\n              <Upload className=\"w-8 h-8 text-tech-cyan\" />\n            </div>\n            <div>\n              <p className=\"text-lg font-medium text-white\">\n                点击上传文件\n              </p>\n              <p className=\"text-sm text-gray-400 mt-1\">\n                支持 .xlsx, .xls 格式文件\n              </p>\n            </div>\n            <input\n              type=\"file\"\n              multiple\n              accept=\".xlsx,.xls\"\n              onChange={handleFileUpload}\n              className=\"tech-button\"\n            />\n          </div>\n        </div>\n      </motion.div>\n\n      {/* 已上传文件列表 */}\n      <motion.div\n        className=\"tech-card p-6 flex flex-col\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.2 }}\n        style={{ height: '400px' }} // 固定高度\n      >\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-white\">\n            已上传文件 ({uploadedFiles.length})\n          </h3>\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={processAllFiles}\n              disabled={isProcessing || uploadedFiles.filter(f => f.status === 'validated').length === 0}\n              className=\"tech-button text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <Play className=\"w-4 h-4 mr-2\" />\n              一键处理全部\n            </button>\n          </div>\n        </div>\n\n        <div className=\"flex-1 overflow-y-auto space-y-3 pr-2\" style={{ maxHeight: '300px' }}>\n          <AnimatePresence>\n            {uploadedFiles.map((file) => (\n              <motion.div\n                key={file.id}\n                className=\"tech-border p-4 rounded-lg\"\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: 20 }}\n                layout\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3 flex-1\">\n                    <div className={`p-2 rounded-lg border ${getStatusColor(file.status)}`}>\n                      {getStatusIcon(file.status)}\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <h4 className=\"font-medium text-white truncate\">\n                        {file.filename}\n                      </h4>\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-400\">\n                        <span>{file.file_type === 'household' ? '入户数据' : '投诉数据'}</span>\n                        <span>{file.month}</span>\n                        <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(file.status)}`}>\n                          {file.status === 'uploaded' && '已上传'}\n                          {file.status === 'validated' && '已验证'}\n                          {file.status === 'processing' && '处理中'}\n                          {file.status === 'processed' && '已处理'}\n                          {file.status === 'error' && '错误'}\n                        </span>\n                      </div>\n\n                      {/* 文件级进度显示 */}\n                      {(() => {\n                        const fileProgress = getFileProgress(file.id);\n                        if (fileProgress && fileProgress.stepProgress > 0) {\n                          return (\n                            <div className=\"mt-2\">\n                              <div className=\"flex justify-between text-xs text-gray-400 mb-1\">\n                                <span>当前步骤: {fileProgress.currentStep}</span>\n                                <span>{fileProgress.stepProgress}%</span>\n                              </div>\n                              <div className=\"w-full bg-gray-700 rounded-full h-1\">\n                                <motion.div\n                                  className=\"bg-gradient-to-r from-blue-500 to-cyan-400 h-1 rounded-full\"\n                                  initial={{ width: 0 }}\n                                  animate={{ width: `${fileProgress.stepProgress}%` }}\n                                  transition={{ duration: 0.3 }}\n                                />\n                              </div>\n                            </div>\n                          );\n                        }\n                        return null;\n                      })()}\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2\">\n                    {file.status === 'uploaded' && (\n                      <button\n                        onClick={() => validateFile(file.id)}\n                        className=\"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\"\n                        title=\"验证文件\"\n                      >\n                        <CheckCircle className=\"w-4 h-4\" />\n                      </button>\n                    )}\n\n                    {file.status === 'validated' && (\n                      <button\n                        onClick={() => processFile(file.id)}\n                        className=\"p-2 text-tech-cyan hover:bg-tech-cyan/10 rounded-lg transition-colors\"\n                        title=\"智能处理\"\n                      >\n                        <Play className=\"w-4 h-4\" />\n                      </button>\n                    )}\n\n                    {file.status === 'processed' && (\n                      <>\n                        <button\n                          className=\"p-2 text-tech-purple hover:bg-tech-purple/10 rounded-lg transition-colors\"\n                          title=\"预览结果\"\n                        >\n                          <Eye className=\"w-4 h-4\" />\n                        </button>\n                        <button\n                          className=\"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\"\n                          title=\"下载结果\"\n                        >\n                          <Download className=\"w-4 h-4\" />\n                        </button>\n                      </>\n                    )}\n\n                    <button\n                      onClick={() => deleteFile(file.id)}\n                      className=\"p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors\"\n                      title=\"删除文件\"\n                    >\n                      <Trash2 className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                </div>\n\n                {file.status === 'processing' && (\n                  <motion.div\n                    className=\"mt-3\"\n                    initial={{ opacity: 0, height: 0 }}\n                    animate={{ opacity: 1, height: 'auto' }}\n                  >\n                    <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                      <motion.div\n                        className=\"progress-bar h-2 rounded-full\"\n                        initial={{ width: 0 }}\n                        animate={{ width: '100%' }}\n                        transition={{ duration: 3, ease: \"easeInOut\" }}\n                      />\n                    </div>\n                    <p className=\"text-xs text-tech-cyan mt-1\">正在进行智能分析...</p>\n                  </motion.div>\n                )}\n              </motion.div>\n            ))}\n          </AnimatePresence>\n\n          {uploadedFiles.length === 0 && (\n            <motion.div\n              className=\"text-center py-12 text-gray-400\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n            >\n              <FileText className=\"w-12 h-12 mx-auto mb-4 opacity-50\" />\n              <p>暂无上传文件</p>\n              <p className=\"text-sm mt-1\">请先上传数据文件</p>\n            </motion.div>\n          )}\n        </div>\n      </motion.div>\n\n      {/* 生成报表按钮 */}\n      {uploadedFiles.some(f => f.status === 'processed') && (\n        <motion.div\n          className=\"tech-card p-6\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n        >\n          <div className=\"text-center\">\n            <h3 className=\"text-lg font-semibold text-white mb-4\">\n              生成分析报表\n            </h3>\n            <div className=\"flex flex-col space-y-4\">\n              <button\n                className=\"tech-button text-lg px-8 py-3\"\n                onClick={onReportGeneration}\n              >\n                <FileText className=\"w-5 h-5 mr-2\" />\n                生成智能分析报表\n              </button>\n\n              {/* 分析结果下载区域 */}\n              {uploadedFiles.some(f => f.status === 'processed') && (\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"tech-card p-4 bg-green-900/20 border-green-500/30\"\n                >\n                  <h4 className=\"text-green-400 font-semibold mb-3 flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 mr-2\" />\n                    分析结果下载\n                  </h4>\n\n                  <div className=\"space-y-2\">\n                    {uploadedFiles.filter(f => f.status === 'processed').map(file => (\n                      <div key={file.id} className=\"flex items-center justify-between bg-gray-800/50 p-3 rounded-lg\">\n                        <span className=\"text-gray-300\">{file.filename}</span>\n                        <div className=\"flex space-x-2\">\n                          <button\n                            onClick={() => downloadAnalysisResult(file.filename, 'excel')}\n                            className=\"text-xs bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded transition-colors\"\n                          >\n                            <Download className=\"w-3 h-3 mr-1 inline\" />\n                            Excel\n                          </button>\n                          <button\n                            onClick={() => downloadAnalysisResult(file.filename, 'pdf')}\n                            className=\"text-xs bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded transition-colors\"\n                          >\n                            <Download className=\"w-3 h-3 mr-1 inline\" />\n                            PDF\n                          </button>\n                          <button\n                            onClick={() => downloadAnalysisResult(file.filename, 'json')}\n                            className=\"text-xs bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded transition-colors\"\n                          >\n                            <Download className=\"w-3 h-3 mr-1 inline\" />\n                            JSON\n                          </button>\n                        </div>\n                      </div>\n                    ))}\n\n                    <button\n                      onClick={downloadAllResults}\n                      className=\"w-full mt-4 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-2 px-4 rounded-lg transition-all duration-300 flex items-center justify-center\"\n                    >\n                      <Download className=\"w-4 h-4 mr-2\" />\n                      下载所有分析结果并重置\n                    </button>\n                  </div>\n                </motion.div>\n              )}\n            </div>\n          </div>\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\nexport default FileUploadSimple;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SACEC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,WAAW,EACXC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,GAAG,EACHC,GAAG,QACE,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAiBtB,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAA4B,WAAW,CAAC;EAChG,MAAM,CAAC2B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5B,QAAQ,CAA8C,IAAI,CAAC;EAC/G,MAAM6B,YAAY,GAAG5B,MAAM,CAAmB,IAAI,CAAC;EAEnD,MAAM;IAAE6B,cAAc;IAAEC,gBAAgB;IAAEC,gBAAgB;IAAEC,kBAAkB;IAAEC,SAAS;IAAEC;EAAa,CAAC,GAAG/B,qBAAqB,CAAC,CAAC;EAEnI,MAAMgC,gBAAgB,GAAIC,KAA0C,IAAK;IACvE,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,EAAE;MACTE,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,OAAO,CAACC,IAAI,IAAI;QAChC,MAAMC,OAAqB,GAAG;UAC5BC,EAAE,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;UAC3CC,QAAQ,EAAEP,IAAI,CAACQ,IAAI;UACnBC,SAAS,EAAE3B,gBAAgB;UAC3B4B,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;UAAE;UAC7CC,MAAM,EAAE,UAAU;UAClBC,WAAW,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACrCI,SAAS,EAAE,YAAYhB,IAAI,CAACQ,IAAI;QAClC,CAAC;QAED7B,gBAAgB,CAACsC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEhB,OAAO,CAAC,CAAC;;QAE5C;QACAd,cAAc,CAACc,OAAO,CAACC,EAAE,EAAED,OAAO,CAACM,QAAQ,CAAC;MAC9C,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMW,YAAY,GAAIC,MAAc,IAAK;IACvCxC,gBAAgB,CAACsC,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACpB,IAAI,IACXA,IAAI,CAACE,EAAE,KAAKiB,MAAM,GACd;MAAE,GAAGnB,IAAI;MAAEc,MAAM,EAAE;IAAY,CAAC,GAChCd,IACN,CACF,CAAC;;IAED;IACAZ,gBAAgB,CAAC+B,MAAM,CAAC;EAC1B,CAAC;EAED,MAAME,WAAW,GAAIF,MAAc,IAAK;IACtCxC,gBAAgB,CAACsC,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACpB,IAAI,IACXA,IAAI,CAACE,EAAE,KAAKiB,MAAM,GACd;MAAE,GAAGnB,IAAI;MAAEc,MAAM,EAAE;IAAa,CAAC,GACjCd,IACN,CACF,CAAC;;IAED;IACAX,gBAAgB,CAAC8B,MAAM,CAAC;IAExBG,UAAU,CAAC,MAAM;MACf3C,gBAAgB,CAACsC,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACpB,IAAI,IACXA,IAAI,CAACE,EAAE,KAAKiB,MAAM,GACd;QAAE,GAAGnB,IAAI;QAAEc,MAAM,EAAE;MAAY,CAAC,GAChCd,IACN,CACF,CAAC;IACH,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMuB,UAAU,GAAIJ,MAAc,IAAK;IACrCxC,gBAAgB,CAACsC,IAAI,IAAIA,IAAI,CAACO,MAAM,CAACxB,IAAI,IAAIA,IAAI,CAACE,EAAE,KAAKiB,MAAM,CAAC,CAAC;EACnE,CAAC;EAED,MAAMM,eAAe,GAAGA,CAAA,KAAM;IAC5B5C,eAAe,CAAC,IAAI,CAAC;IACrB,MAAM6C,cAAc,GAAGhD,aAAa,CAAC8C,MAAM,CAACxB,IAAI,IAAIA,IAAI,CAACc,MAAM,KAAK,WAAW,CAAC;;IAEhF;IACA7B,qBAAqB,CAAC,WAAW,CAAC;;IAElC;IACAqC,UAAU,CAAC,MAAM;MACf;MACArC,qBAAqB,CAAC,SAAS,CAAC;;MAEhC;MACAyC,cAAc,CAAC3B,OAAO,CAAC,CAACC,IAAI,EAAE2B,KAAK,KAAK;QACtCL,UAAU,CAAC,MAAM;UACfD,WAAW,CAACrB,IAAI,CAACE,EAAE,CAAC;QACtB,CAAC,EAAEyB,KAAK,GAAG,IAAI,CAAC;MAClB,CAAC,CAAC;MAEFL,UAAU,CAAC,MAAM;QACf;QACArC,qBAAqB,CAAC,UAAU,CAAC;QAEjCqC,UAAU,CAAC,MAAM;UACf;UACAzC,eAAe,CAAC,KAAK,CAAC;UACtBI,qBAAqB,CAAC,IAAI,CAAC;QAC7B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC;;EAED;EACA,MAAM2C,sBAAsB,GAAGA,CAACC,QAAgB,EAAEC,QAAkC,KAAK;IACvF;IACA,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,GAAG,CAAC,CAAC;IACjBH,IAAI,CAACI,QAAQ,GAAG,GAAGN,QAAQ,oBAAoBC,QAAQ,EAAE;IACzDE,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;;IAE/B;IACAS,OAAO,CAACC,GAAG,CAAC,MAAMZ,QAAQ,KAAKC,QAAQ,CAACY,WAAW,CAAC,CAAC,MAAM,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,cAAc,GAAGlE,aAAa,CAAC8C,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAAC/B,MAAM,KAAK,WAAW,CAAC;IAE1E8B,cAAc,CAAC7C,OAAO,CAACC,IAAI,IAAI;MAC7B4B,sBAAsB,CAAC5B,IAAI,CAACO,QAAQ,EAAE,OAAO,CAAC;IAChD,CAAC,CAAC;;IAEF;IACAe,UAAU,CAAC,MAAM;MACf/B,SAAS,CAAC,CAAC;MACXZ,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMmE,cAAc,GAAIhC,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,UAAU;QAAE,OAAO,uDAAuD;MAC/E,KAAK,WAAW;QAAE,OAAO,uDAAuD;MAChF,KAAK,YAAY;QAAE,OAAO,oDAAoD;MAC9E,KAAK,WAAW;QAAE,OAAO,0DAA0D;MACnF,KAAK,OAAO;QAAE,OAAO,8CAA8C;MACnE;QAAS,OAAO,iDAAiD;IACnE;EACF,CAAC;EAED,MAAMiC,aAAa,GAAIjC,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,UAAU;QAAE,oBAAOzC,OAAA,CAACX,MAAM;UAACsF,SAAS,EAAC;QAAS;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,WAAW;QAAE,oBAAO9E,OAAA,CAACT,WAAW;UAACoF,SAAS,EAAC;QAAS;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5D,KAAK,YAAY;QAAE,oBAAO9E,OAAA,CAACP,QAAQ;UAACkF,SAAS,EAAC;QAAsB;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvE,KAAK,WAAW;QAAE,oBAAO9E,OAAA,CAACV,QAAQ;UAACqF,SAAS,EAAC;QAAS;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD,KAAK,OAAO;QAAE,oBAAO9E,OAAA,CAACR,WAAW;UAACmF,SAAS,EAAC;QAAS;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxD;QAAS,oBAAO9E,OAAA,CAACV,QAAQ;UAACqF,SAAS,EAAC;QAAS;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIjC,MAAc,IAAK;IAC1C,OAAO3B,YAAY,CAAC6D,cAAc,CAACC,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACpC,MAAM,KAAKA,MAAM,CAAC;EACrE,CAAC;EAED,oBACE9C,OAAA;IAAK2E,SAAS,EAAC,gCAAgC;IAAAQ,QAAA,gBAE7CnF,OAAA,CAACd,MAAM,CAACkG,GAAG;MACTT,SAAS,EAAC,eAAe;MACzBU,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBAE9BnF,OAAA;QAAK2E,SAAS,EAAC,wCAAwC;QAAAQ,QAAA,gBACrDnF,OAAA;UAAI2E,SAAS,EAAC,8BAA8B;UAAAQ,QAAA,EAAC;QAAM;UAAA3B,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxD9E,OAAA;UAAK2E,SAAS,EAAC,6BAA6B;UAAAQ,QAAA,gBAC1CnF,OAAA,CAACF,GAAG;YAAC6E,SAAS,EAAC;UAAwB;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1C9E,OAAA;YAAM2E,SAAS,EAAC,wBAAwB;YAAAQ,QAAA,EAAC;UAAI;YAAA3B,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9E,OAAA;QAAK2E,SAAS,EAAC,6BAA6B;QAAAQ,QAAA,gBAC1CnF,OAAA;UAAAmF,QAAA,gBACEnF,OAAA;YAAO2E,SAAS,EAAC,8CAA8C;YAAAQ,QAAA,EAAC;UAEhE;YAAA3B,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9E,OAAA;YACEyF,KAAK,EAAEhF,gBAAiB;YACxBiF,QAAQ,EAAGC,CAAC,IAAKjF,mBAAmB,CAACiF,CAAC,CAACpE,MAAM,CAACkE,KAAkC,CAAE;YAClFd,SAAS,EAAC,mBAAmB;YAAAQ,QAAA,gBAE7BnF,OAAA;cAAQyF,KAAK,EAAC,WAAW;cAAAN,QAAA,EAAC;YAAI;cAAA3B,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvC9E,OAAA;cAAQyF,KAAK,EAAC,WAAW;cAAAN,QAAA,EAAC;YAAI;cAAA3B,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN9E,OAAA;UAAAmF,QAAA,gBACEnF,OAAA;YAAO2E,SAAS,EAAC,8CAA8C;YAAAQ,QAAA,EAAC;UAEhE;YAAA3B,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9E,OAAA;YACE4F,IAAI,EAAC,OAAO;YACZH,KAAK,EAAEI,aAAc;YACrBH,QAAQ,EAAGC,CAAC,IAAKG,gBAAgB,CAACH,CAAC,CAACpE,MAAM,CAACkE,KAAK,CAAE;YAClDd,SAAS,EAAC;UAAmB;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9E,OAAA;QAAK2E,SAAS,EAAC,yHAAyH;QAAAQ,QAAA,eACtInF,OAAA;UAAK2E,SAAS,EAAC,sCAAsC;UAAAQ,QAAA,gBACnDnF,OAAA;YAAK2E,SAAS,EAAC,kCAAkC;YAAAQ,QAAA,eAC/CnF,OAAA,CAACX,MAAM;cAACsF,SAAS,EAAC;YAAwB;cAAAnB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACN9E,OAAA;YAAAmF,QAAA,gBACEnF,OAAA;cAAG2E,SAAS,EAAC,gCAAgC;cAAAQ,QAAA,EAAC;YAE9C;cAAA3B,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ9E,OAAA;cAAG2E,SAAS,EAAC,4BAA4B;cAAAQ,QAAA,EAAC;YAE1C;cAAA3B,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN9E,OAAA;YACE4F,IAAI,EAAC,MAAM;YACXG,QAAQ;YACRC,MAAM,EAAC,YAAY;YACnBN,QAAQ,EAAEtE,gBAAiB;YAC3BuD,SAAS,EAAC;UAAa;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAtB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGb9E,OAAA,CAACd,MAAM,CAACkG,GAAG;MACTT,SAAS,EAAC,6BAA6B;MACvCU,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BU,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BC,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE,CAAC;MAAA;MAAAjB,QAAA,gBAE5BnF,OAAA;QAAK2E,SAAS,EAAC,wCAAwC;QAAAQ,QAAA,gBACrDnF,OAAA;UAAI2E,SAAS,EAAC,kCAAkC;UAAAQ,QAAA,GAAC,kCACxC,EAAC9E,aAAa,CAACgG,MAAM,EAAC,GAC/B;QAAA;UAAA7C,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL9E,OAAA;UAAK2E,SAAS,EAAC,gBAAgB;UAAAQ,QAAA,eAC7BnF,OAAA;YACEsG,OAAO,EAAElD,eAAgB;YACzBmD,QAAQ,EAAEhG,YAAY,IAAIF,aAAa,CAAC8C,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAAC/B,MAAM,KAAK,WAAW,CAAC,CAAC4D,MAAM,KAAK,CAAE;YAC3F1B,SAAS,EAAC,qEAAqE;YAAAQ,QAAA,gBAE/EnF,OAAA,CAACN,IAAI;cAACiF,SAAS,EAAC;YAAc;cAAAnB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wCAEnC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9E,OAAA;QAAK2E,SAAS,EAAC,uCAAuC;QAACwB,KAAK,EAAE;UAAEK,SAAS,EAAE;QAAQ,CAAE;QAAArB,QAAA,gBACnFnF,OAAA,CAACb,eAAe;UAAAgG,QAAA,EACb9E,aAAa,CAAC0C,GAAG,CAAEpB,IAAI,iBACtB3B,OAAA,CAACd,MAAM,CAACkG,GAAG;YAETT,SAAS,EAAC,4BAA4B;YACtCU,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEmB,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCjB,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEmB,CAAC,EAAE;YAAE,CAAE;YAC9BC,IAAI,EAAE;cAAEpB,OAAO,EAAE,CAAC;cAAEmB,CAAC,EAAE;YAAG,CAAE;YAC5BE,MAAM;YAAAxB,QAAA,gBAENnF,OAAA;cAAK2E,SAAS,EAAC,mCAAmC;cAAAQ,QAAA,gBAChDnF,OAAA;gBAAK2E,SAAS,EAAC,oCAAoC;gBAAAQ,QAAA,gBACjDnF,OAAA;kBAAK2E,SAAS,EAAE,yBAAyBF,cAAc,CAAC9C,IAAI,CAACc,MAAM,CAAC,EAAG;kBAAA0C,QAAA,EACpET,aAAa,CAAC/C,IAAI,CAACc,MAAM;gBAAC;kBAAAe,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACN9E,OAAA;kBAAK2E,SAAS,EAAC,gBAAgB;kBAAAQ,QAAA,gBAC7BnF,OAAA;oBAAI2E,SAAS,EAAC,iCAAiC;oBAAAQ,QAAA,EAC5CxD,IAAI,CAACO;kBAAQ;oBAAAsB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACL9E,OAAA;oBAAK2E,SAAS,EAAC,mDAAmD;oBAAAQ,QAAA,gBAChEnF,OAAA;sBAAAmF,QAAA,EAAOxD,IAAI,CAACS,SAAS,KAAK,WAAW,GAAG,MAAM,GAAG;oBAAM;sBAAAoB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC/D9E,OAAA;sBAAAmF,QAAA,EAAOxD,IAAI,CAACU;oBAAK;sBAAAmB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzB9E,OAAA;sBAAM2E,SAAS,EAAE,kCAAkCF,cAAc,CAAC9C,IAAI,CAACc,MAAM,CAAC,EAAG;sBAAA0C,QAAA,GAC9ExD,IAAI,CAACc,MAAM,KAAK,UAAU,IAAI,KAAK,EACnCd,IAAI,CAACc,MAAM,KAAK,WAAW,IAAI,KAAK,EACpCd,IAAI,CAACc,MAAM,KAAK,YAAY,IAAI,KAAK,EACrCd,IAAI,CAACc,MAAM,KAAK,WAAW,IAAI,KAAK,EACpCd,IAAI,CAACc,MAAM,KAAK,OAAO,IAAI,IAAI;oBAAA;sBAAAe,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EAGL,CAAC,MAAM;oBACN,MAAM8B,YAAY,GAAG7B,eAAe,CAACpD,IAAI,CAACE,EAAE,CAAC;oBAC7C,IAAI+E,YAAY,IAAIA,YAAY,CAACC,YAAY,GAAG,CAAC,EAAE;sBACjD,oBACE7G,OAAA;wBAAK2E,SAAS,EAAC,MAAM;wBAAAQ,QAAA,gBACnBnF,OAAA;0BAAK2E,SAAS,EAAC,iDAAiD;0BAAAQ,QAAA,gBAC9DnF,OAAA;4BAAAmF,QAAA,GAAM,4BAAM,EAACyB,YAAY,CAACE,WAAW;0BAAA;4BAAAtD,QAAA,EAAAoB,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eAC7C9E,OAAA;4BAAAmF,QAAA,GAAOyB,YAAY,CAACC,YAAY,EAAC,GAAC;0BAAA;4BAAArD,QAAA,EAAAoB,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAtB,QAAA,EAAAoB,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC,eACN9E,OAAA;0BAAK2E,SAAS,EAAC,qCAAqC;0BAAAQ,QAAA,eAClDnF,OAAA,CAACd,MAAM,CAACkG,GAAG;4BACTT,SAAS,EAAC,6DAA6D;4BACvEU,OAAO,EAAE;8BAAE0B,KAAK,EAAE;4BAAE,CAAE;4BACtBvB,OAAO,EAAE;8BAAEuB,KAAK,EAAE,GAAGH,YAAY,CAACC,YAAY;4BAAI,CAAE;4BACpDZ,UAAU,EAAE;8BAAEe,QAAQ,EAAE;4BAAI;0BAAE;4BAAAxD,QAAA,EAAAoB,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/B;wBAAC;0BAAAtB,QAAA,EAAAoB,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAtB,QAAA,EAAAoB,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAEV;oBACA,OAAO,IAAI;kBACb,CAAC,EAAE,CAAC;gBAAA;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAtB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9E,OAAA;gBAAK2E,SAAS,EAAC,6BAA6B;gBAAAQ,QAAA,GACzCxD,IAAI,CAACc,MAAM,KAAK,UAAU,iBACzBzC,OAAA;kBACEsG,OAAO,EAAEA,CAAA,KAAMzD,YAAY,CAAClB,IAAI,CAACE,EAAE,CAAE;kBACrC8C,SAAS,EAAC,yEAAyE;kBACnFsC,KAAK,EAAC,0BAAM;kBAAA9B,QAAA,eAEZnF,OAAA,CAACT,WAAW;oBAACoF,SAAS,EAAC;kBAAS;oBAAAnB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CACT,EAEAnD,IAAI,CAACc,MAAM,KAAK,WAAW,iBAC1BzC,OAAA;kBACEsG,OAAO,EAAEA,CAAA,KAAMtD,WAAW,CAACrB,IAAI,CAACE,EAAE,CAAE;kBACpC8C,SAAS,EAAC,uEAAuE;kBACjFsC,KAAK,EAAC,0BAAM;kBAAA9B,QAAA,eAEZnF,OAAA,CAACN,IAAI;oBAACiF,SAAS,EAAC;kBAAS;oBAAAnB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CACT,EAEAnD,IAAI,CAACc,MAAM,KAAK,WAAW,iBAC1BzC,OAAA,CAAAE,SAAA;kBAAAiF,QAAA,gBACEnF,OAAA;oBACE2E,SAAS,EAAC,2EAA2E;oBACrFsC,KAAK,EAAC,0BAAM;oBAAA9B,QAAA,eAEZnF,OAAA,CAACH,GAAG;sBAAC8E,SAAS,EAAC;oBAAS;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACT9E,OAAA;oBACE2E,SAAS,EAAC,yEAAyE;oBACnFsC,KAAK,EAAC,0BAAM;oBAAA9B,QAAA,eAEZnF,OAAA,CAACL,QAAQ;sBAACgF,SAAS,EAAC;oBAAS;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA,eACT,CACH,eAED9E,OAAA;kBACEsG,OAAO,EAAEA,CAAA,KAAMpD,UAAU,CAACvB,IAAI,CAACE,EAAE,CAAE;kBACnC8C,SAAS,EAAC,mEAAmE;kBAC7EsC,KAAK,EAAC,0BAAM;kBAAA9B,QAAA,eAEZnF,OAAA,CAACJ,MAAM;oBAAC+E,SAAS,EAAC;kBAAS;oBAAAnB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAtB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELnD,IAAI,CAACc,MAAM,KAAK,YAAY,iBAC3BzC,OAAA,CAACd,MAAM,CAACkG,GAAG;cACTT,SAAS,EAAC,MAAM;cAChBU,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEc,MAAM,EAAE;cAAE,CAAE;cACnCZ,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEc,MAAM,EAAE;cAAO,CAAE;cAAAjB,QAAA,gBAExCnF,OAAA;gBAAK2E,SAAS,EAAC,qCAAqC;gBAAAQ,QAAA,eAClDnF,OAAA,CAACd,MAAM,CAACkG,GAAG;kBACTT,SAAS,EAAC,+BAA+B;kBACzCU,OAAO,EAAE;oBAAE0B,KAAK,EAAE;kBAAE,CAAE;kBACtBvB,OAAO,EAAE;oBAAEuB,KAAK,EAAE;kBAAO,CAAE;kBAC3Bd,UAAU,EAAE;oBAAEe,QAAQ,EAAE,CAAC;oBAAEE,IAAI,EAAE;kBAAY;gBAAE;kBAAA1D,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAtB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9E,OAAA;gBAAG2E,SAAS,EAAC,6BAA6B;gBAAAQ,QAAA,EAAC;cAAW;gBAAA3B,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CACb;UAAA,GAtHInD,IAAI,CAACE,EAAE;YAAA2B,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuHF,CACb;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa,CAAC,EAEjBzE,aAAa,CAACgG,MAAM,KAAK,CAAC,iBACzBrG,OAAA,CAACd,MAAM,CAACkG,GAAG;UACTT,SAAS,EAAC,iCAAiC;UAC3CU,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UAAAH,QAAA,gBAExBnF,OAAA,CAACV,QAAQ;YAACqF,SAAS,EAAC;UAAmC;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1D9E,OAAA;YAAAmF,QAAA,EAAG;UAAM;YAAA3B,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACb9E,OAAA;YAAG2E,SAAS,EAAC,cAAc;YAAAQ,QAAA,EAAC;UAAQ;YAAA3B,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CACb;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAtB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGZzE,aAAa,CAAC8G,IAAI,CAAC3C,CAAC,IAAIA,CAAC,CAAC/B,MAAM,KAAK,WAAW,CAAC,iBAChDzC,OAAA,CAACd,MAAM,CAACkG,GAAG;MACTT,SAAS,EAAC,eAAe;MACzBU,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BU,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAf,QAAA,eAE3BnF,OAAA;QAAK2E,SAAS,EAAC,aAAa;QAAAQ,QAAA,gBAC1BnF,OAAA;UAAI2E,SAAS,EAAC,uCAAuC;UAAAQ,QAAA,EAAC;QAEtD;UAAA3B,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL9E,OAAA;UAAK2E,SAAS,EAAC,yBAAyB;UAAAQ,QAAA,gBACtCnF,OAAA;YACE2E,SAAS,EAAC,+BAA+B;YACzC2B,OAAO,EAAErF,kBAAmB;YAAAkE,QAAA,gBAE5BnF,OAAA,CAACV,QAAQ;cAACqF,SAAS,EAAC;YAAc;cAAAnB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oDAEvC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAGRzE,aAAa,CAAC8G,IAAI,CAAC3C,CAAC,IAAIA,CAAC,CAAC/B,MAAM,KAAK,WAAW,CAAC,iBAChDzC,OAAA,CAACd,MAAM,CAACkG,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BZ,SAAS,EAAC,mDAAmD;YAAAQ,QAAA,gBAE7DnF,OAAA;cAAI2E,SAAS,EAAC,qDAAqD;cAAAQ,QAAA,gBACjEnF,OAAA,CAACT,WAAW;gBAACoF,SAAS,EAAC;cAAc;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wCAE1C;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEL9E,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAQ,QAAA,GACvB9E,aAAa,CAAC8C,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAAC/B,MAAM,KAAK,WAAW,CAAC,CAACM,GAAG,CAACpB,IAAI,iBAC3D3B,OAAA;gBAAmB2E,SAAS,EAAC,iEAAiE;gBAAAQ,QAAA,gBAC5FnF,OAAA;kBAAM2E,SAAS,EAAC,eAAe;kBAAAQ,QAAA,EAAExD,IAAI,CAACO;gBAAQ;kBAAAsB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtD9E,OAAA;kBAAK2E,SAAS,EAAC,gBAAgB;kBAAAQ,QAAA,gBAC7BnF,OAAA;oBACEsG,OAAO,EAAEA,CAAA,KAAM/C,sBAAsB,CAAC5B,IAAI,CAACO,QAAQ,EAAE,OAAO,CAAE;oBAC9DyC,SAAS,EAAC,wFAAwF;oBAAAQ,QAAA,gBAElGnF,OAAA,CAACL,QAAQ;sBAACgF,SAAS,EAAC;oBAAqB;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,SAE9C;kBAAA;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT9E,OAAA;oBACEsG,OAAO,EAAEA,CAAA,KAAM/C,sBAAsB,CAAC5B,IAAI,CAACO,QAAQ,EAAE,KAAK,CAAE;oBAC5DyC,SAAS,EAAC,sFAAsF;oBAAAQ,QAAA,gBAEhGnF,OAAA,CAACL,QAAQ;sBAACgF,SAAS,EAAC;oBAAqB;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,OAE9C;kBAAA;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT9E,OAAA;oBACEsG,OAAO,EAAEA,CAAA,KAAM/C,sBAAsB,CAAC5B,IAAI,CAACO,QAAQ,EAAE,MAAM,CAAE;oBAC7DyC,SAAS,EAAC,0FAA0F;oBAAAQ,QAAA,gBAEpGnF,OAAA,CAACL,QAAQ;sBAACgF,SAAS,EAAC;oBAAqB;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,QAE9C;kBAAA;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GAxBEnD,IAAI,CAACE,EAAE;gBAAA2B,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyBZ,CACN,CAAC,eAEF9E,OAAA;gBACEsG,OAAO,EAAEhC,kBAAmB;gBAC5BK,SAAS,EAAC,6LAA6L;gBAAAQ,QAAA,gBAEvMnF,OAAA,CAACL,QAAQ;kBAACgF,SAAS,EAAC;gBAAc;kBAAAnB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sEAEvC;cAAA;gBAAAtB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAtB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb;EAAA;IAAAtB,QAAA,EAAAoB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1E,EAAA,CAxdID,gBAA0B;EAAA,QAO8Ef,qBAAqB;AAAA;AAAAgI,EAAA,GAP7HjH,gBAA0B;AA0dhC,eAAeA,gBAAgB;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}