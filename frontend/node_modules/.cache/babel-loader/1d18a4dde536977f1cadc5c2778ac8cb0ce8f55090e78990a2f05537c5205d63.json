{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\components\\\\StreamOutput.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Terminal, Copy, Download, Pause, Play, RotateCcw } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const StreamOutput = ({\n  content,\n  isActive,\n  title,\n  onComplete,\n  speed = 150\n}) => {\n  _s();\n  const [displayedLines, setDisplayedLines] = useState([]);\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isCompleted, setIsCompleted] = useState(false);\n  const intervalRef = useRef(null);\n  const containerRef = useRef(null);\n\n  // 自动滚动到底部\n  const scrollToBottom = () => {\n    if (containerRef.current) {\n      containerRef.current.scrollTop = containerRef.current.scrollHeight;\n    }\n  };\n\n  // 开始流式输出\n  const startStreaming = () => {\n    if (currentIndex >= content.length) return;\n    setIsPlaying(true);\n    intervalRef.current = setInterval(() => {\n      setCurrentIndex(prev => {\n        const nextIndex = prev + 1;\n        if (nextIndex >= content.length) {\n          setIsPlaying(false);\n          setIsCompleted(true);\n          onComplete === null || onComplete === void 0 ? void 0 : onComplete();\n          return prev;\n        }\n        return nextIndex;\n      });\n    }, speed);\n  };\n\n  // 暂停流式输出\n  const pauseStreaming = () => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n    setIsPlaying(false);\n  };\n\n  // 重置流式输出\n  const resetStreaming = () => {\n    pauseStreaming();\n    setCurrentIndex(0);\n    setDisplayedLines([]);\n    setIsCompleted(false);\n  };\n\n  // 复制内容\n  const copyContent = () => {\n    const textContent = displayedLines.join('\\n');\n    navigator.clipboard.writeText(textContent);\n  };\n\n  // 下载内容\n  const downloadContent = () => {\n    const textContent = displayedLines.join('\\n');\n    const blob = new Blob([textContent], {\n      type: 'text/plain'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${title}_output.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  // 监听激活状态\n  useEffect(() => {\n    if (isActive && !isPlaying && !isCompleted) {\n      startStreaming();\n    }\n  }, [isActive]);\n\n  // 更新显示的行\n  useEffect(() => {\n    setDisplayedLines(content.slice(0, currentIndex));\n    scrollToBottom();\n  }, [currentIndex, content]);\n\n  // 清理定时器\n  useEffect(() => {\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, []);\n\n  // 格式化文本行\n  const formatLine = (line, index) => {\n    // 处理Markdown样式\n    if (line.startsWith('**') && line.endsWith('**')) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"font-bold text-tech-green my-2\",\n        children: line.slice(2, -2)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this);\n    }\n    if (line.startsWith('# ')) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xl font-bold text-white my-3\",\n        children: line.slice(2)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this);\n    }\n    if (line.startsWith('```')) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 border border-gray-600 rounded px-2 py-1 my-1 font-mono text-sm\",\n        children: line === '```' ? '' : line.slice(3)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this);\n    }\n    if (line.startsWith('- ') || line.startsWith('• ')) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-300 ml-4 my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-tech-blue mr-2\",\n          children: \"\\u2022\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), line.slice(2)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this);\n    }\n    if (line.includes('✅') || line.includes('✓')) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-green-400 my-1\",\n        children: line\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this);\n    }\n    if (line.includes('⚠️') || line.includes('🔴')) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-yellow-400 my-1\",\n        children: line\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this);\n    }\n    if (line.includes('🔧') || line.includes('🎯') || line.includes('📊')) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-tech-blue my-1\",\n        children: line\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-gray-300 my-1\",\n      children: [line || '\\u00A0', \" \"]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"tech-card h-full flex flex-col\",\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    transition: {\n      duration: 0.3\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between p-4 border-b border-gray-700\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(Terminal, {\n          className: \"w-5 h-5 text-tech-green\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-semibold text-white\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), isPlaying && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-green-400\",\n            children: \"\\u8F93\\u51FA\\u4E2D...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this), isCompleted && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-blue-400\",\n            children: \"\\u5DF2\\u5B8C\\u6210\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [isPlaying ? /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: pauseStreaming,\n          className: \"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors\",\n          title: \"\\u6682\\u505C\",\n          children: /*#__PURE__*/_jsxDEV(Pause, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: startStreaming,\n          disabled: isCompleted,\n          className: \"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors disabled:opacity-50\",\n          title: \"\\u64AD\\u653E\",\n          children: /*#__PURE__*/_jsxDEV(Play, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: resetStreaming,\n          className: \"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors\",\n          title: \"\\u91CD\\u7F6E\",\n          children: /*#__PURE__*/_jsxDEV(RotateCcw, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: copyContent,\n          disabled: displayedLines.length === 0,\n          className: \"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors disabled:opacity-50\",\n          title: \"\\u590D\\u5236\",\n          children: /*#__PURE__*/_jsxDEV(Copy, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: downloadContent,\n          disabled: displayedLines.length === 0,\n          className: \"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors disabled:opacity-50\",\n          title: \"\\u4E0B\\u8F7D\",\n          children: /*#__PURE__*/_jsxDEV(Download, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      className: \"flex-1 p-4 overflow-y-auto bg-gray-900/50 font-mono text-sm leading-relaxed\",\n      style: {\n        maxHeight: '400px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: displayedLines.map((line, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -10\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.2\n          },\n          children: formatLine(line, index)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), isPlaying && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"inline-block w-2 h-4 bg-tech-green ml-1\",\n        animate: {\n          opacity: [1, 0]\n        },\n        transition: {\n          duration: 0.8,\n          repeat: Infinity\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-4 py-2 border-t border-gray-700 text-xs text-gray-400\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\u884C\\u6570: \", displayedLines.length, \" / \", content.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\u8FDB\\u5EA6: \", Math.round(currentIndex / content.length * 100), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this);\n};\n_s(StreamOutput, \"iZTFdJ3YWdvFFX9bjrc9810PuI4=\");\n_c = StreamOutput;\nvar _c;\n$RefreshReg$(_c, \"StreamOutput\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "Terminal", "Copy", "Download", "Pause", "Play", "RotateCcw", "jsxDEV", "_jsxDEV", "StreamOutput", "content", "isActive", "title", "onComplete", "speed", "_s", "displayedLines", "setDisplayedLines", "currentIndex", "setCurrentIndex", "isPlaying", "setIsPlaying", "isCompleted", "setIsCompleted", "intervalRef", "containerRef", "scrollToBottom", "current", "scrollTop", "scrollHeight", "startStreaming", "length", "setInterval", "prev", "nextIndex", "pauseStreaming", "clearInterval", "resetStreaming", "copyContent", "textContent", "join", "navigator", "clipboard", "writeText", "downloadContent", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "slice", "formatLine", "line", "index", "startsWith", "endsWith", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "includes", "div", "initial", "opacity", "y", "animate", "transition", "duration", "onClick", "disabled", "ref", "style", "maxHeight", "map", "x", "repeat", "Infinity", "Math", "round", "_c", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/components/StreamOutput.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Terminal, Copy, Download, Pause, Play, RotateCcw } from 'lucide-react';\n\ninterface StreamOutputProps {\n  content: string[];\n  isActive: boolean;\n  title: string;\n  onComplete?: () => void;\n  speed?: number; // 每行显示间隔时间（毫秒）\n}\n\nexport const StreamOutput: React.FC<StreamOutputProps> = ({\n  content,\n  isActive,\n  title,\n  onComplete,\n  speed = 150\n}) => {\n  const [displayedLines, setDisplayedLines] = useState<string[]>([]);\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isCompleted, setIsCompleted] = useState(false);\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  // 自动滚动到底部\n  const scrollToBottom = () => {\n    if (containerRef.current) {\n      containerRef.current.scrollTop = containerRef.current.scrollHeight;\n    }\n  };\n\n  // 开始流式输出\n  const startStreaming = () => {\n    if (currentIndex >= content.length) return;\n    \n    setIsPlaying(true);\n    intervalRef.current = setInterval(() => {\n      setCurrentIndex(prev => {\n        const nextIndex = prev + 1;\n        if (nextIndex >= content.length) {\n          setIsPlaying(false);\n          setIsCompleted(true);\n          onComplete?.();\n          return prev;\n        }\n        return nextIndex;\n      });\n    }, speed);\n  };\n\n  // 暂停流式输出\n  const pauseStreaming = () => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n    setIsPlaying(false);\n  };\n\n  // 重置流式输出\n  const resetStreaming = () => {\n    pauseStreaming();\n    setCurrentIndex(0);\n    setDisplayedLines([]);\n    setIsCompleted(false);\n  };\n\n  // 复制内容\n  const copyContent = () => {\n    const textContent = displayedLines.join('\\n');\n    navigator.clipboard.writeText(textContent);\n  };\n\n  // 下载内容\n  const downloadContent = () => {\n    const textContent = displayedLines.join('\\n');\n    const blob = new Blob([textContent], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${title}_output.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  // 监听激活状态\n  useEffect(() => {\n    if (isActive && !isPlaying && !isCompleted) {\n      startStreaming();\n    }\n  }, [isActive]);\n\n  // 更新显示的行\n  useEffect(() => {\n    setDisplayedLines(content.slice(0, currentIndex));\n    scrollToBottom();\n  }, [currentIndex, content]);\n\n  // 清理定时器\n  useEffect(() => {\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, []);\n\n  // 格式化文本行\n  const formatLine = (line: string, index: number) => {\n    // 处理Markdown样式\n    if (line.startsWith('**') && line.endsWith('**')) {\n      return (\n        <div key={index} className=\"font-bold text-tech-green my-2\">\n          {line.slice(2, -2)}\n        </div>\n      );\n    }\n    \n    if (line.startsWith('# ')) {\n      return (\n        <div key={index} className=\"text-xl font-bold text-white my-3\">\n          {line.slice(2)}\n        </div>\n      );\n    }\n    \n    if (line.startsWith('```')) {\n      return (\n        <div key={index} className=\"bg-gray-800 border border-gray-600 rounded px-2 py-1 my-1 font-mono text-sm\">\n          {line === '```' ? '' : line.slice(3)}\n        </div>\n      );\n    }\n    \n    if (line.startsWith('- ') || line.startsWith('• ')) {\n      return (\n        <div key={index} className=\"text-gray-300 ml-4 my-1\">\n          <span className=\"text-tech-blue mr-2\">•</span>\n          {line.slice(2)}\n        </div>\n      );\n    }\n    \n    if (line.includes('✅') || line.includes('✓')) {\n      return (\n        <div key={index} className=\"text-green-400 my-1\">\n          {line}\n        </div>\n      );\n    }\n    \n    if (line.includes('⚠️') || line.includes('🔴')) {\n      return (\n        <div key={index} className=\"text-yellow-400 my-1\">\n          {line}\n        </div>\n      );\n    }\n    \n    if (line.includes('🔧') || line.includes('🎯') || line.includes('📊')) {\n      return (\n        <div key={index} className=\"text-tech-blue my-1\">\n          {line}\n        </div>\n      );\n    }\n    \n    return (\n      <div key={index} className=\"text-gray-300 my-1\">\n        {line || '\\u00A0'} {/* 空行显示为不间断空格 */}\n      </div>\n    );\n  };\n\n  return (\n    <motion.div\n      className=\"tech-card h-full flex flex-col\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n    >\n      {/* 头部控制栏 */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-700\">\n        <div className=\"flex items-center space-x-3\">\n          <Terminal className=\"w-5 h-5 text-tech-green\" />\n          <h3 className=\"font-semibold text-white\">{title}</h3>\n          {isPlaying && (\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n              <span className=\"text-xs text-green-400\">输出中...</span>\n            </div>\n          )}\n          {isCompleted && (\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\n              <span className=\"text-xs text-blue-400\">已完成</span>\n            </div>\n          )}\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          {isPlaying ? (\n            <button\n              onClick={pauseStreaming}\n              className=\"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors\"\n              title=\"暂停\"\n            >\n              <Pause className=\"w-4 h-4\" />\n            </button>\n          ) : (\n            <button\n              onClick={startStreaming}\n              disabled={isCompleted}\n              className=\"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors disabled:opacity-50\"\n              title=\"播放\"\n            >\n              <Play className=\"w-4 h-4\" />\n            </button>\n          )}\n          \n          <button\n            onClick={resetStreaming}\n            className=\"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors\"\n            title=\"重置\"\n          >\n            <RotateCcw className=\"w-4 h-4\" />\n          </button>\n          \n          <button\n            onClick={copyContent}\n            disabled={displayedLines.length === 0}\n            className=\"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors disabled:opacity-50\"\n            title=\"复制\"\n          >\n            <Copy className=\"w-4 h-4\" />\n          </button>\n          \n          <button\n            onClick={downloadContent}\n            disabled={displayedLines.length === 0}\n            className=\"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors disabled:opacity-50\"\n            title=\"下载\"\n          >\n            <Download className=\"w-4 h-4\" />\n          </button>\n        </div>\n      </div>\n\n      {/* 输出内容区域 */}\n      <div\n        ref={containerRef}\n        className=\"flex-1 p-4 overflow-y-auto bg-gray-900/50 font-mono text-sm leading-relaxed\"\n        style={{ maxHeight: '400px' }}\n      >\n        <AnimatePresence>\n          {displayedLines.map((line, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, x: -10 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.2 }}\n            >\n              {formatLine(line, index)}\n            </motion.div>\n          ))}\n        </AnimatePresence>\n        \n        {/* 光标效果 */}\n        {isPlaying && (\n          <motion.div\n            className=\"inline-block w-2 h-4 bg-tech-green ml-1\"\n            animate={{ opacity: [1, 0] }}\n            transition={{ duration: 0.8, repeat: Infinity }}\n          />\n        )}\n      </div>\n\n      {/* 底部状态栏 */}\n      <div className=\"px-4 py-2 border-t border-gray-700 text-xs text-gray-400\">\n        <div className=\"flex justify-between items-center\">\n          <span>\n            行数: {displayedLines.length} / {content.length}\n          </span>\n          <span>\n            进度: {Math.round((currentIndex / content.length) * 100)}%\n          </span>\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAEC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUhF,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EACxDC,OAAO;EACPC,QAAQ;EACRC,KAAK;EACLC,UAAU;EACVC,KAAK,GAAG;AACV,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM4B,WAAW,GAAG1B,MAAM,CAAwB,IAAI,CAAC;EACvD,MAAM2B,YAAY,GAAG3B,MAAM,CAAiB,IAAI,CAAC;;EAEjD;EACA,MAAM4B,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAID,YAAY,CAACE,OAAO,EAAE;MACxBF,YAAY,CAACE,OAAO,CAACC,SAAS,GAAGH,YAAY,CAACE,OAAO,CAACE,YAAY;IACpE;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIZ,YAAY,IAAIR,OAAO,CAACqB,MAAM,EAAE;IAEpCV,YAAY,CAAC,IAAI,CAAC;IAClBG,WAAW,CAACG,OAAO,GAAGK,WAAW,CAAC,MAAM;MACtCb,eAAe,CAACc,IAAI,IAAI;QACtB,MAAMC,SAAS,GAAGD,IAAI,GAAG,CAAC;QAC1B,IAAIC,SAAS,IAAIxB,OAAO,CAACqB,MAAM,EAAE;UAC/BV,YAAY,CAAC,KAAK,CAAC;UACnBE,cAAc,CAAC,IAAI,CAAC;UACpBV,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAG,CAAC;UACd,OAAOoB,IAAI;QACb;QACA,OAAOC,SAAS;MAClB,CAAC,CAAC;IACJ,CAAC,EAAEpB,KAAK,CAAC;EACX,CAAC;;EAED;EACA,MAAMqB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIX,WAAW,CAACG,OAAO,EAAE;MACvBS,aAAa,CAACZ,WAAW,CAACG,OAAO,CAAC;MAClCH,WAAW,CAACG,OAAO,GAAG,IAAI;IAC5B;IACAN,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMgB,cAAc,GAAGA,CAAA,KAAM;IAC3BF,cAAc,CAAC,CAAC;IAChBhB,eAAe,CAAC,CAAC,CAAC;IAClBF,iBAAiB,CAAC,EAAE,CAAC;IACrBM,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAMe,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,WAAW,GAAGvB,cAAc,CAACwB,IAAI,CAAC,IAAI,CAAC;IAC7CC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACJ,WAAW,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAML,WAAW,GAAGvB,cAAc,CAACwB,IAAI,CAAC,IAAI,CAAC;IAC7C,MAAMK,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACP,WAAW,CAAC,EAAE;MAAEQ,IAAI,EAAE;IAAa,CAAC,CAAC;IAC5D,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IACrC,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACI,QAAQ,GAAG,GAAG3C,KAAK,aAAa;IAClCwC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;IAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;IACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;IAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;EAC1B,CAAC;;EAED;EACAnD,SAAS,CAAC,MAAM;IACd,IAAIc,QAAQ,IAAI,CAACS,SAAS,IAAI,CAACE,WAAW,EAAE;MAC1CQ,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACnB,QAAQ,CAAC,CAAC;;EAEd;EACAd,SAAS,CAAC,MAAM;IACdoB,iBAAiB,CAACP,OAAO,CAACmD,KAAK,CAAC,CAAC,EAAE3C,YAAY,CAAC,CAAC;IACjDQ,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACR,YAAY,EAAER,OAAO,CAAC,CAAC;;EAE3B;EACAb,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAI2B,WAAW,CAACG,OAAO,EAAE;QACvBS,aAAa,CAACZ,WAAW,CAACG,OAAO,CAAC;MACpC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmC,UAAU,GAAGA,CAACC,IAAY,EAAEC,KAAa,KAAK;IAClD;IACA,IAAID,IAAI,CAACE,UAAU,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAE;MAChD,oBACE1D,OAAA;QAAiB2D,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EACxDL,IAAI,CAACF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAAC,GADVG,KAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CAAC;IAEV;IAEA,IAAIT,IAAI,CAACE,UAAU,CAAC,IAAI,CAAC,EAAE;MACzB,oBACEzD,OAAA;QAAiB2D,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAC3DL,IAAI,CAACF,KAAK,CAAC,CAAC;MAAC,GADNG,KAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CAAC;IAEV;IAEA,IAAIT,IAAI,CAACE,UAAU,CAAC,KAAK,CAAC,EAAE;MAC1B,oBACEzD,OAAA;QAAiB2D,SAAS,EAAC,6EAA6E;QAAAC,QAAA,EACrGL,IAAI,KAAK,KAAK,GAAG,EAAE,GAAGA,IAAI,CAACF,KAAK,CAAC,CAAC;MAAC,GAD5BG,KAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CAAC;IAEV;IAEA,IAAIT,IAAI,CAACE,UAAU,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACE,UAAU,CAAC,IAAI,CAAC,EAAE;MAClD,oBACEzD,OAAA;QAAiB2D,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBAClD5D,OAAA;UAAM2D,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC7CT,IAAI,CAACF,KAAK,CAAC,CAAC,CAAC;MAAA,GAFNG,KAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGV,CAAC;IAEV;IAEA,IAAIT,IAAI,CAACU,QAAQ,CAAC,GAAG,CAAC,IAAIV,IAAI,CAACU,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC5C,oBACEjE,OAAA;QAAiB2D,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAC7CL;MAAI,GADGC,KAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CAAC;IAEV;IAEA,IAAIT,IAAI,CAACU,QAAQ,CAAC,IAAI,CAAC,IAAIV,IAAI,CAACU,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC9C,oBACEjE,OAAA;QAAiB2D,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAC9CL;MAAI,GADGC,KAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CAAC;IAEV;IAEA,IAAIT,IAAI,CAACU,QAAQ,CAAC,IAAI,CAAC,IAAIV,IAAI,CAACU,QAAQ,CAAC,IAAI,CAAC,IAAIV,IAAI,CAACU,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrE,oBACEjE,OAAA;QAAiB2D,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAC7CL;MAAI,GADGC,KAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CAAC;IAEV;IAEA,oBACEhE,OAAA;MAAiB2D,SAAS,EAAC,oBAAoB;MAAAC,QAAA,GAC5CL,IAAI,IAAI,QAAQ,EAAC,GAAC;IAAA,GADXC,KAAK;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEV,CAAC;EAEV,CAAC;EAED,oBACEhE,OAAA,CAACT,MAAM,CAAC2E,GAAG;IACTP,SAAS,EAAC,gCAAgC;IAC1CQ,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAZ,QAAA,gBAG9B5D,OAAA;MAAK2D,SAAS,EAAC,gEAAgE;MAAAC,QAAA,gBAC7E5D,OAAA;QAAK2D,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C5D,OAAA,CAACP,QAAQ;UAACkE,SAAS,EAAC;QAAyB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDhE,OAAA;UAAI2D,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAExD;QAAK;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EACpDpD,SAAS,iBACRZ,OAAA;UAAK2D,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C5D,OAAA;YAAK2D,SAAS,EAAC;UAAiD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvEhE,OAAA;YAAM2D,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CACN,EACAlD,WAAW,iBACVd,OAAA;UAAK2D,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C5D,OAAA;YAAK2D,SAAS,EAAC;UAAkC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxDhE,OAAA;YAAM2D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENhE,OAAA;QAAK2D,SAAS,EAAC,6BAA6B;QAAAC,QAAA,GACzChD,SAAS,gBACRZ,OAAA;UACEyE,OAAO,EAAE9C,cAAe;UACxBgC,SAAS,EAAC,gFAAgF;UAC1FvD,KAAK,EAAC,cAAI;UAAAwD,QAAA,eAEV5D,OAAA,CAACJ,KAAK;YAAC+D,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,gBAEThE,OAAA;UACEyE,OAAO,EAAEnD,cAAe;UACxBoD,QAAQ,EAAE5D,WAAY;UACtB6C,SAAS,EAAC,oGAAoG;UAC9GvD,KAAK,EAAC,cAAI;UAAAwD,QAAA,eAEV5D,OAAA,CAACH,IAAI;YAAC8D,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACT,eAEDhE,OAAA;UACEyE,OAAO,EAAE5C,cAAe;UACxB8B,SAAS,EAAC,gFAAgF;UAC1FvD,KAAK,EAAC,cAAI;UAAAwD,QAAA,eAEV5D,OAAA,CAACF,SAAS;YAAC6D,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEThE,OAAA;UACEyE,OAAO,EAAE3C,WAAY;UACrB4C,QAAQ,EAAElE,cAAc,CAACe,MAAM,KAAK,CAAE;UACtCoC,SAAS,EAAC,oGAAoG;UAC9GvD,KAAK,EAAC,cAAI;UAAAwD,QAAA,eAEV5D,OAAA,CAACN,IAAI;YAACiE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEThE,OAAA;UACEyE,OAAO,EAAErC,eAAgB;UACzBsC,QAAQ,EAAElE,cAAc,CAACe,MAAM,KAAK,CAAE;UACtCoC,SAAS,EAAC,oGAAoG;UAC9GvD,KAAK,EAAC,cAAI;UAAAwD,QAAA,eAEV5D,OAAA,CAACL,QAAQ;YAACgE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhE,OAAA;MACE2E,GAAG,EAAE1D,YAAa;MAClB0C,SAAS,EAAC,6EAA6E;MACvFiB,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAQ,CAAE;MAAAjB,QAAA,gBAE9B5D,OAAA,CAACR,eAAe;QAAAoE,QAAA,EACbpD,cAAc,CAACsE,GAAG,CAAC,CAACvB,IAAI,EAAEC,KAAK,kBAC9BxD,OAAA,CAACT,MAAM,CAAC2E,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEW,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCT,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEW,CAAC,EAAE;UAAE,CAAE;UAC9BR,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAZ,QAAA,EAE7BN,UAAU,CAACC,IAAI,EAAEC,KAAK;QAAC,GALnBA,KAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMA,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,EAGjBpD,SAAS,iBACRZ,OAAA,CAACT,MAAM,CAAC2E,GAAG;QACTP,SAAS,EAAC,yCAAyC;QACnDW,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC;QAAE,CAAE;QAC7BG,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEQ,MAAM,EAAEC;QAAS;MAAE;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNhE,OAAA;MAAK2D,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE5D,OAAA;QAAK2D,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD5D,OAAA;UAAA4D,QAAA,GAAM,gBACA,EAACpD,cAAc,CAACe,MAAM,EAAC,KAAG,EAACrB,OAAO,CAACqB,MAAM;QAAA;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACPhE,OAAA;UAAA4D,QAAA,GAAM,gBACA,EAACsB,IAAI,CAACC,KAAK,CAAEzE,YAAY,GAAGR,OAAO,CAACqB,MAAM,GAAI,GAAG,CAAC,EAAC,GACzD;QAAA;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAACzD,EAAA,CA1RWN,YAAyC;AAAAmF,EAAA,GAAzCnF,YAAyC;AAAA,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}