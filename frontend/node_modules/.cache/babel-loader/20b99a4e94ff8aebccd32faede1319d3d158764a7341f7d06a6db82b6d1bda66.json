{"ast": null, "code": "/**\n * lucide-react v0.292.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Maximize2 = createLucideIcon(\"Maximize2\", [[\"polyline\", {\n  points: \"15 3 21 3 21 9\",\n  key: \"mznyad\"\n}], [\"polyline\", {\n  points: \"9 21 3 21 3 15\",\n  key: \"1avn1i\"\n}], [\"line\", {\n  x1: \"21\",\n  x2: \"14\",\n  y1: \"3\",\n  y2: \"10\",\n  key: \"ota7mn\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"10\",\n  y1: \"21\",\n  y2: \"14\",\n  key: \"1atl0r\"\n}]]);\nexport { Maximize2 as default };\n//# sourceMappingURL=maximize-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}