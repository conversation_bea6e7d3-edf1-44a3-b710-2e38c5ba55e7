{"ast": null, "code": "/**\n * lucide-react v0.292.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ArrowRightLeft = createLucideIcon(\"ArrowRightLeft\", [[\"path\", {\n  d: \"m16 3 4 4-4 4\",\n  key: \"1x1c3m\"\n}], [\"path\", {\n  d: \"M20 7H4\",\n  key: \"zbl0bi\"\n}], [\"path\", {\n  d: \"m8 21-4-4 4-4\",\n  key: \"h9nckh\"\n}], [\"path\", {\n  d: \"M4 17h16\",\n  key: \"g4d7ey\"\n}]]);\nexport { ArrowRightLeft as default };\n//# sourceMappingURL=arrow-right-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}