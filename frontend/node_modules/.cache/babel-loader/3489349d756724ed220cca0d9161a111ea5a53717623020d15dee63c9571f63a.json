{"ast": null, "code": "export const generateStageOutput = [\"📊 **开始报表生成阶段**\", \"\", \"正在初始化报表生成引擎...\", \"✅ 报表模板引擎已加载\", \"✅ 数据可视化组件已就绪\", \"\", \"📋 **生成执行摘要报告**\", \"```\", \"📈 核心指标概览\", \"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\", \"📊 数据处理量：    2,103条记录\", \"🎯 数据匹配率：    92.7%\", \"⚡ 处理效率：      平均4.2小时响应\", \"😊 客户满意度：    78.6%\", \"🔄 重复投诉率：    12.4%\", \"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\", \"```\", \"\", \"📊 **生成数据可视化图表**\", \"\", \"正在生成投诉类型分布饼图...\", \"✅ 饼图生成完成 (chart_complaint_types.png)\", \"\", \"正在生成时间趋势折线图...\", \"✅ 折线图生成完成 (chart_time_trends.png)\", \"\", \"正在生成区域热力图...\", \"✅ 热力图生成完成 (chart_area_heatmap.png)\", \"\", \"正在生成客户分群柱状图...\", \"✅ 柱状图生成完成 (chart_customer_segments.png)\", \"\", \"📑 **生成详细分析报告**\", \"\", \"**第一部分：数据概览**\", \"- 数据来源统计 ✓\", \"- 数据质量评估 ✓\", \"- 关键指标汇总 ✓\", \"\", \"**第二部分：投诉分析**\", \"- 投诉类型深度分析 ✓\", \"- 区域分布热点分析 ✓\", \"- 时间模式挖掘分析 ✓\", \"\", \"**第三部分：客户洞察**\", \"- 客户行为画像分析 ✓\", \"- 投诉频次分群分析 ✓\", \"- 满意度影响因素分析 ✓\", \"\", \"**第四部分：运营建议**\", \"- 问题解决优化建议 ✓\", \"- 服务流程改进建议 ✓\", \"- 预防性措施建议 ✓\", \"\", \"📄 **生成多格式报告文件**\", \"\", \"正在生成Excel详细报告...\", \"```\", \"📊 Excel报告包含：\", \"   • 数据概览工作表\", \"   • 投诉明细工作表\", \"   • 统计分析工作表\", \"   • 图表汇总工作表\", \"   • 建议措施工作表\", \"```\", \"✅ Excel报告生成完成 (智能分析报告_详细版.xlsx)\", \"\", \"正在生成PDF执行报告...\", \"```\", \"📑 PDF报告包含：\", \"   • 封面与目录\", \"   • 执行摘要 (2页)\", \"   • 核心发现 (3页)\", \"   • 数据可视化 (4页)\", \"   • 行动建议 (2页)\", \"```\", \"✅ PDF报告生成完成 (智能分析报告_管理版.pdf)\", \"\", \"正在生成JSON数据报告...\", \"```\", \"🔧 JSON报告包含：\", \"   • 原始数据结构\", \"   • 分析结果数据\", \"   • 统计指标数据\", \"   • API接口数据\", \"```\", \"✅ JSON报告生成完成 (智能分析数据_API版.json)\", \"\", \"🎨 **生成可视化仪表板**\", \"\", \"正在构建交互式仪表板...\", \"- 实时数据监控面板 ✓\", \"- 投诉趋势分析面板 ✓\", \"- 区域热点地图面板 ✓\", \"- 客户画像分析面板 ✓\", \"\", \"✅ 交互式仪表板已生成\", \"\", \"📧 **准备报告分发**\", \"\", \"正在准备邮件发送列表...\", \"- 管理层：PDF执行报告\", \"- 运营团队：Excel详细报告\", \"- 技术团队：JSON数据报告\", \"\", \"🔐 **报告安全检查**\", \"- 敏感信息脱敏处理 ✓\", \"- 数据访问权限设置 ✓\", \"- 报告水印添加 ✓\", \"\", \"✅ **报表生成阶段完成**\", \"\", \"🎉 **所有报告已生成完毕！**\", \"\", \"📦 **生成文件清单：**\", \"```\", \"📊 智能分析报告_详细版.xlsx    (2.3MB)\", \"📑 智能分析报告_管理版.pdf     (1.8MB)\", \"🔧 智能分析数据_API版.json     (856KB)\", \"📈 数据可视化图表包.zip        (1.2MB)\", \"🎯 交互式仪表板.html          (3.4MB)\", \"```\", \"\", \"💡 **下一步建议：**\", \"1. 下载所需格式的报告文件\", \"2. 查看交互式仪表板进行深度分析\", \"3. 根据建议制定改进行动计划\", \"4. 设置定期分析监控机制\", \"\", \"🚀 **分析任务圆满完成！**\"];", "map": {"version": 3, "names": ["generateStageOutput"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/data/streamOutputs/generateStage.ts"], "sourcesContent": ["export const generateStageOutput = [\n  \"📊 **开始报表生成阶段**\",\n  \"\",\n  \"正在初始化报表生成引擎...\",\n  \"✅ 报表模板引擎已加载\",\n  \"✅ 数据可视化组件已就绪\",\n  \"\",\n  \"📋 **生成执行摘要报告**\",\n  \"```\",\n  \"📈 核心指标概览\",\n  \"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\",\n  \"📊 数据处理量：    2,103条记录\",\n  \"🎯 数据匹配率：    92.7%\",\n  \"⚡ 处理效率：      平均4.2小时响应\",\n  \"😊 客户满意度：    78.6%\",\n  \"🔄 重复投诉率：    12.4%\",\n  \"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\",\n  \"```\",\n  \"\",\n  \"📊 **生成数据可视化图表**\",\n  \"\",\n  \"正在生成投诉类型分布饼图...\",\n  \"✅ 饼图生成完成 (chart_complaint_types.png)\",\n  \"\",\n  \"正在生成时间趋势折线图...\",\n  \"✅ 折线图生成完成 (chart_time_trends.png)\",\n  \"\",\n  \"正在生成区域热力图...\",\n  \"✅ 热力图生成完成 (chart_area_heatmap.png)\",\n  \"\",\n  \"正在生成客户分群柱状图...\",\n  \"✅ 柱状图生成完成 (chart_customer_segments.png)\",\n  \"\",\n  \"📑 **生成详细分析报告**\",\n  \"\",\n  \"**第一部分：数据概览**\",\n  \"- 数据来源统计 ✓\",\n  \"- 数据质量评估 ✓\", \n  \"- 关键指标汇总 ✓\",\n  \"\",\n  \"**第二部分：投诉分析**\",\n  \"- 投诉类型深度分析 ✓\",\n  \"- 区域分布热点分析 ✓\",\n  \"- 时间模式挖掘分析 ✓\",\n  \"\",\n  \"**第三部分：客户洞察**\",\n  \"- 客户行为画像分析 ✓\",\n  \"- 投诉频次分群分析 ✓\",\n  \"- 满意度影响因素分析 ✓\",\n  \"\",\n  \"**第四部分：运营建议**\",\n  \"- 问题解决优化建议 ✓\",\n  \"- 服务流程改进建议 ✓\",\n  \"- 预防性措施建议 ✓\",\n  \"\",\n  \"📄 **生成多格式报告文件**\",\n  \"\",\n  \"正在生成Excel详细报告...\",\n  \"```\",\n  \"📊 Excel报告包含：\",\n  \"   • 数据概览工作表\",\n  \"   • 投诉明细工作表\", \n  \"   • 统计分析工作表\",\n  \"   • 图表汇总工作表\",\n  \"   • 建议措施工作表\",\n  \"```\",\n  \"✅ Excel报告生成完成 (智能分析报告_详细版.xlsx)\",\n  \"\",\n  \"正在生成PDF执行报告...\",\n  \"```\",\n  \"📑 PDF报告包含：\",\n  \"   • 封面与目录\",\n  \"   • 执行摘要 (2页)\",\n  \"   • 核心发现 (3页)\",\n  \"   • 数据可视化 (4页)\",\n  \"   • 行动建议 (2页)\",\n  \"```\",\n  \"✅ PDF报告生成完成 (智能分析报告_管理版.pdf)\",\n  \"\",\n  \"正在生成JSON数据报告...\",\n  \"```\",\n  \"🔧 JSON报告包含：\",\n  \"   • 原始数据结构\",\n  \"   • 分析结果数据\",\n  \"   • 统计指标数据\",\n  \"   • API接口数据\",\n  \"```\",\n  \"✅ JSON报告生成完成 (智能分析数据_API版.json)\",\n  \"\",\n  \"🎨 **生成可视化仪表板**\",\n  \"\",\n  \"正在构建交互式仪表板...\",\n  \"- 实时数据监控面板 ✓\",\n  \"- 投诉趋势分析面板 ✓\", \n  \"- 区域热点地图面板 ✓\",\n  \"- 客户画像分析面板 ✓\",\n  \"\",\n  \"✅ 交互式仪表板已生成\",\n  \"\",\n  \"📧 **准备报告分发**\",\n  \"\",\n  \"正在准备邮件发送列表...\",\n  \"- 管理层：PDF执行报告\",\n  \"- 运营团队：Excel详细报告\", \n  \"- 技术团队：JSON数据报告\",\n  \"\",\n  \"🔐 **报告安全检查**\",\n  \"- 敏感信息脱敏处理 ✓\",\n  \"- 数据访问权限设置 ✓\",\n  \"- 报告水印添加 ✓\",\n  \"\",\n  \"✅ **报表生成阶段完成**\",\n  \"\",\n  \"🎉 **所有报告已生成完毕！**\",\n  \"\",\n  \"📦 **生成文件清单：**\",\n  \"```\",\n  \"📊 智能分析报告_详细版.xlsx    (2.3MB)\",\n  \"📑 智能分析报告_管理版.pdf     (1.8MB)\", \n  \"🔧 智能分析数据_API版.json     (856KB)\",\n  \"📈 数据可视化图表包.zip        (1.2MB)\",\n  \"🎯 交互式仪表板.html          (3.4MB)\",\n  \"```\",\n  \"\",\n  \"💡 **下一步建议：**\",\n  \"1. 下载所需格式的报告文件\",\n  \"2. 查看交互式仪表板进行深度分析\",\n  \"3. 根据建议制定改进行动计划\",\n  \"4. 设置定期分析监控机制\",\n  \"\",\n  \"🚀 **分析任务圆满完成！**\"\n];\n"], "mappings": "AAAA,OAAO,MAAMA,mBAAmB,GAAG,CACjC,iBAAiB,EACjB,EAAE,EACF,gBAAgB,EAChB,aAAa,EACb,cAAc,EACd,EAAE,EACF,iBAAiB,EACjB,KAAK,EACL,WAAW,EACX,qDAAqD,EACrD,uBAAuB,EACvB,oBAAoB,EACpB,wBAAwB,EACxB,oBAAoB,EACpB,oBAAoB,EACpB,qDAAqD,EACrD,KAAK,EACL,EAAE,EACF,kBAAkB,EAClB,EAAE,EACF,iBAAiB,EACjB,sCAAsC,EACtC,EAAE,EACF,gBAAgB,EAChB,mCAAmC,EACnC,EAAE,EACF,cAAc,EACd,oCAAoC,EACpC,EAAE,EACF,gBAAgB,EAChB,yCAAyC,EACzC,EAAE,EACF,iBAAiB,EACjB,EAAE,EACF,eAAe,EACf,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,EAAE,EACF,eAAe,EACf,cAAc,EACd,cAAc,EACd,cAAc,EACd,EAAE,EACF,eAAe,EACf,cAAc,EACd,cAAc,EACd,eAAe,EACf,EAAE,EACF,eAAe,EACf,cAAc,EACd,cAAc,EACd,aAAa,EACb,EAAE,EACF,kBAAkB,EAClB,EAAE,EACF,kBAAkB,EAClB,KAAK,EACL,eAAe,EACf,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,KAAK,EACL,iCAAiC,EACjC,EAAE,EACF,gBAAgB,EAChB,KAAK,EACL,aAAa,EACb,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,EAChB,iBAAiB,EACjB,gBAAgB,EAChB,KAAK,EACL,8BAA8B,EAC9B,EAAE,EACF,iBAAiB,EACjB,KAAK,EACL,cAAc,EACd,aAAa,EACb,aAAa,EACb,aAAa,EACb,cAAc,EACd,KAAK,EACL,iCAAiC,EACjC,EAAE,EACF,iBAAiB,EACjB,EAAE,EACF,eAAe,EACf,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,EAAE,EACF,aAAa,EACb,EAAE,EACF,eAAe,EACf,EAAE,EACF,eAAe,EACf,eAAe,EACf,kBAAkB,EAClB,iBAAiB,EACjB,EAAE,EACF,eAAe,EACf,cAAc,EACd,cAAc,EACd,YAAY,EACZ,EAAE,EACF,gBAAgB,EAChB,EAAE,EACF,mBAAmB,EACnB,EAAE,EACF,gBAAgB,EAChB,KAAK,EACL,+BAA+B,EAC/B,+BAA+B,EAC/B,iCAAiC,EACjC,gCAAgC,EAChC,iCAAiC,EACjC,KAAK,EACL,EAAE,EACF,eAAe,EACf,gBAAgB,EAChB,mBAAmB,EACnB,iBAAiB,EACjB,eAAe,EACf,EAAE,EACF,kBAAkB,CACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}