{"ast": null, "code": "/**\n * lucide-react v0.292.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst CornerDownRight = createLucideIcon(\"CornerDownRight\", [[\"polyline\", {\n  points: \"15 10 20 15 15 20\",\n  key: \"1q7qjw\"\n}], [\"path\", {\n  d: \"M4 4v7a4 4 0 0 0 4 4h12\",\n  key: \"z08zvw\"\n}]]);\nexport { CornerDownRight as default };", "map": {"version": 3, "names": ["CornerDownRight", "createLucideIcon", "points", "key", "d"], "sources": ["D:\\pyworkspace\\analysisFlow\\frontend\\node_modules\\lucide-react\\src\\icons\\corner-down-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CornerDownRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIxNSAxMCAyMCAxNSAxNSAyMCIgLz4KICA8cGF0aCBkPSJNNCA0djdhNCA0IDAgMCAwIDQgNGgxMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/corner-down-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CornerDownRight = createLucideIcon('CornerDownRight', [\n  ['polyline', { points: '15 10 20 15 15 20', key: '1q7qjw' }],\n  ['path', { d: 'M4 4v7a4 4 0 0 0 4 4h12', key: 'z08zvw' }],\n]);\n\nexport default CornerDownRight;\n"], "mappings": ";;;;;AAaM,MAAAA,eAAA,GAAkBC,gBAAA,CAAiB,iBAAmB,GAC1D,CAAC,UAAY;EAAEC,MAAA,EAAQ,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}