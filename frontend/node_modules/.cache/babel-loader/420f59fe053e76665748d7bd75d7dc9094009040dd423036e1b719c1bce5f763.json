{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\components\\\\ProcessFlow.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Upload, CheckCircle, Settings, Brain, FileText, Download, Clock, AlertCircle, Zap } from 'lucide-react';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\n\n// 模拟已处理文件数据\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProcessFlow = () => {\n  _s();\n  const {\n    processState\n  } = useProcessFlowContext();\n\n  // 检查是否有已处理的文件（用于显示下载界面）\n  const hasProcessedFiles = true; // 简化逻辑，总是显示下载选项\n\n  // 配置下载目录（可配置）\n  const downloadDirectory = process.env.REACT_APP_DOWNLOAD_DIR || '/downloads';\n\n  // 下载分析结果报表\n  const downloadAnalysisReport = () => {\n    // 下载预设的分析结果报表\n    const link = document.createElement('a');\n    link.href = `${downloadDirectory}/文本分析结果报表.pdf`; // 从配置目录下载\n    link.download = '文本分析结果报表.pdf';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    console.log('下载文本分析结果报表完成');\n  };\n  const getStepIcon = stepId => {\n    switch (stepId) {\n      case 'upload':\n        return /*#__PURE__*/_jsxDEV(Upload, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 29\n        }, this);\n      case 'validate':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 31\n        }, this);\n      case 'configure':\n        return /*#__PURE__*/_jsxDEV(Settings, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 32\n        }, this);\n      case 'analyze':\n        return /*#__PURE__*/_jsxDEV(Brain, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 30\n        }, this);\n      case 'generate':\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 31\n        }, this);\n      case 'download':\n        return /*#__PURE__*/_jsxDEV(Download, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 31\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  const getStepColor = status => {\n    switch (status) {\n      case 'completed':\n        return 'text-tech-green border-tech-green bg-tech-green/10';\n      case 'active':\n        return 'text-tech-cyan border-tech-cyan bg-tech-cyan/10 animate-glow';\n      case 'error':\n        return 'text-red-400 border-red-400 bg-red-400/10';\n      default:\n        return 'text-gray-400 border-gray-600 bg-gray-600/10';\n    }\n  };\n  const getConnectorColor = (currentStatus, nextStatus) => {\n    if (currentStatus === 'completed') {\n      return 'bg-tech-green';\n    } else if (currentStatus === 'active') {\n      return 'bg-gradient-to-b from-tech-cyan to-gray-600';\n    }\n    return 'bg-gray-600';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"tech-card p-6 h-full flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-3 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"p-2 bg-gradient-to-r from-tech-cyan to-tech-purple rounded-lg\",\n        animate: {\n          rotate: [0, 360]\n        },\n        transition: {\n          duration: 20,\n          repeat: Infinity,\n          ease: \"linear\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Zap, {\n          className: \"w-6 h-6 text-white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-white\",\n          children: \"\\u5904\\u7406\\u6D41\\u7A0B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 text-sm\",\n          children: \"\\u5B9E\\u65F6\\u76D1\\u63A7\\u5206\\u6790\\u8FDB\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4 flex-1\",\n      children: processState.steps.map((step, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"relative\",\n        initial: {\n          opacity: 0,\n          x: -20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        children: [index < processState.steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute left-6 top-12 w-0.5 h-8 z-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-full h-full ${getConnectorColor(step.status, processState.steps[index + 1].status)} transition-all duration-500`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: `relative z-10 flex items-start space-x-4 p-4 rounded-lg border transition-all duration-300 ${getStepColor(step.status)}`,\n          whileHover: {\n            scale: 1.02\n          },\n          layout: true,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex-shrink-0 p-2 rounded-lg border ${getStepColor(step.status)}`,\n            children: step.status === 'active' ? /*#__PURE__*/_jsxDEV(motion.div, {\n              animate: {\n                rotate: 360\n              },\n              transition: {\n                duration: 2,\n                repeat: Infinity,\n                ease: \"linear\"\n              },\n              children: getStepIcon(step.id)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 19\n            }, this) : step.status === 'completed' ? /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0\n              },\n              animate: {\n                scale: 1\n              },\n              transition: {\n                type: \"spring\",\n                stiffness: 500,\n                damping: 30\n              },\n              children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 19\n            }, this) : step.status === 'error' ? /*#__PURE__*/_jsxDEV(AlertCircle, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 19\n            }, this) : getStepIcon(step.id)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-white\",\n              children: step.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mt-1\",\n              children: step.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: step.status === 'active' && step.progress !== undefined && /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"mt-3\",\n                initial: {\n                  opacity: 0,\n                  height: 0\n                },\n                animate: {\n                  opacity: 1,\n                  height: 'auto'\n                },\n                exit: {\n                  opacity: 0,\n                  height: 0\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-xs text-gray-400 mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u5904\\u7406\\u4E2D...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [step.progress, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full bg-gray-700 rounded-full h-2\",\n                  children: /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"progress-bar h-2 rounded-full\",\n                    initial: {\n                      width: 0\n                    },\n                    animate: {\n                      width: `${step.progress}%`\n                    },\n                    transition: {\n                      duration: 0.3\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), step.status === 'completed' && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"flex items-center space-x-1 mt-2 text-xs text-tech-green\",\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Clock, {\n                className: \"w-3 h-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"\\u5B8C\\u6210\\u4E8E \", new Date().toLocaleTimeString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 19\n            }, this), step.id === 'download' && step.status === 'completed' && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"mt-4 p-4 bg-green-900/20 border border-green-500/30 rounded-lg\",\n              initial: {\n                opacity: 0,\n                y: 10\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.3\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-green-400 font-semibold mb-3 flex items-center text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-4 h-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 23\n                }, this), \"\\u5206\\u6790\\u7ED3\\u679C\\u4E0B\\u8F7D\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-800/50 p-3 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(FileText, {\n                        className: \"w-4 h-4 text-red-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 204,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-300 text-sm\",\n                        children: \"\\u6587\\u672C\\u5206\\u6790\\u7ED3\\u679C\\u62A5\\u8868.pdf\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 205,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"\\u7EFC\\u5408\\u5206\\u6790\\u62A5\\u544A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: downloadAnalysisReport,\n                  className: \"w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-2 px-3 rounded transition-all duration-300 flex items-center justify-center text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(Download, {\n                    className: \"w-4 h-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 25\n                  }, this), \"\\u4E0B\\u8F7D\\u6587\\u672C\\u5206\\u6790\\u7ED3\\u679C\\u62A5\\u8868\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this)]\n      }, step.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"mt-6 p-4 tech-card\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.5\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between text-sm text-gray-400 mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u603B\\u4F53\\u8FDB\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [Math.round(processState.overallProgress), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full bg-gray-700 rounded-full h-2\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"progress-bar h-2 rounded-full\",\n          initial: {\n            width: 0\n          },\n          animate: {\n            width: `${processState.overallProgress}%`\n          },\n          transition: {\n            duration: 0.5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n};\n_s(ProcessFlow, \"ZTtg0zH22t11rUv1mhOdEAoX2Mc=\", false, function () {\n  return [useProcessFlowContext];\n});\n_c = ProcessFlow;\nexport default ProcessFlow;\nvar _c;\n$RefreshReg$(_c, \"ProcessFlow\");", "map": {"version": 3, "names": ["React", "motion", "AnimatePresence", "Upload", "CheckCircle", "Settings", "Brain", "FileText", "Download", "Clock", "AlertCircle", "Zap", "useProcessFlowContext", "jsxDEV", "_jsxDEV", "ProcessFlow", "_s", "processState", "hasProcessedFiles", "downloadDirectory", "process", "env", "REACT_APP_DOWNLOAD_DIR", "downloadAnalysisReport", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "getStepIcon", "stepId", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStepColor", "status", "getConnectorColor", "currentStatus", "nextStatus", "children", "div", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "steps", "map", "step", "index", "initial", "opacity", "x", "delay", "length", "whileHover", "scale", "layout", "id", "type", "stiffness", "damping", "title", "description", "progress", "undefined", "height", "exit", "width", "Date", "toLocaleTimeString", "y", "onClick", "Math", "round", "overallProgress", "_c", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/components/ProcessFlow.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Upload,\n  CheckCircle,\n  Settings,\n  Brain,\n  FileText,\n  Download,\n  Clock,\n  AlertCircle,\n  Zap\n} from 'lucide-react';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\n\n// 模拟已处理文件数据\ninterface ProcessedFile {\n  id: string;\n  filename: string;\n  status: 'processed';\n}\n\nconst ProcessFlow: React.FC = () => {\n  const { processState } = useProcessFlowContext();\n\n  // 检查是否有已处理的文件（用于显示下载界面）\n  const hasProcessedFiles = true; // 简化逻辑，总是显示下载选项\n\n  // 配置下载目录（可配置）\n  const downloadDirectory = process.env.REACT_APP_DOWNLOAD_DIR || '/downloads';\n\n  // 下载分析结果报表\n  const downloadAnalysisReport = () => {\n    // 下载预设的分析结果报表\n    const link = document.createElement('a');\n    link.href = `${downloadDirectory}/文本分析结果报表.pdf`; // 从配置目录下载\n    link.download = '文本分析结果报表.pdf';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    console.log('下载文本分析结果报表完成');\n  };\n\n  const getStepIcon = (stepId: string) => {\n    switch (stepId) {\n      case 'upload': return <Upload className=\"w-6 h-6\" />;\n      case 'validate': return <CheckCircle className=\"w-6 h-6\" />;\n      case 'configure': return <Settings className=\"w-6 h-6\" />;\n      case 'analyze': return <Brain className=\"w-6 h-6\" />;\n      case 'generate': return <FileText className=\"w-6 h-6\" />;\n      case 'download': return <Download className=\"w-6 h-6\" />;\n      default: return <FileText className=\"w-6 h-6\" />;\n    }\n  };\n\n\n\n  const getStepColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'text-tech-green border-tech-green bg-tech-green/10';\n      case 'active':\n        return 'text-tech-cyan border-tech-cyan bg-tech-cyan/10 animate-glow';\n      case 'error':\n        return 'text-red-400 border-red-400 bg-red-400/10';\n      default:\n        return 'text-gray-400 border-gray-600 bg-gray-600/10';\n    }\n  };\n\n  const getConnectorColor = (currentStatus: string, nextStatus: string) => {\n    if (currentStatus === 'completed') {\n      return 'bg-tech-green';\n    } else if (currentStatus === 'active') {\n      return 'bg-gradient-to-b from-tech-cyan to-gray-600';\n    }\n    return 'bg-gray-600';\n  };\n\n  return (\n    <div className=\"tech-card p-6 h-full flex flex-col\">\n      <div className=\"flex items-center space-x-3 mb-6\">\n        <motion.div\n          className=\"p-2 bg-gradient-to-r from-tech-cyan to-tech-purple rounded-lg\"\n          animate={{ rotate: [0, 360] }}\n          transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n        >\n          <Zap className=\"w-6 h-6 text-white\" />\n        </motion.div>\n        <div>\n          <h2 className=\"text-xl font-bold text-white\">处理流程</h2>\n          <p className=\"text-gray-400 text-sm\">实时监控分析进度</p>\n        </div>\n      </div>\n\n      <div className=\"space-y-4 flex-1\">\n        {processState.steps.map((step, index) => (\n          <motion.div\n            key={step.id}\n            className=\"relative\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: index * 0.1 }}\n          >\n            {/* 连接线 */}\n            {index < processState.steps.length - 1 && (\n              <div className=\"absolute left-6 top-12 w-0.5 h-8 z-0\">\n                <div\n                  className={`w-full h-full ${getConnectorColor(step.status, processState.steps[index + 1].status)} transition-all duration-500`}\n                />\n              </div>\n            )}\n\n            {/* 步骤卡片 */}\n            <motion.div\n              className={`relative z-10 flex items-start space-x-4 p-4 rounded-lg border transition-all duration-300 ${getStepColor(step.status)}`}\n              whileHover={{ scale: 1.02 }}\n              layout\n            >\n              {/* 图标 */}\n              <div className={`flex-shrink-0 p-2 rounded-lg border ${getStepColor(step.status)}`}>\n                {step.status === 'active' ? (\n                  <motion.div\n                    animate={{ rotate: 360 }}\n                    transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n                  >\n                    {getStepIcon(step.id)}\n                  </motion.div>\n                ) : step.status === 'completed' ? (\n                  <motion.div\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                  >\n                    <CheckCircle className=\"w-6 h-6\" />\n                  </motion.div>\n                ) : step.status === 'error' ? (\n                  <AlertCircle className=\"w-6 h-6\" />\n                ) : (\n                  getStepIcon(step.id)\n                )}\n              </div>\n\n              {/* 内容 */}\n              <div className=\"flex-1 min-w-0\">\n                <h3 className=\"font-medium text-white\">{step.title}</h3>\n                <p className=\"text-sm text-gray-400 mt-1\">{step.description}</p>\n                \n                {/* 进度条 */}\n                <AnimatePresence>\n                  {step.status === 'active' && step.progress !== undefined && (\n                    <motion.div\n                      className=\"mt-3\"\n                      initial={{ opacity: 0, height: 0 }}\n                      animate={{ opacity: 1, height: 'auto' }}\n                      exit={{ opacity: 0, height: 0 }}\n                    >\n                      <div className=\"flex items-center justify-between text-xs text-gray-400 mb-1\">\n                        <span>处理中...</span>\n                        <span>{step.progress}%</span>\n                      </div>\n                      <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                        <motion.div\n                          className=\"progress-bar h-2 rounded-full\"\n                          initial={{ width: 0 }}\n                          animate={{ width: `${step.progress}%` }}\n                          transition={{ duration: 0.3 }}\n                        />\n                      </div>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n\n                {/* 时间戳 */}\n                {step.status === 'completed' && (\n                  <motion.div\n                    className=\"flex items-center space-x-1 mt-2 text-xs text-tech-green\"\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                  >\n                    <Clock className=\"w-3 h-3\" />\n                    <span>完成于 {new Date().toLocaleTimeString()}</span>\n                  </motion.div>\n                )}\n\n                {/* 结果下载步骤的特殊内容 */}\n                {step.id === 'download' && step.status === 'completed' && (\n                  <motion.div\n                    className=\"mt-4 p-4 bg-green-900/20 border border-green-500/30 rounded-lg\"\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.3 }}\n                  >\n                    <h4 className=\"text-green-400 font-semibold mb-3 flex items-center text-sm\">\n                      <CheckCircle className=\"w-4 h-4 mr-2\" />\n                      分析结果下载\n                    </h4>\n\n                    <div className=\"space-y-3\">\n                      <div className=\"bg-gray-800/50 p-3 rounded-lg\">\n                        <div className=\"flex items-center justify-between\">\n                          <div className=\"flex items-center space-x-2\">\n                            <FileText className=\"w-4 h-4 text-red-400\" />\n                            <span className=\"text-gray-300 text-sm\">文本分析结果报表.pdf</span>\n                          </div>\n                          <span className=\"text-xs text-gray-500\">综合分析报告</span>\n                        </div>\n                      </div>\n\n                      <button\n                        onClick={downloadAnalysisReport}\n                        className=\"w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-2 px-3 rounded transition-all duration-300 flex items-center justify-center text-sm\"\n                      >\n                        <Download className=\"w-4 h-4 mr-2\" />\n                        下载文本分析结果报表\n                      </button>\n                    </div>\n                  </motion.div>\n                )}\n              </div>\n            </motion.div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* 总体进度 */}\n      <motion.div\n        className=\"mt-6 p-4 tech-card\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.5 }}\n      >\n        <div className=\"flex items-center justify-between text-sm text-gray-400 mb-2\">\n          <span>总体进度</span>\n          <span>{Math.round(processState.overallProgress)}%</span>\n        </div>\n        <div className=\"w-full bg-gray-700 rounded-full h-2\">\n          <motion.div\n            className=\"progress-bar h-2 rounded-full\"\n            initial={{ width: 0 }}\n            animate={{ width: `${processState.overallProgress}%` }}\n            transition={{ duration: 0.5 }}\n          />\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default ProcessFlow;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAoB,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,MAAM,EACNC,WAAW,EACXC,QAAQ,EACRC,KAAK,EACLC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,GAAG,QACE,cAAc;AACrB,SAASC,qBAAqB,QAAQ,gCAAgC;;AAEtE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAa,CAAC,GAAGL,qBAAqB,CAAC,CAAC;;EAEhD;EACA,MAAMM,iBAAiB,GAAG,IAAI,CAAC,CAAC;;EAEhC;EACA,MAAMC,iBAAiB,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,YAAY;;EAE5E;EACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACA,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,GAAGR,iBAAiB,eAAe,CAAC,CAAC;IACjDK,IAAI,CAACI,QAAQ,GAAG,cAAc;IAC9BH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IAE/BS,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B,CAAC;EAED,MAAMC,WAAW,GAAIC,MAAc,IAAK;IACtC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,oBAAOtB,OAAA,CAACX,MAAM;UAACkC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD,KAAK,UAAU;QAAE,oBAAO3B,OAAA,CAACV,WAAW;UAACiC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK,WAAW;QAAE,oBAAO3B,OAAA,CAACT,QAAQ;UAACgC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD,KAAK,SAAS;QAAE,oBAAO3B,OAAA,CAACR,KAAK;UAAC+B,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD,KAAK,UAAU;QAAE,oBAAO3B,OAAA,CAACP,QAAQ;UAAC8B,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxD,KAAK,UAAU;QAAE,oBAAO3B,OAAA,CAACN,QAAQ;UAAC6B,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxD;QAAS,oBAAO3B,OAAA,CAACP,QAAQ;UAAC8B,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClD;EACF,CAAC;EAID,MAAMC,YAAY,GAAIC,MAAc,IAAK;IACvC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,oDAAoD;MAC7D,KAAK,QAAQ;QACX,OAAO,8DAA8D;MACvE,KAAK,OAAO;QACV,OAAO,2CAA2C;MACpD;QACE,OAAO,8CAA8C;IACzD;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAACC,aAAqB,EAAEC,UAAkB,KAAK;IACvE,IAAID,aAAa,KAAK,WAAW,EAAE;MACjC,OAAO,eAAe;IACxB,CAAC,MAAM,IAAIA,aAAa,KAAK,QAAQ,EAAE;MACrC,OAAO,6CAA6C;IACtD;IACA,OAAO,aAAa;EACtB,CAAC;EAED,oBACE/B,OAAA;IAAKuB,SAAS,EAAC,oCAAoC;IAAAU,QAAA,gBACjDjC,OAAA;MAAKuB,SAAS,EAAC,kCAAkC;MAAAU,QAAA,gBAC/CjC,OAAA,CAACb,MAAM,CAAC+C,GAAG;QACTX,SAAS,EAAC,+DAA+D;QACzEY,OAAO,EAAE;UAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG;QAAE,CAAE;QAC9BC,UAAU,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEC,MAAM,EAAEC,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAE;QAAAR,QAAA,eAE/DjC,OAAA,CAACH,GAAG;UAAC0B,SAAS,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACb3B,OAAA;QAAAiC,QAAA,gBACEjC,OAAA;UAAIuB,SAAS,EAAC,8BAA8B;UAAAU,QAAA,EAAC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtD3B,OAAA;UAAGuB,SAAS,EAAC,uBAAuB;UAAAU,QAAA,EAAC;QAAQ;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3B,OAAA;MAAKuB,SAAS,EAAC,kBAAkB;MAAAU,QAAA,EAC9B9B,YAAY,CAACuC,KAAK,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAClC7C,OAAA,CAACb,MAAM,CAAC+C,GAAG;QAETX,SAAS,EAAC,UAAU;QACpBuB,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCb,OAAO,EAAE;UAAEY,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BX,UAAU,EAAE;UAAEY,KAAK,EAAEJ,KAAK,GAAG;QAAI,CAAE;QAAAZ,QAAA,GAGlCY,KAAK,GAAG1C,YAAY,CAACuC,KAAK,CAACQ,MAAM,GAAG,CAAC,iBACpClD,OAAA;UAAKuB,SAAS,EAAC,sCAAsC;UAAAU,QAAA,eACnDjC,OAAA;YACEuB,SAAS,EAAE,iBAAiBO,iBAAiB,CAACc,IAAI,CAACf,MAAM,EAAE1B,YAAY,CAACuC,KAAK,CAACG,KAAK,GAAG,CAAC,CAAC,CAAChB,MAAM,CAAC;UAA+B;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGD3B,OAAA,CAACb,MAAM,CAAC+C,GAAG;UACTX,SAAS,EAAE,8FAA8FK,YAAY,CAACgB,IAAI,CAACf,MAAM,CAAC,EAAG;UACrIsB,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,MAAM;UAAApB,QAAA,gBAGNjC,OAAA;YAAKuB,SAAS,EAAE,uCAAuCK,YAAY,CAACgB,IAAI,CAACf,MAAM,CAAC,EAAG;YAAAI,QAAA,EAChFW,IAAI,CAACf,MAAM,KAAK,QAAQ,gBACvB7B,OAAA,CAACb,MAAM,CAAC+C,GAAG;cACTC,OAAO,EAAE;gBAAEC,MAAM,EAAE;cAAI,CAAE;cACzBC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,CAAC;gBAAEC,MAAM,EAAEC,QAAQ;gBAAEC,IAAI,EAAE;cAAS,CAAE;cAAAR,QAAA,EAE7DZ,WAAW,CAACuB,IAAI,CAACU,EAAE;YAAC;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,GACXiB,IAAI,CAACf,MAAM,KAAK,WAAW,gBAC7B7B,OAAA,CAACb,MAAM,CAAC+C,GAAG;cACTY,OAAO,EAAE;gBAAEM,KAAK,EAAE;cAAE,CAAE;cACtBjB,OAAO,EAAE;gBAAEiB,KAAK,EAAE;cAAE,CAAE;cACtBf,UAAU,EAAE;gBAAEkB,IAAI,EAAE,QAAQ;gBAAEC,SAAS,EAAE,GAAG;gBAAEC,OAAO,EAAE;cAAG,CAAE;cAAAxB,QAAA,eAE5DjC,OAAA,CAACV,WAAW;gBAACiC,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,GACXiB,IAAI,CAACf,MAAM,KAAK,OAAO,gBACzB7B,OAAA,CAACJ,WAAW;cAAC2B,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAEnCN,WAAW,CAACuB,IAAI,CAACU,EAAE;UACpB;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN3B,OAAA;YAAKuB,SAAS,EAAC,gBAAgB;YAAAU,QAAA,gBAC7BjC,OAAA;cAAIuB,SAAS,EAAC,wBAAwB;cAAAU,QAAA,EAAEW,IAAI,CAACc;YAAK;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxD3B,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAU,QAAA,EAAEW,IAAI,CAACe;YAAW;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAGhE3B,OAAA,CAACZ,eAAe;cAAA6C,QAAA,EACbW,IAAI,CAACf,MAAM,KAAK,QAAQ,IAAIe,IAAI,CAACgB,QAAQ,KAAKC,SAAS,iBACtD7D,OAAA,CAACb,MAAM,CAAC+C,GAAG;gBACTX,SAAS,EAAC,MAAM;gBAChBuB,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEe,MAAM,EAAE;gBAAE,CAAE;gBACnC3B,OAAO,EAAE;kBAAEY,OAAO,EAAE,CAAC;kBAAEe,MAAM,EAAE;gBAAO,CAAE;gBACxCC,IAAI,EAAE;kBAAEhB,OAAO,EAAE,CAAC;kBAAEe,MAAM,EAAE;gBAAE,CAAE;gBAAA7B,QAAA,gBAEhCjC,OAAA;kBAAKuB,SAAS,EAAC,8DAA8D;kBAAAU,QAAA,gBAC3EjC,OAAA;oBAAAiC,QAAA,EAAM;kBAAM;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnB3B,OAAA;oBAAAiC,QAAA,GAAOW,IAAI,CAACgB,QAAQ,EAAC,GAAC;kBAAA;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACN3B,OAAA;kBAAKuB,SAAS,EAAC,qCAAqC;kBAAAU,QAAA,eAClDjC,OAAA,CAACb,MAAM,CAAC+C,GAAG;oBACTX,SAAS,EAAC,+BAA+B;oBACzCuB,OAAO,EAAE;sBAAEkB,KAAK,EAAE;oBAAE,CAAE;oBACtB7B,OAAO,EAAE;sBAAE6B,KAAK,EAAE,GAAGpB,IAAI,CAACgB,QAAQ;oBAAI,CAAE;oBACxCvB,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI;kBAAE;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC,EAGjBiB,IAAI,CAACf,MAAM,KAAK,WAAW,iBAC1B7B,OAAA,CAACb,MAAM,CAAC+C,GAAG;cACTX,SAAS,EAAC,0DAA0D;cACpEuB,OAAO,EAAE;gBAAEC,OAAO,EAAE;cAAE,CAAE;cACxBZ,OAAO,EAAE;gBAAEY,OAAO,EAAE;cAAE,CAAE;cAAAd,QAAA,gBAExBjC,OAAA,CAACL,KAAK;gBAAC4B,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7B3B,OAAA;gBAAAiC,QAAA,GAAM,qBAAI,EAAC,IAAIgC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CACb,EAGAiB,IAAI,CAACU,EAAE,KAAK,UAAU,IAAIV,IAAI,CAACf,MAAM,KAAK,WAAW,iBACpD7B,OAAA,CAACb,MAAM,CAAC+C,GAAG;cACTX,SAAS,EAAC,gEAAgE;cAC1EuB,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEoB,CAAC,EAAE;cAAG,CAAE;cAC/BhC,OAAO,EAAE;gBAAEY,OAAO,EAAE,CAAC;gBAAEoB,CAAC,EAAE;cAAE,CAAE;cAC9B9B,UAAU,EAAE;gBAAEY,KAAK,EAAE;cAAI,CAAE;cAAAhB,QAAA,gBAE3BjC,OAAA;gBAAIuB,SAAS,EAAC,6DAA6D;gBAAAU,QAAA,gBACzEjC,OAAA,CAACV,WAAW;kBAACiC,SAAS,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wCAE1C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEL3B,OAAA;gBAAKuB,SAAS,EAAC,WAAW;gBAAAU,QAAA,gBACxBjC,OAAA;kBAAKuB,SAAS,EAAC,+BAA+B;kBAAAU,QAAA,eAC5CjC,OAAA;oBAAKuB,SAAS,EAAC,mCAAmC;oBAAAU,QAAA,gBAChDjC,OAAA;sBAAKuB,SAAS,EAAC,6BAA6B;sBAAAU,QAAA,gBAC1CjC,OAAA,CAACP,QAAQ;wBAAC8B,SAAS,EAAC;sBAAsB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7C3B,OAAA;wBAAMuB,SAAS,EAAC,uBAAuB;wBAAAU,QAAA,EAAC;sBAAY;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACN3B,OAAA;sBAAMuB,SAAS,EAAC,uBAAuB;sBAAAU,QAAA,EAAC;oBAAM;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN3B,OAAA;kBACEoE,OAAO,EAAE3D,sBAAuB;kBAChCc,SAAS,EAAC,6LAA6L;kBAAAU,QAAA,gBAEvMjC,OAAA,CAACN,QAAQ;oBAAC6B,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gEAEvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA,GA1HRiB,IAAI,CAACU,EAAE;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA2HF,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN3B,OAAA,CAACb,MAAM,CAAC+C,GAAG;MACTX,SAAS,EAAC,oBAAoB;MAC9BuB,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEoB,CAAC,EAAE;MAAG,CAAE;MAC/BhC,OAAO,EAAE;QAAEY,OAAO,EAAE,CAAC;QAAEoB,CAAC,EAAE;MAAE,CAAE;MAC9B9B,UAAU,EAAE;QAAEY,KAAK,EAAE;MAAI,CAAE;MAAAhB,QAAA,gBAE3BjC,OAAA;QAAKuB,SAAS,EAAC,8DAA8D;QAAAU,QAAA,gBAC3EjC,OAAA;UAAAiC,QAAA,EAAM;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjB3B,OAAA;UAAAiC,QAAA,GAAOoC,IAAI,CAACC,KAAK,CAACnE,YAAY,CAACoE,eAAe,CAAC,EAAC,GAAC;QAAA;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eACN3B,OAAA;QAAKuB,SAAS,EAAC,qCAAqC;QAAAU,QAAA,eAClDjC,OAAA,CAACb,MAAM,CAAC+C,GAAG;UACTX,SAAS,EAAC,+BAA+B;UACzCuB,OAAO,EAAE;YAAEkB,KAAK,EAAE;UAAE,CAAE;UACtB7B,OAAO,EAAE;YAAE6B,KAAK,EAAE,GAAG7D,YAAY,CAACoE,eAAe;UAAI,CAAE;UACvDlC,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACzB,EAAA,CAlOID,WAAqB;EAAA,QACAH,qBAAqB;AAAA;AAAA0E,EAAA,GAD1CvE,WAAqB;AAoO3B,eAAeA,WAAW;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}