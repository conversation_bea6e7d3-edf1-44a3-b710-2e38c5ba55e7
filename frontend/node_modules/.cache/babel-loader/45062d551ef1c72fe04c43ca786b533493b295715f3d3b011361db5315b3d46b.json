{"ast": null, "code": "/**\n * lucide-react v0.292.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BookCopy = createLucideIcon(\"BookCopy\", [[\"path\", {\n  d: \"M2 16V4a2 2 0 0 1 2-2h11\",\n  key: \"spzkk5\"\n}], [\"path\", {\n  d: \"M5 14H4a2 2 0 1 0 0 4h1\",\n  key: \"16gqf9\"\n}], [\"path\", {\n  d: \"M22 18H11a2 2 0 1 0 0 4h11V6H11a2 2 0 0 0-2 2v12\",\n  key: \"1owzki\"\n}]]);\nexport { BookCopy as default };", "map": {"version": 3, "names": ["BookCopy", "createLucideIcon", "d", "key"], "sources": ["D:\\pyworkspace\\analysisFlow\\frontend\\node_modules\\lucide-react\\src\\icons\\book-copy.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BookCopy\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAxNlY0YTIgMiAwIDAgMSAyLTJoMTEiIC8+CiAgPHBhdGggZD0iTTUgMTRINGEyIDIgMCAxIDAgMCA0aDEiIC8+CiAgPHBhdGggZD0iTTIyIDE4SDExYTIgMiAwIDEgMCAwIDRoMTFWNkgxMWEyIDIgMCAwIDAtMiAydjEyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/book-copy\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookCopy = createLucideIcon('BookCopy', [\n  ['path', { d: 'M2 16V4a2 2 0 0 1 2-2h11', key: 'spzkk5' }],\n  ['path', { d: 'M5 14H4a2 2 0 1 0 0 4h1', key: '16gqf9' }],\n  [\n    'path',\n    { d: 'M22 18H11a2 2 0 1 0 0 4h11V6H11a2 2 0 0 0-2 2v12', key: '1owzki' },\n  ],\n]);\n\nexport default BookCopy;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CACE,QACA;EAAED,CAAA,EAAG,kDAAoD;EAAAC,GAAA,EAAK;AAAS,EACzE,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}