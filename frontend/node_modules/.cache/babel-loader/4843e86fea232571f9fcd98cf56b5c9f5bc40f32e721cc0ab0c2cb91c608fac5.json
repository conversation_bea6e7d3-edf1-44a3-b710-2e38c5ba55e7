{"ast": null, "code": "/**\n * lucide-react v0.292.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Aperture = createLucideIcon(\"Aperture\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"line\", {\n  x1: \"14.31\",\n  x2: \"20.05\",\n  y1: \"8\",\n  y2: \"17.94\",\n  key: \"jdes2e\"\n}], [\"line\", {\n  x1: \"9.69\",\n  x2: \"21.17\",\n  y1: \"8\",\n  y2: \"8\",\n  key: \"1gubuk\"\n}], [\"line\", {\n  x1: \"7.38\",\n  x2: \"13.12\",\n  y1: \"12\",\n  y2: \"2.06\",\n  key: \"1m4d1n\"\n}], [\"line\", {\n  x1: \"9.69\",\n  x2: \"3.95\",\n  y1: \"16\",\n  y2: \"6.06\",\n  key: \"1wye2p\"\n}], [\"line\", {\n  x1: \"14.31\",\n  x2: \"2.83\",\n  y1: \"16\",\n  y2: \"16\",\n  key: \"1l9f4x\"\n}], [\"line\", {\n  x1: \"16.62\",\n  x2: \"10.88\",\n  y1: \"12\",\n  y2: \"21.94\",\n  key: \"1jjvfs\"\n}]]);\nexport { Aperture as default };\n//# sourceMappingURL=aperture.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}