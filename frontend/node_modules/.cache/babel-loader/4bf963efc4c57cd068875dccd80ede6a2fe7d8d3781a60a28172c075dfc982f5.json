{"ast": null, "code": "// 简单的事件发射器，用于组件间通信\nclass EventEmitter {\n  constructor() {\n    this.events = {};\n  }\n  on(event, callback) {\n    if (!this.events[event]) {\n      this.events[event] = [];\n    }\n    this.events[event].push(callback);\n  }\n  off(event, callback) {\n    if (!this.events[event]) return;\n    this.events[event] = this.events[event].filter(cb => cb !== callback);\n  }\n  emit(event, ...args) {\n    if (!this.events[event]) return;\n    this.events[event].forEach(callback => callback(...args));\n  }\n}\nexport const eventEmitter = new EventEmitter();\n\n// 事件常量\nexport const EVENTS = {\n  RESET_ALL: 'reset_all'\n};", "map": {"version": 3, "names": ["EventEmitter", "constructor", "events", "on", "event", "callback", "push", "off", "filter", "cb", "emit", "args", "for<PERSON>ach", "eventEmitter", "EVENTS", "RESET_ALL"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/utils/eventEmitter.ts"], "sourcesContent": ["// 简单的事件发射器，用于组件间通信\nclass EventEmitter {\n  private events: { [key: string]: Function[] } = {};\n\n  on(event: string, callback: Function) {\n    if (!this.events[event]) {\n      this.events[event] = [];\n    }\n    this.events[event].push(callback);\n  }\n\n  off(event: string, callback: Function) {\n    if (!this.events[event]) return;\n    this.events[event] = this.events[event].filter(cb => cb !== callback);\n  }\n\n  emit(event: string, ...args: any[]) {\n    if (!this.events[event]) return;\n    this.events[event].forEach(callback => callback(...args));\n  }\n}\n\nexport const eventEmitter = new EventEmitter();\n\n// 事件常量\nexport const EVENTS = {\n  RESET_ALL: 'reset_all'\n};\n"], "mappings": "AAAA;AACA,MAAMA,YAAY,CAAC;EAAAC,YAAA;IAAA,KACTC,MAAM,GAAkC,CAAC,CAAC;EAAA;EAElDC,EAAEA,CAACC,KAAa,EAAEC,QAAkB,EAAE;IACpC,IAAI,CAAC,IAAI,CAACH,MAAM,CAACE,KAAK,CAAC,EAAE;MACvB,IAAI,CAACF,MAAM,CAACE,KAAK,CAAC,GAAG,EAAE;IACzB;IACA,IAAI,CAACF,MAAM,CAACE,KAAK,CAAC,CAACE,IAAI,CAACD,QAAQ,CAAC;EACnC;EAEAE,GAAGA,CAACH,KAAa,EAAEC,QAAkB,EAAE;IACrC,IAAI,CAAC,IAAI,CAACH,MAAM,CAACE,KAAK,CAAC,EAAE;IACzB,IAAI,CAACF,MAAM,CAACE,KAAK,CAAC,GAAG,IAAI,CAACF,MAAM,CAACE,KAAK,CAAC,CAACI,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKJ,QAAQ,CAAC;EACvE;EAEAK,IAAIA,CAACN,KAAa,EAAE,GAAGO,IAAW,EAAE;IAClC,IAAI,CAAC,IAAI,CAACT,MAAM,CAACE,KAAK,CAAC,EAAE;IACzB,IAAI,CAACF,MAAM,CAACE,KAAK,CAAC,CAACQ,OAAO,CAACP,QAAQ,IAAIA,QAAQ,CAAC,GAAGM,IAAI,CAAC,CAAC;EAC3D;AACF;AAEA,OAAO,MAAME,YAAY,GAAG,IAAIb,YAAY,CAAC,CAAC;;AAE9C;AACA,OAAO,MAAMc,MAAM,GAAG;EACpBC,SAAS,EAAE;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}