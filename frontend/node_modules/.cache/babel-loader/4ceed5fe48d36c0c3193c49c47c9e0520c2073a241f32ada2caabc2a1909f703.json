{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\components\\\\Header.tsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Brain, Zap } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  return /*#__PURE__*/_jsxDEV(motion.header, {\n    className: \"bg-tech-blue/30 backdrop-blur-sm border-b border-tech-cyan/20\",\n    initial: {\n      opacity: 0,\n      y: -20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    transition: {\n      duration: 0.5\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"p-2 bg-gradient-to-r from-tech-cyan to-tech-purple rounded-lg\",\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: /*#__PURE__*/_jsxDEV(Brain, {\n              className: \"w-8 h-8 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold glow-text\",\n              children: \"\\u667A\\u80FD\\u6587\\u672C\\u5206\\u6790\\u5DE5\\u5177\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-sm\",\n              children: \"Intelligent Text Analysis Tool\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"flex items-center space-x-2 text-tech-cyan\",\n            animate: {\n              opacity: [0.5, 1, 0.5]\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity\n            },\n            children: [/*#__PURE__*/_jsxDEV(Zap, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium\",\n              children: \"AI \\u667A\\u80FD\\u5206\\u6790\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-400\",\n              children: \"\\u72B6\\u6001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-tech-green rounded-full animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-tech-green text-sm font-medium\",\n                children: \"\\u7CFB\\u7EDF\\u6B63\\u5E38\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "motion", "Brain", "Zap", "jsxDEV", "_jsxDEV", "Header", "header", "className", "initial", "opacity", "y", "animate", "transition", "duration", "children", "div", "whileHover", "scale", "whileTap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "repeat", "Infinity", "_c", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/components/Header.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Brain, Zap } from 'lucide-react';\n\nconst Header: React.FC = () => {\n  return (\n    <motion.header \n      className=\"bg-tech-blue/30 backdrop-blur-sm border-b border-tech-cyan/20\"\n      initial={{ opacity: 0, y: -20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <motion.div\n              className=\"p-2 bg-gradient-to-r from-tech-cyan to-tech-purple rounded-lg\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <Brain className=\"w-8 h-8 text-white\" />\n            </motion.div>\n            <div>\n              <h1 className=\"text-2xl font-bold glow-text\">\n                智能文本分析工具\n              </h1>\n              <p className=\"text-gray-400 text-sm\">\n                Intelligent Text Analysis Tool\n              </p>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            <motion.div \n              className=\"flex items-center space-x-2 text-tech-cyan\"\n              animate={{ opacity: [0.5, 1, 0.5] }}\n              transition={{ duration: 2, repeat: Infinity }}\n            >\n              <Zap className=\"w-5 h-5\" />\n              <span className=\"text-sm font-medium\">AI 智能分析</span>\n            </motion.div>\n            \n            <div className=\"text-right\">\n              <div className=\"text-sm text-gray-400\">状态</div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-tech-green rounded-full animate-pulse\"></div>\n                <span className=\"text-tech-green text-sm font-medium\">系统正常</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </motion.header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,KAAK,EAAEC,GAAG,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAC7B,oBACED,OAAA,CAACJ,MAAM,CAACM,MAAM;IACZC,SAAS,EAAC,+DAA+D;IACzEC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;IAAG,CAAE;IAChCC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,eAE9BV,OAAA;MAAKG,SAAS,EAAC,6BAA6B;MAAAO,QAAA,eAC1CV,OAAA;QAAKG,SAAS,EAAC,mCAAmC;QAAAO,QAAA,gBAChDV,OAAA;UAAKG,SAAS,EAAC,6BAA6B;UAAAO,QAAA,gBAC1CV,OAAA,CAACJ,MAAM,CAACe,GAAG;YACTR,SAAS,EAAC,+DAA+D;YACzES,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAAAH,QAAA,eAE1BV,OAAA,CAACH,KAAK;cAACM,SAAS,EAAC;YAAoB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACblB,OAAA;YAAAU,QAAA,gBACEV,OAAA;cAAIG,SAAS,EAAC,8BAA8B;cAAAO,QAAA,EAAC;YAE7C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlB,OAAA;cAAGG,SAAS,EAAC,uBAAuB;cAAAO,QAAA,EAAC;YAErC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlB,OAAA;UAAKG,SAAS,EAAC,6BAA6B;UAAAO,QAAA,gBAC1CV,OAAA,CAACJ,MAAM,CAACe,GAAG;YACTR,SAAS,EAAC,4CAA4C;YACtDI,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;YAAE,CAAE;YACpCG,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAEU,MAAM,EAAEC;YAAS,CAAE;YAAAV,QAAA,gBAE9CV,OAAA,CAACF,GAAG;cAACK,SAAS,EAAC;YAAS;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3BlB,OAAA;cAAMG,SAAS,EAAC,qBAAqB;cAAAO,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eAEblB,OAAA;YAAKG,SAAS,EAAC,YAAY;YAAAO,QAAA,gBACzBV,OAAA;cAAKG,SAAS,EAAC,uBAAuB;cAAAO,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/ClB,OAAA;cAAKG,SAAS,EAAC,6BAA6B;cAAAO,QAAA,gBAC1CV,OAAA;gBAAKG,SAAS,EAAC;cAAkD;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxElB,OAAA;gBAAMG,SAAS,EAAC,qCAAqC;gBAAAO,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB,CAAC;AAACG,EAAA,GAlDIpB,MAAgB;AAoDtB,eAAeA,MAAM;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}