{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useCallback } from 'react';\nconst initialSteps = [{\n  id: 'upload',\n  title: '文件上传',\n  description: '上传入户数据和投诉数据文件',\n  status: 'pending'\n}, {\n  id: 'validate',\n  title: '内容检测',\n  description: '验证文件格式和必要字段',\n  status: 'pending'\n}, {\n  id: 'configure',\n  title: '解析配置',\n  description: '配置智能解析规则',\n  status: 'pending'\n}, {\n  id: 'analyze',\n  title: '智能分析',\n  description: '提取转写文本并进行智能解析',\n  status: 'pending'\n}, {\n  id: 'generate',\n  title: '报表生成',\n  description: '生成分析报表和可视化图表',\n  status: 'pending'\n}, {\n  id: 'download',\n  title: '结果下载',\n  description: '下载分析结果和报表文件',\n  status: 'pending'\n}];\nexport const useProcessFlow = () => {\n  _s();\n  const [processState, setProcessState] = useState({\n    currentStep: '',\n    steps: initialSteps,\n    overallProgress: 0,\n    fileProgresses: []\n  });\n\n  // 智能分析状态\n  const [analysisStage, setAnalysisStage] = useState(null);\n\n  // 激活某个步骤\n  const activateStep = useCallback(stepId => {\n    setProcessState(prev => ({\n      ...prev,\n      currentStep: stepId,\n      steps: prev.steps.map(step => ({\n        ...step,\n        status: step.id === stepId ? 'active' : step.status === 'active' ? 'pending' : step.status,\n        progress: step.id === stepId ? 0 : step.progress\n      }))\n    }));\n  }, []);\n\n  // 更新步骤进度\n  const updateStepProgress = useCallback((stepId, progress) => {\n    setProcessState(prev => ({\n      ...prev,\n      steps: prev.steps.map(step => step.id === stepId ? {\n        ...step,\n        progress\n      } : step)\n    }));\n  }, []);\n\n  // 完成某个步骤\n  const completeStep = useCallback(stepId => {\n    setProcessState(prev => {\n      const updatedSteps = prev.steps.map(step => step.id === stepId ? {\n        ...step,\n        status: 'completed',\n        progress: 100\n      } : step);\n\n      // 重新计算总体进度：基于步骤完成情况和文件进度\n      const completedSteps = updatedSteps.filter(s => s.status === 'completed').length;\n      const activeSteps = updatedSteps.filter(s => s.status === 'active');\n\n      // 计算活跃步骤的平均进度\n      const activeStepsProgress = activeSteps.reduce((sum, step) => sum + step.progress, 0);\n      const totalStepsProgress = completedSteps * 100 + activeStepsProgress;\n      const overallProgress = Math.round(totalStepsProgress / updatedSteps.length);\n      return {\n        ...prev,\n        steps: updatedSteps,\n        overallProgress: Math.min(100, overallProgress)\n      };\n    });\n  }, []);\n\n  // 设置步骤错误\n  const setStepError = useCallback((stepId, errorMessage) => {\n    setProcessState(prev => ({\n      ...prev,\n      steps: prev.steps.map(step => step.id === stepId ? {\n        ...step,\n        status: 'error',\n        progress: 0\n      } : step)\n    }));\n  }, []);\n\n  // 添加文件进度跟踪\n  const addFileProgress = useCallback((fileId, fileName) => {\n    setProcessState(prev => ({\n      ...prev,\n      fileProgresses: [...prev.fileProgresses.filter(fp => fp.fileId !== fileId), {\n        fileId,\n        fileName,\n        currentStep: 'upload',\n        stepProgress: 0\n      }]\n    }));\n  }, []);\n\n  // 更新文件进度\n  const updateFileProgress = useCallback((fileId, stepId, progress) => {\n    setProcessState(prev => {\n      const updatedFileProgresses = prev.fileProgresses.map(fp => fp.fileId === fileId ? {\n        ...fp,\n        currentStep: stepId,\n        stepProgress: progress\n      } : fp);\n\n      // 计算整体进度\n      const totalFiles = updatedFileProgresses.length;\n      if (totalFiles === 0) return prev;\n\n      // 更新步骤状态\n      const updatedSteps = prev.steps.map(step => {\n        const filesInThisStep = updatedFileProgresses.filter(fp => fp.currentStep === step.id);\n        const filesCompletedThisStep = updatedFileProgresses.filter(fp => {\n          const stepWeight = stepWeights[fp.currentStep] || 1;\n          const currentStepWeight = stepWeights[step.id] || 1;\n          return stepWeight > currentStepWeight || stepWeight === currentStepWeight && fp.stepProgress === 100;\n        });\n        if (step.id === 'validate') {\n          // 验证步骤特殊处理：基于已验证文件数量计算进度\n          const validatedFiles = updatedFileProgresses.filter(fp => fp.currentStep === 'validate' && fp.stepProgress === 100).length;\n          const validatingFiles = updatedFileProgresses.filter(fp => fp.currentStep === 'validate' && fp.stepProgress > 0 && fp.stepProgress < 100).length;\n          if (validatedFiles === totalFiles && totalFiles > 0) {\n            return {\n              ...step,\n              status: 'completed',\n              progress: 100\n            };\n          } else if (validatedFiles > 0 || validatingFiles > 0) {\n            const progress = totalFiles > 0 ? Math.round(validatedFiles / totalFiles * 100) : 0;\n            return {\n              ...step,\n              status: 'active',\n              progress\n            };\n          } else {\n            return {\n              ...step,\n              status: 'pending',\n              progress: 0\n            };\n          }\n        } else if (step.id === 'analyze') {\n          // 智能分析步骤特殊处理：基于已分析文件数量计算进度\n          const analyzedFiles = updatedFileProgresses.filter(fp => fp.currentStep === 'analyze' && fp.stepProgress === 100).length;\n          const analyzingFiles = updatedFileProgresses.filter(fp => fp.currentStep === 'analyze' && fp.stepProgress > 0 && fp.stepProgress < 100).length;\n          if (analyzedFiles === totalFiles && totalFiles > 0) {\n            return {\n              ...step,\n              status: 'completed',\n              progress: 100\n            };\n          } else if (analyzedFiles > 0 || analyzingFiles > 0) {\n            const progress = totalFiles > 0 ? Math.round(analyzedFiles / totalFiles * 100) : 0;\n            return {\n              ...step,\n              status: 'active',\n              progress\n            };\n          } else {\n            return {\n              ...step,\n              status: 'pending',\n              progress: 0\n            };\n          }\n        } else if (filesInThisStep.length > 0) {\n          const avgProgress = filesInThisStep.reduce((sum, fp) => sum + fp.stepProgress, 0) / filesInThisStep.length;\n          return {\n            ...step,\n            status: 'active',\n            progress: Math.round(avgProgress)\n          };\n        } else if (filesCompletedThisStep.length === totalFiles && totalFiles > 0) {\n          return {\n            ...step,\n            status: 'completed',\n            progress: 100\n          };\n        } else {\n          return {\n            ...step,\n            status: 'pending',\n            progress: 0\n          };\n        }\n      });\n      return {\n        ...prev,\n        fileProgresses: updatedFileProgresses,\n        steps: updatedSteps,\n        overallProgress: Math.round(overallProgress)\n      };\n    });\n  }, []);\n\n  // 重置所有步骤\n  const resetFlow = useCallback(() => {\n    setProcessState({\n      currentStep: '',\n      steps: initialSteps,\n      overallProgress: 0,\n      fileProgresses: []\n    });\n    // 重置智能分析状态\n    setAnalysisStage(null);\n  }, []);\n\n  // 完整重置函数（供外部调用）\n  const resetAll = useCallback(() => {\n    resetFlow();\n  }, [resetFlow]);\n\n  // 文件上传时的联动\n  const onFileUploaded = useCallback((fileId, fileName) => {\n    addFileProgress(fileId, fileName);\n    updateFileProgress(fileId, 'upload', 100);\n  }, [addFileProgress, updateFileProgress]);\n\n  // 文件验证时的联动\n  const onFileValidation = useCallback(fileId => {\n    updateFileProgress(fileId, 'validate', 0);\n\n    // 模拟验证过程\n    let progress = 0;\n    const interval = setInterval(() => {\n      progress += 20;\n      updateFileProgress(fileId, 'validate', progress);\n      if (progress >= 100) {\n        clearInterval(interval);\n      }\n    }, 200);\n  }, [updateFileProgress]);\n\n  // 文件处理时的联动（用于一键处理全部，跳过已完成的验证步骤）\n  const onFileProcessing = useCallback(fileId => {\n    // 检查文件当前状态，如果已经验证过，直接从配置步骤开始\n    const fileProgress = processState.fileProgresses.find(fp => fp.fileId === fileId);\n\n    // 如果文件已经完成验证，直接从配置开始\n    if (!fileProgress || fileProgress.currentStep === 'validate') {\n      updateFileProgress(fileId, 'configure', 0);\n    }\n\n    // 模拟配置过程\n    let configProgress = 0;\n    const configInterval = setInterval(() => {\n      configProgress += 25;\n      updateFileProgress(fileId, 'configure', configProgress);\n      if (configProgress >= 100) {\n        clearInterval(configInterval);\n\n        // 开始分析过程\n        updateFileProgress(fileId, 'analyze', 0);\n        let analyzeProgress = 0;\n        const analyzeInterval = setInterval(() => {\n          analyzeProgress += 10;\n          updateFileProgress(fileId, 'analyze', analyzeProgress);\n          if (analyzeProgress >= 100) {\n            clearInterval(analyzeInterval);\n          }\n        }, 300);\n      }\n    }, 250);\n  }, [updateFileProgress, processState.fileProgresses]);\n\n  // 报表生成时的联动\n  const onReportGeneration = useCallback(() => {\n    // 激活报表生成步骤\n    activateStep('generate');\n\n    // 为所有已处理的文件生成报表\n    processState.fileProgresses.forEach(fp => {\n      if (fp.currentStep === 'analyze' && fp.stepProgress === 100) {\n        updateFileProgress(fp.fileId, 'generate', 0);\n\n        // 模拟报表生成过程\n        let progress = 0;\n        const interval = setInterval(() => {\n          progress += 15;\n          updateFileProgress(fp.fileId, 'generate', progress);\n          if (progress >= 100) {\n            clearInterval(interval);\n            updateFileProgress(fp.fileId, 'download', 100);\n          }\n        }, 400);\n      }\n    });\n\n    // 模拟报表生成完成后自动激活下载步骤\n    setTimeout(() => {\n      completeStep('generate');\n      activateStep('download');\n\n      // 再延迟一点完成下载步骤，显示下载界面\n      setTimeout(() => {\n        completeStep('download');\n      }, 1000);\n    }, 6000); // 6秒后完成报表生成\n  }, [processState.fileProgresses, updateFileProgress, activateStep, completeStep]);\n  return {\n    processState,\n    activateStep,\n    updateStepProgress,\n    completeStep,\n    setStepError,\n    resetFlow,\n    addFileProgress,\n    updateFileProgress,\n    // 联动方法\n    onFileUploaded,\n    onFileValidation,\n    onFileProcessing,\n    onReportGeneration,\n    // 智能分析状态\n    analysisStage,\n    setAnalysisStage,\n    // 完整重置\n    resetAll\n  };\n};\n_s(useProcessFlow, \"VK8cAvakihhftavuOeqtDCuFR1g=\");", "map": {"version": 3, "names": ["useState", "useCallback", "initialSteps", "id", "title", "description", "status", "useProcessFlow", "_s", "processState", "setProcessState", "currentStep", "steps", "overallProgress", "fileProgresses", "analysisStage", "setAnalysisStage", "activateStep", "stepId", "prev", "map", "step", "progress", "updateStepProgress", "completeStep", "updatedSteps", "completedSteps", "filter", "s", "length", "activeSteps", "activeStepsProgress", "reduce", "sum", "totalStepsProgress", "Math", "round", "min", "setStepError", "errorMessage", "addFileProgress", "fileId", "fileName", "fp", "stepProgress", "updateFileProgress", "updatedFileProgresses", "totalFiles", "filesInThisStep", "filesCompletedThisStep", "step<PERSON><PERSON>ght", "stepWeights", "currentStepWeight", "validatedFiles", "validatingFiles", "analyzedFiles", "analyzingFiles", "avgProgress", "resetFlow", "resetAll", "onFileUploaded", "onFileValidation", "interval", "setInterval", "clearInterval", "onFileProcessing", "fileProgress", "find", "configProgress", "configInterval", "analyzeProgress", "analyzeInterval", "onReportGeneration", "for<PERSON>ach", "setTimeout"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/hooks/useProcessFlow.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\n\nexport interface ProcessStep {\n  id: string;\n  title: string;\n  description: string;\n  status: 'pending' | 'active' | 'completed' | 'error';\n  progress?: number;\n}\n\nexport interface FileProgress {\n  fileId: string;\n  fileName: string;\n  currentStep: string;\n  stepProgress: number;\n}\n\nexport interface ProcessFlowState {\n  currentStep: string;\n  steps: ProcessStep[];\n  overallProgress: number;\n  fileProgresses: FileProgress[];\n}\n\nconst initialSteps: ProcessStep[] = [\n  {\n    id: 'upload',\n    title: '文件上传',\n    description: '上传入户数据和投诉数据文件',\n    status: 'pending'\n  },\n  {\n    id: 'validate',\n    title: '内容检测',\n    description: '验证文件格式和必要字段',\n    status: 'pending'\n  },\n  {\n    id: 'configure',\n    title: '解析配置',\n    description: '配置智能解析规则',\n    status: 'pending'\n  },\n  {\n    id: 'analyze',\n    title: '智能分析',\n    description: '提取转写文本并进行智能解析',\n    status: 'pending'\n  },\n  {\n    id: 'generate',\n    title: '报表生成',\n    description: '生成分析报表和可视化图表',\n    status: 'pending'\n  },\n  {\n    id: 'download',\n    title: '结果下载',\n    description: '下载分析结果和报表文件',\n    status: 'pending'\n  }\n];\n\nexport const useProcessFlow = () => {\n  const [processState, setProcessState] = useState<ProcessFlowState>({\n    currentStep: '',\n    steps: initialSteps,\n    overallProgress: 0,\n    fileProgresses: []\n  });\n\n  // 智能分析状态\n  const [analysisStage, setAnalysisStage] = useState<'configure' | 'analyze' | 'generate' | null>(null);\n\n  // 激活某个步骤\n  const activateStep = useCallback((stepId: string) => {\n    setProcessState(prev => ({\n      ...prev,\n      currentStep: stepId,\n      steps: prev.steps.map(step => ({\n        ...step,\n        status: step.id === stepId ? 'active' : \n                step.status === 'active' ? 'pending' : step.status,\n        progress: step.id === stepId ? 0 : step.progress\n      }))\n    }));\n  }, []);\n\n  // 更新步骤进度\n  const updateStepProgress = useCallback((stepId: string, progress: number) => {\n    setProcessState(prev => ({\n      ...prev,\n      steps: prev.steps.map(step => \n        step.id === stepId ? { ...step, progress } : step\n      )\n    }));\n  }, []);\n\n  // 完成某个步骤\n  const completeStep = useCallback((stepId: string) => {\n    setProcessState(prev => {\n      const updatedSteps = prev.steps.map(step =>\n        step.id === stepId ? { ...step, status: 'completed' as const, progress: 100 } : step\n      );\n\n      // 重新计算总体进度：基于步骤完成情况和文件进度\n      const completedSteps = updatedSteps.filter(s => s.status === 'completed').length;\n      const activeSteps = updatedSteps.filter(s => s.status === 'active');\n\n      // 计算活跃步骤的平均进度\n      const activeStepsProgress = activeSteps.reduce((sum, step) => sum + step.progress, 0);\n      const totalStepsProgress = completedSteps * 100 + activeStepsProgress;\n      const overallProgress = Math.round(totalStepsProgress / updatedSteps.length);\n\n      return {\n        ...prev,\n        steps: updatedSteps,\n        overallProgress: Math.min(100, overallProgress)\n      };\n    });\n  }, []);\n\n  // 设置步骤错误\n  const setStepError = useCallback((stepId: string, errorMessage?: string) => {\n    setProcessState(prev => ({\n      ...prev,\n      steps: prev.steps.map(step => \n        step.id === stepId ? { \n          ...step, \n          status: 'error' as const, \n          progress: 0 \n        } : step\n      )\n    }));\n  }, []);\n\n  // 添加文件进度跟踪\n  const addFileProgress = useCallback((fileId: string, fileName: string) => {\n    setProcessState(prev => ({\n      ...prev,\n      fileProgresses: [\n        ...prev.fileProgresses.filter(fp => fp.fileId !== fileId),\n        {\n          fileId,\n          fileName,\n          currentStep: 'upload',\n          stepProgress: 0\n        }\n      ]\n    }));\n  }, []);\n\n  // 更新文件进度\n  const updateFileProgress = useCallback((fileId: string, stepId: string, progress: number) => {\n    setProcessState(prev => {\n      const updatedFileProgresses = prev.fileProgresses.map(fp =>\n        fp.fileId === fileId ? { ...fp, currentStep: stepId, stepProgress: progress } : fp\n      );\n\n      // 计算整体进度\n      const totalFiles = updatedFileProgresses.length;\n      if (totalFiles === 0) return prev;\n\n      // 更新步骤状态\n      const updatedSteps = prev.steps.map(step => {\n        const filesInThisStep = updatedFileProgresses.filter(fp => fp.currentStep === step.id);\n        const filesCompletedThisStep = updatedFileProgresses.filter(fp => {\n          const stepWeight = stepWeights[fp.currentStep as keyof typeof stepWeights] || 1;\n          const currentStepWeight = stepWeights[step.id as keyof typeof stepWeights] || 1;\n          return stepWeight > currentStepWeight || (stepWeight === currentStepWeight && fp.stepProgress === 100);\n        });\n\n        if (step.id === 'validate') {\n          // 验证步骤特殊处理：基于已验证文件数量计算进度\n          const validatedFiles = updatedFileProgresses.filter(fp =>\n            fp.currentStep === 'validate' && fp.stepProgress === 100\n          ).length;\n          const validatingFiles = updatedFileProgresses.filter(fp =>\n            fp.currentStep === 'validate' && fp.stepProgress > 0 && fp.stepProgress < 100\n          ).length;\n\n          if (validatedFiles === totalFiles && totalFiles > 0) {\n            return { ...step, status: 'completed' as const, progress: 100 };\n          } else if (validatedFiles > 0 || validatingFiles > 0) {\n            const progress = totalFiles > 0 ? Math.round((validatedFiles / totalFiles) * 100) : 0;\n            return { ...step, status: 'active' as const, progress };\n          } else {\n            return { ...step, status: 'pending' as const, progress: 0 };\n          }\n        } else if (step.id === 'analyze') {\n          // 智能分析步骤特殊处理：基于已分析文件数量计算进度\n          const analyzedFiles = updatedFileProgresses.filter(fp =>\n            fp.currentStep === 'analyze' && fp.stepProgress === 100\n          ).length;\n          const analyzingFiles = updatedFileProgresses.filter(fp =>\n            fp.currentStep === 'analyze' && fp.stepProgress > 0 && fp.stepProgress < 100\n          ).length;\n\n          if (analyzedFiles === totalFiles && totalFiles > 0) {\n            return { ...step, status: 'completed' as const, progress: 100 };\n          } else if (analyzedFiles > 0 || analyzingFiles > 0) {\n            const progress = totalFiles > 0 ? Math.round((analyzedFiles / totalFiles) * 100) : 0;\n            return { ...step, status: 'active' as const, progress };\n          } else {\n            return { ...step, status: 'pending' as const, progress: 0 };\n          }\n        } else if (filesInThisStep.length > 0) {\n          const avgProgress = filesInThisStep.reduce((sum, fp) => sum + fp.stepProgress, 0) / filesInThisStep.length;\n          return { ...step, status: 'active' as const, progress: Math.round(avgProgress) };\n        } else if (filesCompletedThisStep.length === totalFiles && totalFiles > 0) {\n          return { ...step, status: 'completed' as const, progress: 100 };\n        } else {\n          return { ...step, status: 'pending' as const, progress: 0 };\n        }\n      });\n\n      return {\n        ...prev,\n        fileProgresses: updatedFileProgresses,\n        steps: updatedSteps,\n        overallProgress: Math.round(overallProgress)\n      };\n    });\n  }, []);\n\n  // 重置所有步骤\n  const resetFlow = useCallback(() => {\n    setProcessState({\n      currentStep: '',\n      steps: initialSteps,\n      overallProgress: 0,\n      fileProgresses: []\n    });\n    // 重置智能分析状态\n    setAnalysisStage(null);\n  }, []);\n\n  // 完整重置函数（供外部调用）\n  const resetAll = useCallback(() => {\n    resetFlow();\n  }, [resetFlow]);\n\n  // 文件上传时的联动\n  const onFileUploaded = useCallback((fileId: string, fileName: string) => {\n    addFileProgress(fileId, fileName);\n    updateFileProgress(fileId, 'upload', 100);\n  }, [addFileProgress, updateFileProgress]);\n\n  // 文件验证时的联动\n  const onFileValidation = useCallback((fileId: string) => {\n    updateFileProgress(fileId, 'validate', 0);\n\n    // 模拟验证过程\n    let progress = 0;\n    const interval = setInterval(() => {\n      progress += 20;\n      updateFileProgress(fileId, 'validate', progress);\n      if (progress >= 100) {\n        clearInterval(interval);\n      }\n    }, 200);\n  }, [updateFileProgress]);\n\n  // 文件处理时的联动（用于一键处理全部，跳过已完成的验证步骤）\n  const onFileProcessing = useCallback((fileId: string) => {\n    // 检查文件当前状态，如果已经验证过，直接从配置步骤开始\n    const fileProgress = processState.fileProgresses.find(fp => fp.fileId === fileId);\n\n    // 如果文件已经完成验证，直接从配置开始\n    if (!fileProgress || fileProgress.currentStep === 'validate') {\n      updateFileProgress(fileId, 'configure', 0);\n    }\n\n    // 模拟配置过程\n    let configProgress = 0;\n    const configInterval = setInterval(() => {\n      configProgress += 25;\n      updateFileProgress(fileId, 'configure', configProgress);\n      if (configProgress >= 100) {\n        clearInterval(configInterval);\n\n        // 开始分析过程\n        updateFileProgress(fileId, 'analyze', 0);\n        let analyzeProgress = 0;\n        const analyzeInterval = setInterval(() => {\n          analyzeProgress += 10;\n          updateFileProgress(fileId, 'analyze', analyzeProgress);\n          if (analyzeProgress >= 100) {\n            clearInterval(analyzeInterval);\n          }\n        }, 300);\n      }\n    }, 250);\n  }, [updateFileProgress, processState.fileProgresses]);\n\n  // 报表生成时的联动\n  const onReportGeneration = useCallback(() => {\n    // 激活报表生成步骤\n    activateStep('generate');\n\n    // 为所有已处理的文件生成报表\n    processState.fileProgresses.forEach(fp => {\n      if (fp.currentStep === 'analyze' && fp.stepProgress === 100) {\n        updateFileProgress(fp.fileId, 'generate', 0);\n\n        // 模拟报表生成过程\n        let progress = 0;\n        const interval = setInterval(() => {\n          progress += 15;\n          updateFileProgress(fp.fileId, 'generate', progress);\n          if (progress >= 100) {\n            clearInterval(interval);\n            updateFileProgress(fp.fileId, 'download', 100);\n          }\n        }, 400);\n      }\n    });\n\n    // 模拟报表生成完成后自动激活下载步骤\n    setTimeout(() => {\n      completeStep('generate');\n      activateStep('download');\n\n      // 再延迟一点完成下载步骤，显示下载界面\n      setTimeout(() => {\n        completeStep('download');\n      }, 1000);\n    }, 6000); // 6秒后完成报表生成\n  }, [processState.fileProgresses, updateFileProgress, activateStep, completeStep]);\n\n  return {\n    processState,\n    activateStep,\n    updateStepProgress,\n    completeStep,\n    setStepError,\n    resetFlow,\n    addFileProgress,\n    updateFileProgress,\n    // 联动方法\n    onFileUploaded,\n    onFileValidation,\n    onFileProcessing,\n    onReportGeneration,\n    // 智能分析状态\n    analysisStage,\n    setAnalysisStage,\n    // 完整重置\n    resetAll\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAwB7C,MAAMC,YAA2B,GAAG,CAClC;EACEC,EAAE,EAAE,QAAQ;EACZC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,eAAe;EAC5BC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,UAAU;EACdC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,aAAa;EAC1BC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,WAAW;EACfC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,UAAU;EACvBC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,SAAS;EACbC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,eAAe;EAC5BC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,UAAU;EACdC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,cAAc;EAC3BC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,UAAU;EACdC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,aAAa;EAC1BC,MAAM,EAAE;AACV,CAAC,CACF;AAED,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAmB;IACjEW,WAAW,EAAE,EAAE;IACfC,KAAK,EAAEV,YAAY;IACnBW,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAA8C,IAAI,CAAC;;EAErG;EACA,MAAMiB,YAAY,GAAGhB,WAAW,CAAEiB,MAAc,IAAK;IACnDR,eAAe,CAACS,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPR,WAAW,EAAEO,MAAM;MACnBN,KAAK,EAAEO,IAAI,CAACP,KAAK,CAACQ,GAAG,CAACC,IAAI,KAAK;QAC7B,GAAGA,IAAI;QACPf,MAAM,EAAEe,IAAI,CAAClB,EAAE,KAAKe,MAAM,GAAG,QAAQ,GAC7BG,IAAI,CAACf,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAGe,IAAI,CAACf,MAAM;QAC1DgB,QAAQ,EAAED,IAAI,CAAClB,EAAE,KAAKe,MAAM,GAAG,CAAC,GAAGG,IAAI,CAACC;MAC1C,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,kBAAkB,GAAGtB,WAAW,CAAC,CAACiB,MAAc,EAAEI,QAAgB,KAAK;IAC3EZ,eAAe,CAACS,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPP,KAAK,EAAEO,IAAI,CAACP,KAAK,CAACQ,GAAG,CAACC,IAAI,IACxBA,IAAI,CAAClB,EAAE,KAAKe,MAAM,GAAG;QAAE,GAAGG,IAAI;QAAEC;MAAS,CAAC,GAAGD,IAC/C;IACF,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,YAAY,GAAGvB,WAAW,CAAEiB,MAAc,IAAK;IACnDR,eAAe,CAACS,IAAI,IAAI;MACtB,MAAMM,YAAY,GAAGN,IAAI,CAACP,KAAK,CAACQ,GAAG,CAACC,IAAI,IACtCA,IAAI,CAAClB,EAAE,KAAKe,MAAM,GAAG;QAAE,GAAGG,IAAI;QAAEf,MAAM,EAAE,WAAoB;QAAEgB,QAAQ,EAAE;MAAI,CAAC,GAAGD,IAClF,CAAC;;MAED;MACA,MAAMK,cAAc,GAAGD,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACtB,MAAM,KAAK,WAAW,CAAC,CAACuB,MAAM;MAChF,MAAMC,WAAW,GAAGL,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACtB,MAAM,KAAK,QAAQ,CAAC;;MAEnE;MACA,MAAMyB,mBAAmB,GAAGD,WAAW,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEZ,IAAI,KAAKY,GAAG,GAAGZ,IAAI,CAACC,QAAQ,EAAE,CAAC,CAAC;MACrF,MAAMY,kBAAkB,GAAGR,cAAc,GAAG,GAAG,GAAGK,mBAAmB;MACrE,MAAMlB,eAAe,GAAGsB,IAAI,CAACC,KAAK,CAACF,kBAAkB,GAAGT,YAAY,CAACI,MAAM,CAAC;MAE5E,OAAO;QACL,GAAGV,IAAI;QACPP,KAAK,EAAEa,YAAY;QACnBZ,eAAe,EAAEsB,IAAI,CAACE,GAAG,CAAC,GAAG,EAAExB,eAAe;MAChD,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyB,YAAY,GAAGrC,WAAW,CAAC,CAACiB,MAAc,EAAEqB,YAAqB,KAAK;IAC1E7B,eAAe,CAACS,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPP,KAAK,EAAEO,IAAI,CAACP,KAAK,CAACQ,GAAG,CAACC,IAAI,IACxBA,IAAI,CAAClB,EAAE,KAAKe,MAAM,GAAG;QACnB,GAAGG,IAAI;QACPf,MAAM,EAAE,OAAgB;QACxBgB,QAAQ,EAAE;MACZ,CAAC,GAAGD,IACN;IACF,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmB,eAAe,GAAGvC,WAAW,CAAC,CAACwC,MAAc,EAAEC,QAAgB,KAAK;IACxEhC,eAAe,CAACS,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPL,cAAc,EAAE,CACd,GAAGK,IAAI,CAACL,cAAc,CAACa,MAAM,CAACgB,EAAE,IAAIA,EAAE,CAACF,MAAM,KAAKA,MAAM,CAAC,EACzD;QACEA,MAAM;QACNC,QAAQ;QACR/B,WAAW,EAAE,QAAQ;QACrBiC,YAAY,EAAE;MAChB,CAAC;IAEL,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,kBAAkB,GAAG5C,WAAW,CAAC,CAACwC,MAAc,EAAEvB,MAAc,EAAEI,QAAgB,KAAK;IAC3FZ,eAAe,CAACS,IAAI,IAAI;MACtB,MAAM2B,qBAAqB,GAAG3B,IAAI,CAACL,cAAc,CAACM,GAAG,CAACuB,EAAE,IACtDA,EAAE,CAACF,MAAM,KAAKA,MAAM,GAAG;QAAE,GAAGE,EAAE;QAAEhC,WAAW,EAAEO,MAAM;QAAE0B,YAAY,EAAEtB;MAAS,CAAC,GAAGqB,EAClF,CAAC;;MAED;MACA,MAAMI,UAAU,GAAGD,qBAAqB,CAACjB,MAAM;MAC/C,IAAIkB,UAAU,KAAK,CAAC,EAAE,OAAO5B,IAAI;;MAEjC;MACA,MAAMM,YAAY,GAAGN,IAAI,CAACP,KAAK,CAACQ,GAAG,CAACC,IAAI,IAAI;QAC1C,MAAM2B,eAAe,GAAGF,qBAAqB,CAACnB,MAAM,CAACgB,EAAE,IAAIA,EAAE,CAAChC,WAAW,KAAKU,IAAI,CAAClB,EAAE,CAAC;QACtF,MAAM8C,sBAAsB,GAAGH,qBAAqB,CAACnB,MAAM,CAACgB,EAAE,IAAI;UAChE,MAAMO,UAAU,GAAGC,WAAW,CAACR,EAAE,CAAChC,WAAW,CAA6B,IAAI,CAAC;UAC/E,MAAMyC,iBAAiB,GAAGD,WAAW,CAAC9B,IAAI,CAAClB,EAAE,CAA6B,IAAI,CAAC;UAC/E,OAAO+C,UAAU,GAAGE,iBAAiB,IAAKF,UAAU,KAAKE,iBAAiB,IAAIT,EAAE,CAACC,YAAY,KAAK,GAAI;QACxG,CAAC,CAAC;QAEF,IAAIvB,IAAI,CAAClB,EAAE,KAAK,UAAU,EAAE;UAC1B;UACA,MAAMkD,cAAc,GAAGP,qBAAqB,CAACnB,MAAM,CAACgB,EAAE,IACpDA,EAAE,CAAChC,WAAW,KAAK,UAAU,IAAIgC,EAAE,CAACC,YAAY,KAAK,GACvD,CAAC,CAACf,MAAM;UACR,MAAMyB,eAAe,GAAGR,qBAAqB,CAACnB,MAAM,CAACgB,EAAE,IACrDA,EAAE,CAAChC,WAAW,KAAK,UAAU,IAAIgC,EAAE,CAACC,YAAY,GAAG,CAAC,IAAID,EAAE,CAACC,YAAY,GAAG,GAC5E,CAAC,CAACf,MAAM;UAER,IAAIwB,cAAc,KAAKN,UAAU,IAAIA,UAAU,GAAG,CAAC,EAAE;YACnD,OAAO;cAAE,GAAG1B,IAAI;cAAEf,MAAM,EAAE,WAAoB;cAAEgB,QAAQ,EAAE;YAAI,CAAC;UACjE,CAAC,MAAM,IAAI+B,cAAc,GAAG,CAAC,IAAIC,eAAe,GAAG,CAAC,EAAE;YACpD,MAAMhC,QAAQ,GAAGyB,UAAU,GAAG,CAAC,GAAGZ,IAAI,CAACC,KAAK,CAAEiB,cAAc,GAAGN,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;YACrF,OAAO;cAAE,GAAG1B,IAAI;cAAEf,MAAM,EAAE,QAAiB;cAAEgB;YAAS,CAAC;UACzD,CAAC,MAAM;YACL,OAAO;cAAE,GAAGD,IAAI;cAAEf,MAAM,EAAE,SAAkB;cAAEgB,QAAQ,EAAE;YAAE,CAAC;UAC7D;QACF,CAAC,MAAM,IAAID,IAAI,CAAClB,EAAE,KAAK,SAAS,EAAE;UAChC;UACA,MAAMoD,aAAa,GAAGT,qBAAqB,CAACnB,MAAM,CAACgB,EAAE,IACnDA,EAAE,CAAChC,WAAW,KAAK,SAAS,IAAIgC,EAAE,CAACC,YAAY,KAAK,GACtD,CAAC,CAACf,MAAM;UACR,MAAM2B,cAAc,GAAGV,qBAAqB,CAACnB,MAAM,CAACgB,EAAE,IACpDA,EAAE,CAAChC,WAAW,KAAK,SAAS,IAAIgC,EAAE,CAACC,YAAY,GAAG,CAAC,IAAID,EAAE,CAACC,YAAY,GAAG,GAC3E,CAAC,CAACf,MAAM;UAER,IAAI0B,aAAa,KAAKR,UAAU,IAAIA,UAAU,GAAG,CAAC,EAAE;YAClD,OAAO;cAAE,GAAG1B,IAAI;cAAEf,MAAM,EAAE,WAAoB;cAAEgB,QAAQ,EAAE;YAAI,CAAC;UACjE,CAAC,MAAM,IAAIiC,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAG,CAAC,EAAE;YAClD,MAAMlC,QAAQ,GAAGyB,UAAU,GAAG,CAAC,GAAGZ,IAAI,CAACC,KAAK,CAAEmB,aAAa,GAAGR,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;YACpF,OAAO;cAAE,GAAG1B,IAAI;cAAEf,MAAM,EAAE,QAAiB;cAAEgB;YAAS,CAAC;UACzD,CAAC,MAAM;YACL,OAAO;cAAE,GAAGD,IAAI;cAAEf,MAAM,EAAE,SAAkB;cAAEgB,QAAQ,EAAE;YAAE,CAAC;UAC7D;QACF,CAAC,MAAM,IAAI0B,eAAe,CAACnB,MAAM,GAAG,CAAC,EAAE;UACrC,MAAM4B,WAAW,GAAGT,eAAe,CAAChB,MAAM,CAAC,CAACC,GAAG,EAAEU,EAAE,KAAKV,GAAG,GAAGU,EAAE,CAACC,YAAY,EAAE,CAAC,CAAC,GAAGI,eAAe,CAACnB,MAAM;UAC1G,OAAO;YAAE,GAAGR,IAAI;YAAEf,MAAM,EAAE,QAAiB;YAAEgB,QAAQ,EAAEa,IAAI,CAACC,KAAK,CAACqB,WAAW;UAAE,CAAC;QAClF,CAAC,MAAM,IAAIR,sBAAsB,CAACpB,MAAM,KAAKkB,UAAU,IAAIA,UAAU,GAAG,CAAC,EAAE;UACzE,OAAO;YAAE,GAAG1B,IAAI;YAAEf,MAAM,EAAE,WAAoB;YAAEgB,QAAQ,EAAE;UAAI,CAAC;QACjE,CAAC,MAAM;UACL,OAAO;YAAE,GAAGD,IAAI;YAAEf,MAAM,EAAE,SAAkB;YAAEgB,QAAQ,EAAE;UAAE,CAAC;QAC7D;MACF,CAAC,CAAC;MAEF,OAAO;QACL,GAAGH,IAAI;QACPL,cAAc,EAAEgC,qBAAqB;QACrClC,KAAK,EAAEa,YAAY;QACnBZ,eAAe,EAAEsB,IAAI,CAACC,KAAK,CAACvB,eAAe;MAC7C,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM6C,SAAS,GAAGzD,WAAW,CAAC,MAAM;IAClCS,eAAe,CAAC;MACdC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAEV,YAAY;MACnBW,eAAe,EAAE,CAAC;MAClBC,cAAc,EAAE;IAClB,CAAC,CAAC;IACF;IACAE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2C,QAAQ,GAAG1D,WAAW,CAAC,MAAM;IACjCyD,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;;EAEf;EACA,MAAME,cAAc,GAAG3D,WAAW,CAAC,CAACwC,MAAc,EAAEC,QAAgB,KAAK;IACvEF,eAAe,CAACC,MAAM,EAAEC,QAAQ,CAAC;IACjCG,kBAAkB,CAACJ,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAC;EAC3C,CAAC,EAAE,CAACD,eAAe,EAAEK,kBAAkB,CAAC,CAAC;;EAEzC;EACA,MAAMgB,gBAAgB,GAAG5D,WAAW,CAAEwC,MAAc,IAAK;IACvDI,kBAAkB,CAACJ,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;;IAEzC;IACA,IAAInB,QAAQ,GAAG,CAAC;IAChB,MAAMwC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCzC,QAAQ,IAAI,EAAE;MACduB,kBAAkB,CAACJ,MAAM,EAAE,UAAU,EAAEnB,QAAQ,CAAC;MAChD,IAAIA,QAAQ,IAAI,GAAG,EAAE;QACnB0C,aAAa,CAACF,QAAQ,CAAC;MACzB;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,CAACjB,kBAAkB,CAAC,CAAC;;EAExB;EACA,MAAMoB,gBAAgB,GAAGhE,WAAW,CAAEwC,MAAc,IAAK;IACvD;IACA,MAAMyB,YAAY,GAAGzD,YAAY,CAACK,cAAc,CAACqD,IAAI,CAACxB,EAAE,IAAIA,EAAE,CAACF,MAAM,KAAKA,MAAM,CAAC;;IAEjF;IACA,IAAI,CAACyB,YAAY,IAAIA,YAAY,CAACvD,WAAW,KAAK,UAAU,EAAE;MAC5DkC,kBAAkB,CAACJ,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC;IAC5C;;IAEA;IACA,IAAI2B,cAAc,GAAG,CAAC;IACtB,MAAMC,cAAc,GAAGN,WAAW,CAAC,MAAM;MACvCK,cAAc,IAAI,EAAE;MACpBvB,kBAAkB,CAACJ,MAAM,EAAE,WAAW,EAAE2B,cAAc,CAAC;MACvD,IAAIA,cAAc,IAAI,GAAG,EAAE;QACzBJ,aAAa,CAACK,cAAc,CAAC;;QAE7B;QACAxB,kBAAkB,CAACJ,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;QACxC,IAAI6B,eAAe,GAAG,CAAC;QACvB,MAAMC,eAAe,GAAGR,WAAW,CAAC,MAAM;UACxCO,eAAe,IAAI,EAAE;UACrBzB,kBAAkB,CAACJ,MAAM,EAAE,SAAS,EAAE6B,eAAe,CAAC;UACtD,IAAIA,eAAe,IAAI,GAAG,EAAE;YAC1BN,aAAa,CAACO,eAAe,CAAC;UAChC;QACF,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,CAAC1B,kBAAkB,EAAEpC,YAAY,CAACK,cAAc,CAAC,CAAC;;EAErD;EACA,MAAM0D,kBAAkB,GAAGvE,WAAW,CAAC,MAAM;IAC3C;IACAgB,YAAY,CAAC,UAAU,CAAC;;IAExB;IACAR,YAAY,CAACK,cAAc,CAAC2D,OAAO,CAAC9B,EAAE,IAAI;MACxC,IAAIA,EAAE,CAAChC,WAAW,KAAK,SAAS,IAAIgC,EAAE,CAACC,YAAY,KAAK,GAAG,EAAE;QAC3DC,kBAAkB,CAACF,EAAE,CAACF,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;;QAE5C;QACA,IAAInB,QAAQ,GAAG,CAAC;QAChB,MAAMwC,QAAQ,GAAGC,WAAW,CAAC,MAAM;UACjCzC,QAAQ,IAAI,EAAE;UACduB,kBAAkB,CAACF,EAAE,CAACF,MAAM,EAAE,UAAU,EAAEnB,QAAQ,CAAC;UACnD,IAAIA,QAAQ,IAAI,GAAG,EAAE;YACnB0C,aAAa,CAACF,QAAQ,CAAC;YACvBjB,kBAAkB,CAACF,EAAE,CAACF,MAAM,EAAE,UAAU,EAAE,GAAG,CAAC;UAChD;QACF,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,CAAC;;IAEF;IACAiC,UAAU,CAAC,MAAM;MACflD,YAAY,CAAC,UAAU,CAAC;MACxBP,YAAY,CAAC,UAAU,CAAC;;MAExB;MACAyD,UAAU,CAAC,MAAM;QACflD,YAAY,CAAC,UAAU,CAAC;MAC1B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC,EAAE,CAACf,YAAY,CAACK,cAAc,EAAE+B,kBAAkB,EAAE5B,YAAY,EAAEO,YAAY,CAAC,CAAC;EAEjF,OAAO;IACLf,YAAY;IACZQ,YAAY;IACZM,kBAAkB;IAClBC,YAAY;IACZc,YAAY;IACZoB,SAAS;IACTlB,eAAe;IACfK,kBAAkB;IAClB;IACAe,cAAc;IACdC,gBAAgB;IAChBI,gBAAgB;IAChBO,kBAAkB;IAClB;IACAzD,aAAa;IACbC,gBAAgB;IAChB;IACA2C;EACF,CAAC;AACH,CAAC;AAACnD,EAAA,CA/RWD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}