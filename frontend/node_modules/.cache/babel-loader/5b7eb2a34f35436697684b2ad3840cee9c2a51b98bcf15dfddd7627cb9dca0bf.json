{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\components\\\\ProcessFlow.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Upload, CheckCircle, Settings, Brain, FileText, Download, Clock, AlertCircle, Zap } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProcessFlow = () => {\n  _s();\n  const [steps, setSteps] = useState([{\n    id: 'upload',\n    title: '文件上传',\n    description: '上传入户数据和投诉数据文件',\n    icon: /*#__PURE__*/_jsxDEV(Upload, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 13\n    }, this),\n    status: 'pending'\n  }, {\n    id: 'validate',\n    title: '内容检测',\n    description: '验证文件格式和必要字段',\n    icon: /*#__PURE__*/_jsxDEV(CheckCircle, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this),\n    status: 'pending'\n  }, {\n    id: 'configure',\n    title: '解析配置',\n    description: '配置智能解析规则',\n    icon: /*#__PURE__*/_jsxDEV(Settings, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this),\n    status: 'pending'\n  }, {\n    id: 'analyze',\n    title: '智能分析',\n    description: '提取转写文本并进行智能解析',\n    icon: /*#__PURE__*/_jsxDEV(Brain, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this),\n    status: 'pending'\n  }, {\n    id: 'generate',\n    title: '报表生成',\n    description: '生成分析报表和可视化图表',\n    icon: /*#__PURE__*/_jsxDEV(FileText, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this),\n    status: 'pending'\n  }, {\n    id: 'download',\n    title: '结果下载',\n    description: '下载分析结果和报表文件',\n    icon: /*#__PURE__*/_jsxDEV(Download, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this),\n    status: 'pending'\n  }]);\n  const [currentProgress, setCurrentProgress] = useState('');\n\n  // 模拟流程进度更新\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setSteps(prevSteps => {\n        const newSteps = [...prevSteps];\n        const activeIndex = newSteps.findIndex(step => step.status === 'active');\n        if (activeIndex !== -1) {\n          // 更新当前活动步骤的进度\n          if (newSteps[activeIndex].progress === undefined) {\n            newSteps[activeIndex].progress = 0;\n          }\n          newSteps[activeIndex].progress += 10;\n          if (newSteps[activeIndex].progress >= 100) {\n            newSteps[activeIndex].status = 'completed';\n            newSteps[activeIndex].progress = 100;\n\n            // 激活下一个步骤\n            if (activeIndex + 1 < newSteps.length) {\n              newSteps[activeIndex + 1].status = 'active';\n              newSteps[activeIndex + 1].progress = 0;\n            }\n          }\n        }\n        return newSteps;\n      });\n    }, 500);\n    return () => clearInterval(interval);\n  }, []);\n  const getStepColor = status => {\n    switch (status) {\n      case 'completed':\n        return 'text-tech-green border-tech-green bg-tech-green/10';\n      case 'active':\n        return 'text-tech-cyan border-tech-cyan bg-tech-cyan/10 animate-glow';\n      case 'error':\n        return 'text-red-400 border-red-400 bg-red-400/10';\n      default:\n        return 'text-gray-400 border-gray-600 bg-gray-600/10';\n    }\n  };\n  const getConnectorColor = (currentStatus, nextStatus) => {\n    if (currentStatus === 'completed') {\n      return 'bg-tech-green';\n    } else if (currentStatus === 'active') {\n      return 'bg-gradient-to-b from-tech-cyan to-gray-600';\n    }\n    return 'bg-gray-600';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"tech-card p-6 h-full flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-3 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"p-2 bg-gradient-to-r from-tech-cyan to-tech-purple rounded-lg\",\n        animate: {\n          rotate: [0, 360]\n        },\n        transition: {\n          duration: 20,\n          repeat: Infinity,\n          ease: \"linear\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Zap, {\n          className: \"w-6 h-6 text-white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-white\",\n          children: \"\\u5904\\u7406\\u6D41\\u7A0B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 text-sm\",\n          children: \"\\u5B9E\\u65F6\\u76D1\\u63A7\\u5206\\u6790\\u8FDB\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4 flex-1\",\n      children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"relative\",\n        initial: {\n          opacity: 0,\n          x: -20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        children: [index < steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute left-6 top-12 w-0.5 h-8 z-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-full h-full ${getConnectorColor(step.status, steps[index + 1].status)} transition-all duration-500`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: `relative z-10 flex items-start space-x-4 p-4 rounded-lg border transition-all duration-300 ${getStepColor(step.status)}`,\n          whileHover: {\n            scale: 1.02\n          },\n          layout: true,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex-shrink-0 p-2 rounded-lg border ${getStepColor(step.status)}`,\n            children: step.status === 'active' ? /*#__PURE__*/_jsxDEV(motion.div, {\n              animate: {\n                rotate: 360\n              },\n              transition: {\n                duration: 2,\n                repeat: Infinity,\n                ease: \"linear\"\n              },\n              children: step.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 19\n            }, this) : step.status === 'completed' ? /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0\n              },\n              animate: {\n                scale: 1\n              },\n              transition: {\n                type: \"spring\",\n                stiffness: 500,\n                damping: 30\n              },\n              children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 19\n            }, this) : step.status === 'error' ? /*#__PURE__*/_jsxDEV(AlertCircle, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 19\n            }, this) : step.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-white\",\n              children: step.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mt-1\",\n              children: step.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: step.status === 'active' && step.progress !== undefined && /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"mt-3\",\n                initial: {\n                  opacity: 0,\n                  height: 0\n                },\n                animate: {\n                  opacity: 1,\n                  height: 'auto'\n                },\n                exit: {\n                  opacity: 0,\n                  height: 0\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-xs text-gray-400 mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u5904\\u7406\\u4E2D...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [step.progress, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full bg-gray-700 rounded-full h-2\",\n                  children: /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"progress-bar h-2 rounded-full\",\n                    initial: {\n                      width: 0\n                    },\n                    animate: {\n                      width: `${step.progress}%`\n                    },\n                    transition: {\n                      duration: 0.3\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), step.status === 'completed' && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"flex items-center space-x-1 mt-2 text-xs text-tech-green\",\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Clock, {\n                className: \"w-3 h-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"\\u5B8C\\u6210\\u4E8E \", new Date().toLocaleTimeString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this)]\n      }, step.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"mt-6 p-4 bg-tech-blue/20 rounded-lg border border-tech-cyan/20\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.5\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between text-sm text-gray-400 mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u603B\\u4F53\\u8FDB\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [Math.round(steps.filter(s => s.status === 'completed').length / steps.length * 100), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full bg-gray-700 rounded-full h-2\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"progress-bar h-2 rounded-full\",\n          initial: {\n            width: 0\n          },\n          animate: {\n            width: `${steps.filter(s => s.status === 'completed').length / steps.length * 100}%`\n          },\n          transition: {\n            duration: 0.5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n};\n_s(ProcessFlow, \"aZbNzRNHWUyv/IUtncQhkUzXpDo=\");\n_c = ProcessFlow;\nexport default ProcessFlow;\nvar _c;\n$RefreshReg$(_c, \"ProcessFlow\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "Upload", "CheckCircle", "Settings", "Brain", "FileText", "Download", "Clock", "AlertCircle", "Zap", "jsxDEV", "_jsxDEV", "ProcessFlow", "_s", "steps", "setSteps", "id", "title", "description", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "status", "currentProgress", "setCurrentProgress", "interval", "setInterval", "prevSteps", "newSteps", "activeIndex", "findIndex", "step", "progress", "undefined", "length", "clearInterval", "getStepColor", "getConnectorColor", "currentStatus", "nextStatus", "children", "div", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "map", "index", "initial", "opacity", "x", "delay", "whileHover", "scale", "layout", "type", "stiffness", "damping", "height", "exit", "width", "Date", "toLocaleTimeString", "y", "Math", "round", "filter", "s", "_c", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/components/ProcessFlow.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Upload, \n  CheckCircle, \n  Settings, \n  Brain, \n  FileText, \n  Download,\n  Clock,\n  AlertCircle,\n  Zap\n} from 'lucide-react';\n\ninterface ProcessStep {\n  id: string;\n  title: string;\n  description: string;\n  icon: React.ReactNode;\n  status: 'pending' | 'active' | 'completed' | 'error';\n  progress?: number;\n}\n\nconst ProcessFlow: React.FC = () => {\n  const [steps, setSteps] = useState<ProcessStep[]>([\n    {\n      id: 'upload',\n      title: '文件上传',\n      description: '上传入户数据和投诉数据文件',\n      icon: <Upload className=\"w-6 h-6\" />,\n      status: 'pending'\n    },\n    {\n      id: 'validate',\n      title: '内容检测',\n      description: '验证文件格式和必要字段',\n      icon: <CheckCircle className=\"w-6 h-6\" />,\n      status: 'pending'\n    },\n    {\n      id: 'configure',\n      title: '解析配置',\n      description: '配置智能解析规则',\n      icon: <Settings className=\"w-6 h-6\" />,\n      status: 'pending'\n    },\n    {\n      id: 'analyze',\n      title: '智能分析',\n      description: '提取转写文本并进行智能解析',\n      icon: <Brain className=\"w-6 h-6\" />,\n      status: 'pending'\n    },\n    {\n      id: 'generate',\n      title: '报表生成',\n      description: '生成分析报表和可视化图表',\n      icon: <FileText className=\"w-6 h-6\" />,\n      status: 'pending'\n    },\n    {\n      id: 'download',\n      title: '结果下载',\n      description: '下载分析结果和报表文件',\n      icon: <Download className=\"w-6 h-6\" />,\n      status: 'pending'\n    }\n  ]);\n\n  const [currentProgress, setCurrentProgress] = useState<string>('');\n\n  // 模拟流程进度更新\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setSteps(prevSteps => {\n        const newSteps = [...prevSteps];\n        const activeIndex = newSteps.findIndex(step => step.status === 'active');\n        \n        if (activeIndex !== -1) {\n          // 更新当前活动步骤的进度\n          if (newSteps[activeIndex].progress === undefined) {\n            newSteps[activeIndex].progress = 0;\n          }\n          newSteps[activeIndex].progress! += 10;\n          \n          if (newSteps[activeIndex].progress! >= 100) {\n            newSteps[activeIndex].status = 'completed';\n            newSteps[activeIndex].progress = 100;\n            \n            // 激活下一个步骤\n            if (activeIndex + 1 < newSteps.length) {\n              newSteps[activeIndex + 1].status = 'active';\n              newSteps[activeIndex + 1].progress = 0;\n            }\n          }\n        }\n        \n        return newSteps;\n      });\n    }, 500);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const getStepColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'text-tech-green border-tech-green bg-tech-green/10';\n      case 'active':\n        return 'text-tech-cyan border-tech-cyan bg-tech-cyan/10 animate-glow';\n      case 'error':\n        return 'text-red-400 border-red-400 bg-red-400/10';\n      default:\n        return 'text-gray-400 border-gray-600 bg-gray-600/10';\n    }\n  };\n\n  const getConnectorColor = (currentStatus: string, nextStatus: string) => {\n    if (currentStatus === 'completed') {\n      return 'bg-tech-green';\n    } else if (currentStatus === 'active') {\n      return 'bg-gradient-to-b from-tech-cyan to-gray-600';\n    }\n    return 'bg-gray-600';\n  };\n\n  return (\n    <div className=\"tech-card p-6 h-full flex flex-col\">\n      <div className=\"flex items-center space-x-3 mb-6\">\n        <motion.div\n          className=\"p-2 bg-gradient-to-r from-tech-cyan to-tech-purple rounded-lg\"\n          animate={{ rotate: [0, 360] }}\n          transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n        >\n          <Zap className=\"w-6 h-6 text-white\" />\n        </motion.div>\n        <div>\n          <h2 className=\"text-xl font-bold text-white\">处理流程</h2>\n          <p className=\"text-gray-400 text-sm\">实时监控分析进度</p>\n        </div>\n      </div>\n\n      <div className=\"space-y-4 flex-1\">\n        {steps.map((step, index) => (\n          <motion.div\n            key={step.id}\n            className=\"relative\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: index * 0.1 }}\n          >\n            {/* 连接线 */}\n            {index < steps.length - 1 && (\n              <div className=\"absolute left-6 top-12 w-0.5 h-8 z-0\">\n                <div \n                  className={`w-full h-full ${getConnectorColor(step.status, steps[index + 1].status)} transition-all duration-500`}\n                />\n              </div>\n            )}\n\n            {/* 步骤卡片 */}\n            <motion.div\n              className={`relative z-10 flex items-start space-x-4 p-4 rounded-lg border transition-all duration-300 ${getStepColor(step.status)}`}\n              whileHover={{ scale: 1.02 }}\n              layout\n            >\n              {/* 图标 */}\n              <div className={`flex-shrink-0 p-2 rounded-lg border ${getStepColor(step.status)}`}>\n                {step.status === 'active' ? (\n                  <motion.div\n                    animate={{ rotate: 360 }}\n                    transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n                  >\n                    {step.icon}\n                  </motion.div>\n                ) : step.status === 'completed' ? (\n                  <motion.div\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                  >\n                    <CheckCircle className=\"w-6 h-6\" />\n                  </motion.div>\n                ) : step.status === 'error' ? (\n                  <AlertCircle className=\"w-6 h-6\" />\n                ) : (\n                  step.icon\n                )}\n              </div>\n\n              {/* 内容 */}\n              <div className=\"flex-1 min-w-0\">\n                <h3 className=\"font-medium text-white\">{step.title}</h3>\n                <p className=\"text-sm text-gray-400 mt-1\">{step.description}</p>\n                \n                {/* 进度条 */}\n                <AnimatePresence>\n                  {step.status === 'active' && step.progress !== undefined && (\n                    <motion.div\n                      className=\"mt-3\"\n                      initial={{ opacity: 0, height: 0 }}\n                      animate={{ opacity: 1, height: 'auto' }}\n                      exit={{ opacity: 0, height: 0 }}\n                    >\n                      <div className=\"flex items-center justify-between text-xs text-gray-400 mb-1\">\n                        <span>处理中...</span>\n                        <span>{step.progress}%</span>\n                      </div>\n                      <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                        <motion.div\n                          className=\"progress-bar h-2 rounded-full\"\n                          initial={{ width: 0 }}\n                          animate={{ width: `${step.progress}%` }}\n                          transition={{ duration: 0.3 }}\n                        />\n                      </div>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n\n                {/* 时间戳 */}\n                {step.status === 'completed' && (\n                  <motion.div\n                    className=\"flex items-center space-x-1 mt-2 text-xs text-tech-green\"\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                  >\n                    <Clock className=\"w-3 h-3\" />\n                    <span>完成于 {new Date().toLocaleTimeString()}</span>\n                  </motion.div>\n                )}\n              </div>\n            </motion.div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* 总体进度 */}\n      <motion.div\n        className=\"mt-6 p-4 bg-tech-blue/20 rounded-lg border border-tech-cyan/20\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.5 }}\n      >\n        <div className=\"flex items-center justify-between text-sm text-gray-400 mb-2\">\n          <span>总体进度</span>\n          <span>{Math.round((steps.filter(s => s.status === 'completed').length / steps.length) * 100)}%</span>\n        </div>\n        <div className=\"w-full bg-gray-700 rounded-full h-2\">\n          <motion.div\n            className=\"progress-bar h-2 rounded-full\"\n            initial={{ width: 0 }}\n            animate={{ width: `${(steps.filter(s => s.status === 'completed').length / steps.length) * 100}%` }}\n            transition={{ duration: 0.5 }}\n          />\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default ProcessFlow;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,MAAM,EACNC,WAAW,EACXC,QAAQ,EACRC,KAAK,EACLC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,GAAG,QACE,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWtB,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAgB,CAChD;IACEmB,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,eAAe;IAC5BC,IAAI,eAAER,OAAA,CAACV,MAAM;MAACmB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpCC,MAAM,EAAE;EACV,CAAC,EACD;IACET,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,aAAa;IAC1BC,IAAI,eAAER,OAAA,CAACT,WAAW;MAACkB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzCC,MAAM,EAAE;EACV,CAAC,EACD;IACET,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,UAAU;IACvBC,IAAI,eAAER,OAAA,CAACR,QAAQ;MAACiB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtCC,MAAM,EAAE;EACV,CAAC,EACD;IACET,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,eAAe;IAC5BC,IAAI,eAAER,OAAA,CAACP,KAAK;MAACgB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnCC,MAAM,EAAE;EACV,CAAC,EACD;IACET,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,cAAc;IAC3BC,IAAI,eAAER,OAAA,CAACN,QAAQ;MAACe,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtCC,MAAM,EAAE;EACV,CAAC,EACD;IACET,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,aAAa;IAC1BC,IAAI,eAAER,OAAA,CAACL,QAAQ;MAACc,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtCC,MAAM,EAAE;EACV,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAS,EAAE,CAAC;;EAElE;EACAC,SAAS,CAAC,MAAM;IACd,MAAM8B,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCd,QAAQ,CAACe,SAAS,IAAI;QACpB,MAAMC,QAAQ,GAAG,CAAC,GAAGD,SAAS,CAAC;QAC/B,MAAME,WAAW,GAAGD,QAAQ,CAACE,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACT,MAAM,KAAK,QAAQ,CAAC;QAExE,IAAIO,WAAW,KAAK,CAAC,CAAC,EAAE;UACtB;UACA,IAAID,QAAQ,CAACC,WAAW,CAAC,CAACG,QAAQ,KAAKC,SAAS,EAAE;YAChDL,QAAQ,CAACC,WAAW,CAAC,CAACG,QAAQ,GAAG,CAAC;UACpC;UACAJ,QAAQ,CAACC,WAAW,CAAC,CAACG,QAAQ,IAAK,EAAE;UAErC,IAAIJ,QAAQ,CAACC,WAAW,CAAC,CAACG,QAAQ,IAAK,GAAG,EAAE;YAC1CJ,QAAQ,CAACC,WAAW,CAAC,CAACP,MAAM,GAAG,WAAW;YAC1CM,QAAQ,CAACC,WAAW,CAAC,CAACG,QAAQ,GAAG,GAAG;;YAEpC;YACA,IAAIH,WAAW,GAAG,CAAC,GAAGD,QAAQ,CAACM,MAAM,EAAE;cACrCN,QAAQ,CAACC,WAAW,GAAG,CAAC,CAAC,CAACP,MAAM,GAAG,QAAQ;cAC3CM,QAAQ,CAACC,WAAW,GAAG,CAAC,CAAC,CAACG,QAAQ,GAAG,CAAC;YACxC;UACF;QACF;QAEA,OAAOJ,QAAQ;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMO,aAAa,CAACV,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,YAAY,GAAId,MAAc,IAAK;IACvC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,oDAAoD;MAC7D,KAAK,QAAQ;QACX,OAAO,8DAA8D;MACvE,KAAK,OAAO;QACV,OAAO,2CAA2C;MACpD;QACE,OAAO,8CAA8C;IACzD;EACF,CAAC;EAED,MAAMe,iBAAiB,GAAGA,CAACC,aAAqB,EAAEC,UAAkB,KAAK;IACvE,IAAID,aAAa,KAAK,WAAW,EAAE;MACjC,OAAO,eAAe;IACxB,CAAC,MAAM,IAAIA,aAAa,KAAK,QAAQ,EAAE;MACrC,OAAO,6CAA6C;IACtD;IACA,OAAO,aAAa;EACtB,CAAC;EAED,oBACE9B,OAAA;IAAKS,SAAS,EAAC,oCAAoC;IAAAuB,QAAA,gBACjDhC,OAAA;MAAKS,SAAS,EAAC,kCAAkC;MAAAuB,QAAA,gBAC/ChC,OAAA,CAACZ,MAAM,CAAC6C,GAAG;QACTxB,SAAS,EAAC,+DAA+D;QACzEyB,OAAO,EAAE;UAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG;QAAE,CAAE;QAC9BC,UAAU,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEC,MAAM,EAAEC,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAE;QAAAR,QAAA,eAE/DhC,OAAA,CAACF,GAAG;UAACW,SAAS,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACbb,OAAA;QAAAgC,QAAA,gBACEhC,OAAA;UAAIS,SAAS,EAAC,8BAA8B;UAAAuB,QAAA,EAAC;QAAI;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDb,OAAA;UAAGS,SAAS,EAAC,uBAAuB;UAAAuB,QAAA,EAAC;QAAQ;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENb,OAAA;MAAKS,SAAS,EAAC,kBAAkB;MAAAuB,QAAA,EAC9B7B,KAAK,CAACsC,GAAG,CAAC,CAAClB,IAAI,EAAEmB,KAAK,kBACrB1C,OAAA,CAACZ,MAAM,CAAC6C,GAAG;QAETxB,SAAS,EAAC,UAAU;QACpBkC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCX,OAAO,EAAE;UAAEU,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BT,UAAU,EAAE;UAAEU,KAAK,EAAEJ,KAAK,GAAG;QAAI,CAAE;QAAAV,QAAA,GAGlCU,KAAK,GAAGvC,KAAK,CAACuB,MAAM,GAAG,CAAC,iBACvB1B,OAAA;UAAKS,SAAS,EAAC,sCAAsC;UAAAuB,QAAA,eACnDhC,OAAA;YACES,SAAS,EAAE,iBAAiBoB,iBAAiB,CAACN,IAAI,CAACT,MAAM,EAAEX,KAAK,CAACuC,KAAK,GAAG,CAAC,CAAC,CAAC5B,MAAM,CAAC;UAA+B;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGDb,OAAA,CAACZ,MAAM,CAAC6C,GAAG;UACTxB,SAAS,EAAE,8FAA8FmB,YAAY,CAACL,IAAI,CAACT,MAAM,CAAC,EAAG;UACrIiC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,MAAM;UAAAjB,QAAA,gBAGNhC,OAAA;YAAKS,SAAS,EAAE,uCAAuCmB,YAAY,CAACL,IAAI,CAACT,MAAM,CAAC,EAAG;YAAAkB,QAAA,EAChFT,IAAI,CAACT,MAAM,KAAK,QAAQ,gBACvBd,OAAA,CAACZ,MAAM,CAAC6C,GAAG;cACTC,OAAO,EAAE;gBAAEC,MAAM,EAAE;cAAI,CAAE;cACzBC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,CAAC;gBAAEC,MAAM,EAAEC,QAAQ;gBAAEC,IAAI,EAAE;cAAS,CAAE;cAAAR,QAAA,EAE7DT,IAAI,CAACf;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,GACXU,IAAI,CAACT,MAAM,KAAK,WAAW,gBAC7Bd,OAAA,CAACZ,MAAM,CAAC6C,GAAG;cACTU,OAAO,EAAE;gBAAEK,KAAK,EAAE;cAAE,CAAE;cACtBd,OAAO,EAAE;gBAAEc,KAAK,EAAE;cAAE,CAAE;cACtBZ,UAAU,EAAE;gBAAEc,IAAI,EAAE,QAAQ;gBAAEC,SAAS,EAAE,GAAG;gBAAEC,OAAO,EAAE;cAAG,CAAE;cAAApB,QAAA,eAE5DhC,OAAA,CAACT,WAAW;gBAACkB,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,GACXU,IAAI,CAACT,MAAM,KAAK,OAAO,gBACzBd,OAAA,CAACH,WAAW;cAACY,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAEnCU,IAAI,CAACf;UACN;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNb,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAuB,QAAA,gBAC7BhC,OAAA;cAAIS,SAAS,EAAC,wBAAwB;cAAAuB,QAAA,EAAET,IAAI,CAACjB;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxDb,OAAA;cAAGS,SAAS,EAAC,4BAA4B;cAAAuB,QAAA,EAAET,IAAI,CAAChB;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAGhEb,OAAA,CAACX,eAAe;cAAA2C,QAAA,EACbT,IAAI,CAACT,MAAM,KAAK,QAAQ,IAAIS,IAAI,CAACC,QAAQ,KAAKC,SAAS,iBACtDzB,OAAA,CAACZ,MAAM,CAAC6C,GAAG;gBACTxB,SAAS,EAAC,MAAM;gBAChBkC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAES,MAAM,EAAE;gBAAE,CAAE;gBACnCnB,OAAO,EAAE;kBAAEU,OAAO,EAAE,CAAC;kBAAES,MAAM,EAAE;gBAAO,CAAE;gBACxCC,IAAI,EAAE;kBAAEV,OAAO,EAAE,CAAC;kBAAES,MAAM,EAAE;gBAAE,CAAE;gBAAArB,QAAA,gBAEhChC,OAAA;kBAAKS,SAAS,EAAC,8DAA8D;kBAAAuB,QAAA,gBAC3EhC,OAAA;oBAAAgC,QAAA,EAAM;kBAAM;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnBb,OAAA;oBAAAgC,QAAA,GAAOT,IAAI,CAACC,QAAQ,EAAC,GAAC;kBAAA;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACNb,OAAA;kBAAKS,SAAS,EAAC,qCAAqC;kBAAAuB,QAAA,eAClDhC,OAAA,CAACZ,MAAM,CAAC6C,GAAG;oBACTxB,SAAS,EAAC,+BAA+B;oBACzCkC,OAAO,EAAE;sBAAEY,KAAK,EAAE;oBAAE,CAAE;oBACtBrB,OAAO,EAAE;sBAAEqB,KAAK,EAAE,GAAGhC,IAAI,CAACC,QAAQ;oBAAI,CAAE;oBACxCY,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI;kBAAE;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC,EAGjBU,IAAI,CAACT,MAAM,KAAK,WAAW,iBAC1Bd,OAAA,CAACZ,MAAM,CAAC6C,GAAG;cACTxB,SAAS,EAAC,0DAA0D;cACpEkC,OAAO,EAAE;gBAAEC,OAAO,EAAE;cAAE,CAAE;cACxBV,OAAO,EAAE;gBAAEU,OAAO,EAAE;cAAE,CAAE;cAAAZ,QAAA,gBAExBhC,OAAA,CAACJ,KAAK;gBAACa,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7Bb,OAAA;gBAAAgC,QAAA,GAAM,qBAAI,EAAC,IAAIwB,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA,GAvFRU,IAAI,CAAClB,EAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAwFF,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNb,OAAA,CAACZ,MAAM,CAAC6C,GAAG;MACTxB,SAAS,EAAC,gEAAgE;MAC1EkC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEc,CAAC,EAAE;MAAG,CAAE;MAC/BxB,OAAO,EAAE;QAAEU,OAAO,EAAE,CAAC;QAAEc,CAAC,EAAE;MAAE,CAAE;MAC9BtB,UAAU,EAAE;QAAEU,KAAK,EAAE;MAAI,CAAE;MAAAd,QAAA,gBAE3BhC,OAAA;QAAKS,SAAS,EAAC,8DAA8D;QAAAuB,QAAA,gBAC3EhC,OAAA;UAAAgC,QAAA,EAAM;QAAI;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjBb,OAAA;UAAAgC,QAAA,GAAO2B,IAAI,CAACC,KAAK,CAAEzD,KAAK,CAAC0D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChD,MAAM,KAAK,WAAW,CAAC,CAACY,MAAM,GAAGvB,KAAK,CAACuB,MAAM,GAAI,GAAG,CAAC,EAAC,GAAC;QAAA;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClG,CAAC,eACNb,OAAA;QAAKS,SAAS,EAAC,qCAAqC;QAAAuB,QAAA,eAClDhC,OAAA,CAACZ,MAAM,CAAC6C,GAAG;UACTxB,SAAS,EAAC,+BAA+B;UACzCkC,OAAO,EAAE;YAAEY,KAAK,EAAE;UAAE,CAAE;UACtBrB,OAAO,EAAE;YAAEqB,KAAK,EAAE,GAAIpD,KAAK,CAAC0D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChD,MAAM,KAAK,WAAW,CAAC,CAACY,MAAM,GAAGvB,KAAK,CAACuB,MAAM,GAAI,GAAG;UAAI,CAAE;UACpGU,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACX,EAAA,CA5OID,WAAqB;AAAA8D,EAAA,GAArB9D,WAAqB;AA8O3B,eAAeA,WAAW;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}