{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\components\\\\ProcessFlow.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Upload, CheckCircle, Settings, Brain, FileText, Download, Clock, AlertCircle, Zap } from 'lucide-react';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\n\n// 模拟已处理文件数据\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProcessFlow = () => {\n  _s();\n  const {\n    processState\n  } = useProcessFlowContext();\n\n  // 模拟已处理文件列表\n  const [processedFiles] = useState([{\n    id: '1',\n    filename: '5月13日入户.xlsx',\n    status: 'processed'\n  }, {\n    id: '2',\n    filename: '5月13日投诉.xlsx',\n    status: 'processed'\n  }, {\n    id: '3',\n    filename: '5月14日入户.xlsx',\n    status: 'processed'\n  }, {\n    id: '4',\n    filename: '5月14日投诉.xlsx',\n    status: 'processed'\n  }]);\n\n  // 配置下载目录（可配置）\n  const downloadDirectory = process.env.REACT_APP_DOWNLOAD_DIR || '/downloads';\n\n  // 下载分析结果报表\n  const downloadAnalysisReport = () => {\n    // 下载预设的分析结果报表\n    const link = document.createElement('a');\n    link.href = `${downloadDirectory}/文本分析结果报表.pdf`; // 从配置目录下载\n    link.download = '文本分析结果报表.pdf';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    console.log('下载文本分析结果报表完成');\n  };\n  const getStepIcon = stepId => {\n    switch (stepId) {\n      case 'upload':\n        return /*#__PURE__*/_jsxDEV(Upload, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 29\n        }, this);\n      case 'validate':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 31\n        }, this);\n      case 'configure':\n        return /*#__PURE__*/_jsxDEV(Settings, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 32\n        }, this);\n      case 'analyze':\n        return /*#__PURE__*/_jsxDEV(Brain, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 30\n        }, this);\n      case 'generate':\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 31\n        }, this);\n      case 'download':\n        return /*#__PURE__*/_jsxDEV(Download, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 31\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  const getStepColor = status => {\n    switch (status) {\n      case 'completed':\n        return 'text-tech-green border-tech-green bg-tech-green/10';\n      case 'active':\n        return 'text-tech-cyan border-tech-cyan bg-tech-cyan/10 animate-glow';\n      case 'error':\n        return 'text-red-400 border-red-400 bg-red-400/10';\n      default:\n        return 'text-gray-400 border-gray-600 bg-gray-600/10';\n    }\n  };\n  const getConnectorColor = (currentStatus, nextStatus) => {\n    if (currentStatus === 'completed') {\n      return 'bg-tech-green';\n    } else if (currentStatus === 'active') {\n      return 'bg-gradient-to-b from-tech-cyan to-gray-600';\n    }\n    return 'bg-gray-600';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"tech-card p-6 h-full flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-3 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"p-2 bg-gradient-to-r from-tech-cyan to-tech-purple rounded-lg\",\n        animate: {\n          rotate: [0, 360]\n        },\n        transition: {\n          duration: 20,\n          repeat: Infinity,\n          ease: \"linear\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Zap, {\n          className: \"w-6 h-6 text-white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-white\",\n          children: \"\\u5904\\u7406\\u6D41\\u7A0B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 text-sm\",\n          children: \"\\u5B9E\\u65F6\\u76D1\\u63A7\\u5206\\u6790\\u8FDB\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4 flex-1\",\n      children: processState.steps.map((step, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"relative\",\n        initial: {\n          opacity: 0,\n          x: -20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        children: [index < processState.steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute left-6 top-12 w-0.5 h-8 z-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-full h-full ${getConnectorColor(step.status, processState.steps[index + 1].status)} transition-all duration-500`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: `relative z-10 flex items-start space-x-4 p-4 rounded-lg border transition-all duration-300 ${getStepColor(step.status)}`,\n          whileHover: {\n            scale: 1.02\n          },\n          layout: true,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex-shrink-0 p-2 rounded-lg border ${getStepColor(step.status)}`,\n            children: step.status === 'active' ? /*#__PURE__*/_jsxDEV(motion.div, {\n              animate: {\n                rotate: 360\n              },\n              transition: {\n                duration: 2,\n                repeat: Infinity,\n                ease: \"linear\"\n              },\n              children: getStepIcon(step.id)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 19\n            }, this) : step.status === 'completed' ? /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0\n              },\n              animate: {\n                scale: 1\n              },\n              transition: {\n                type: \"spring\",\n                stiffness: 500,\n                damping: 30\n              },\n              children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 19\n            }, this) : step.status === 'error' ? /*#__PURE__*/_jsxDEV(AlertCircle, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 19\n            }, this) : getStepIcon(step.id)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-white\",\n              children: step.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mt-1\",\n              children: step.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: step.status === 'active' && step.progress !== undefined && /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"mt-3\",\n                initial: {\n                  opacity: 0,\n                  height: 0\n                },\n                animate: {\n                  opacity: 1,\n                  height: 'auto'\n                },\n                exit: {\n                  opacity: 0,\n                  height: 0\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-xs text-gray-400 mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u5904\\u7406\\u4E2D...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [step.progress, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full bg-gray-700 rounded-full h-2\",\n                  children: /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"progress-bar h-2 rounded-full\",\n                    initial: {\n                      width: 0\n                    },\n                    animate: {\n                      width: `${step.progress}%`\n                    },\n                    transition: {\n                      duration: 0.3\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), step.status === 'completed' && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"flex items-center space-x-1 mt-2 text-xs text-tech-green\",\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Clock, {\n                className: \"w-3 h-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"\\u5B8C\\u6210\\u4E8E \", new Date().toLocaleTimeString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 19\n            }, this), step.id === 'download' && step.status === 'completed' && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"mt-4 p-4 bg-green-900/20 border border-green-500/30 rounded-lg\",\n              initial: {\n                opacity: 0,\n                y: 10\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.3\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-green-400 font-semibold mb-3 flex items-center text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-4 h-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 23\n                }, this), \"\\u5206\\u6790\\u7ED3\\u679C\\u4E0B\\u8F7D\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-800/50 p-3 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(FileText, {\n                        className: \"w-4 h-4 text-red-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 209,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-300 text-sm\",\n                        children: \"\\u6587\\u672C\\u5206\\u6790\\u7ED3\\u679C\\u62A5\\u8868.pdf\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 210,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"\\u7EFC\\u5408\\u5206\\u6790\\u62A5\\u544A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: downloadAnalysisReport,\n                  className: \"w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-2 px-3 rounded transition-all duration-300 flex items-center justify-center text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(Download, {\n                    className: \"w-4 h-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 25\n                  }, this), \"\\u4E0B\\u8F7D\\u6587\\u672C\\u5206\\u6790\\u7ED3\\u679C\\u62A5\\u8868\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this)]\n      }, step.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"mt-6 p-4 tech-card\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.5\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between text-sm text-gray-400 mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u603B\\u4F53\\u8FDB\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [Math.round(processState.overallProgress), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full bg-gray-700 rounded-full h-2\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"progress-bar h-2 rounded-full\",\n          initial: {\n            width: 0\n          },\n          animate: {\n            width: `${processState.overallProgress}%`\n          },\n          transition: {\n            duration: 0.5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_s(ProcessFlow, \"j5LMU8bRKr8dlwOCQatnVsmBxfg=\", false, function () {\n  return [useProcessFlowContext];\n});\n_c = ProcessFlow;\nexport default ProcessFlow;\nvar _c;\n$RefreshReg$(_c, \"ProcessFlow\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "Upload", "CheckCircle", "Settings", "Brain", "FileText", "Download", "Clock", "AlertCircle", "Zap", "useProcessFlowContext", "jsxDEV", "_jsxDEV", "ProcessFlow", "_s", "processState", "processedFiles", "id", "filename", "status", "downloadDirectory", "process", "env", "REACT_APP_DOWNLOAD_DIR", "downloadAnalysisReport", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "getStepIcon", "stepId", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStepColor", "getConnectorColor", "currentStatus", "nextStatus", "children", "div", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "steps", "map", "step", "index", "initial", "opacity", "x", "delay", "length", "whileHover", "scale", "layout", "type", "stiffness", "damping", "title", "description", "progress", "undefined", "height", "exit", "width", "Date", "toLocaleTimeString", "y", "onClick", "Math", "round", "overallProgress", "_c", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/components/ProcessFlow.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Upload,\n  CheckCircle,\n  Settings,\n  Brain,\n  FileText,\n  Download,\n  Clock,\n  AlertCircle,\n  Zap\n} from 'lucide-react';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\n\n// 模拟已处理文件数据\ninterface ProcessedFile {\n  id: string;\n  filename: string;\n  status: 'processed';\n}\n\nconst ProcessFlow: React.FC = () => {\n  const { processState } = useProcessFlowContext();\n\n  // 模拟已处理文件列表\n  const [processedFiles] = useState<ProcessedFile[]>([\n    { id: '1', filename: '5月13日入户.xlsx', status: 'processed' },\n    { id: '2', filename: '5月13日投诉.xlsx', status: 'processed' },\n    { id: '3', filename: '5月14日入户.xlsx', status: 'processed' },\n    { id: '4', filename: '5月14日投诉.xlsx', status: 'processed' }\n  ]);\n\n  // 配置下载目录（可配置）\n  const downloadDirectory = process.env.REACT_APP_DOWNLOAD_DIR || '/downloads';\n\n  // 下载分析结果报表\n  const downloadAnalysisReport = () => {\n    // 下载预设的分析结果报表\n    const link = document.createElement('a');\n    link.href = `${downloadDirectory}/文本分析结果报表.pdf`; // 从配置目录下载\n    link.download = '文本分析结果报表.pdf';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    console.log('下载文本分析结果报表完成');\n  };\n\n  const getStepIcon = (stepId: string) => {\n    switch (stepId) {\n      case 'upload': return <Upload className=\"w-6 h-6\" />;\n      case 'validate': return <CheckCircle className=\"w-6 h-6\" />;\n      case 'configure': return <Settings className=\"w-6 h-6\" />;\n      case 'analyze': return <Brain className=\"w-6 h-6\" />;\n      case 'generate': return <FileText className=\"w-6 h-6\" />;\n      case 'download': return <Download className=\"w-6 h-6\" />;\n      default: return <FileText className=\"w-6 h-6\" />;\n    }\n  };\n\n\n\n  const getStepColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'text-tech-green border-tech-green bg-tech-green/10';\n      case 'active':\n        return 'text-tech-cyan border-tech-cyan bg-tech-cyan/10 animate-glow';\n      case 'error':\n        return 'text-red-400 border-red-400 bg-red-400/10';\n      default:\n        return 'text-gray-400 border-gray-600 bg-gray-600/10';\n    }\n  };\n\n  const getConnectorColor = (currentStatus: string, nextStatus: string) => {\n    if (currentStatus === 'completed') {\n      return 'bg-tech-green';\n    } else if (currentStatus === 'active') {\n      return 'bg-gradient-to-b from-tech-cyan to-gray-600';\n    }\n    return 'bg-gray-600';\n  };\n\n  return (\n    <div className=\"tech-card p-6 h-full flex flex-col\">\n      <div className=\"flex items-center space-x-3 mb-6\">\n        <motion.div\n          className=\"p-2 bg-gradient-to-r from-tech-cyan to-tech-purple rounded-lg\"\n          animate={{ rotate: [0, 360] }}\n          transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n        >\n          <Zap className=\"w-6 h-6 text-white\" />\n        </motion.div>\n        <div>\n          <h2 className=\"text-xl font-bold text-white\">处理流程</h2>\n          <p className=\"text-gray-400 text-sm\">实时监控分析进度</p>\n        </div>\n      </div>\n\n      <div className=\"space-y-4 flex-1\">\n        {processState.steps.map((step, index) => (\n          <motion.div\n            key={step.id}\n            className=\"relative\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: index * 0.1 }}\n          >\n            {/* 连接线 */}\n            {index < processState.steps.length - 1 && (\n              <div className=\"absolute left-6 top-12 w-0.5 h-8 z-0\">\n                <div\n                  className={`w-full h-full ${getConnectorColor(step.status, processState.steps[index + 1].status)} transition-all duration-500`}\n                />\n              </div>\n            )}\n\n            {/* 步骤卡片 */}\n            <motion.div\n              className={`relative z-10 flex items-start space-x-4 p-4 rounded-lg border transition-all duration-300 ${getStepColor(step.status)}`}\n              whileHover={{ scale: 1.02 }}\n              layout\n            >\n              {/* 图标 */}\n              <div className={`flex-shrink-0 p-2 rounded-lg border ${getStepColor(step.status)}`}>\n                {step.status === 'active' ? (\n                  <motion.div\n                    animate={{ rotate: 360 }}\n                    transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n                  >\n                    {getStepIcon(step.id)}\n                  </motion.div>\n                ) : step.status === 'completed' ? (\n                  <motion.div\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                  >\n                    <CheckCircle className=\"w-6 h-6\" />\n                  </motion.div>\n                ) : step.status === 'error' ? (\n                  <AlertCircle className=\"w-6 h-6\" />\n                ) : (\n                  getStepIcon(step.id)\n                )}\n              </div>\n\n              {/* 内容 */}\n              <div className=\"flex-1 min-w-0\">\n                <h3 className=\"font-medium text-white\">{step.title}</h3>\n                <p className=\"text-sm text-gray-400 mt-1\">{step.description}</p>\n                \n                {/* 进度条 */}\n                <AnimatePresence>\n                  {step.status === 'active' && step.progress !== undefined && (\n                    <motion.div\n                      className=\"mt-3\"\n                      initial={{ opacity: 0, height: 0 }}\n                      animate={{ opacity: 1, height: 'auto' }}\n                      exit={{ opacity: 0, height: 0 }}\n                    >\n                      <div className=\"flex items-center justify-between text-xs text-gray-400 mb-1\">\n                        <span>处理中...</span>\n                        <span>{step.progress}%</span>\n                      </div>\n                      <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                        <motion.div\n                          className=\"progress-bar h-2 rounded-full\"\n                          initial={{ width: 0 }}\n                          animate={{ width: `${step.progress}%` }}\n                          transition={{ duration: 0.3 }}\n                        />\n                      </div>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n\n                {/* 时间戳 */}\n                {step.status === 'completed' && (\n                  <motion.div\n                    className=\"flex items-center space-x-1 mt-2 text-xs text-tech-green\"\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                  >\n                    <Clock className=\"w-3 h-3\" />\n                    <span>完成于 {new Date().toLocaleTimeString()}</span>\n                  </motion.div>\n                )}\n\n                {/* 结果下载步骤的特殊内容 */}\n                {step.id === 'download' && step.status === 'completed' && (\n                  <motion.div\n                    className=\"mt-4 p-4 bg-green-900/20 border border-green-500/30 rounded-lg\"\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.3 }}\n                  >\n                    <h4 className=\"text-green-400 font-semibold mb-3 flex items-center text-sm\">\n                      <CheckCircle className=\"w-4 h-4 mr-2\" />\n                      分析结果下载\n                    </h4>\n\n                    <div className=\"space-y-3\">\n                      <div className=\"bg-gray-800/50 p-3 rounded-lg\">\n                        <div className=\"flex items-center justify-between\">\n                          <div className=\"flex items-center space-x-2\">\n                            <FileText className=\"w-4 h-4 text-red-400\" />\n                            <span className=\"text-gray-300 text-sm\">文本分析结果报表.pdf</span>\n                          </div>\n                          <span className=\"text-xs text-gray-500\">综合分析报告</span>\n                        </div>\n                      </div>\n\n                      <button\n                        onClick={downloadAnalysisReport}\n                        className=\"w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-2 px-3 rounded transition-all duration-300 flex items-center justify-center text-sm\"\n                      >\n                        <Download className=\"w-4 h-4 mr-2\" />\n                        下载文本分析结果报表\n                      </button>\n                    </div>\n                  </motion.div>\n                )}\n              </div>\n            </motion.div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* 总体进度 */}\n      <motion.div\n        className=\"mt-6 p-4 tech-card\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.5 }}\n      >\n        <div className=\"flex items-center justify-between text-sm text-gray-400 mb-2\">\n          <span>总体进度</span>\n          <span>{Math.round(processState.overallProgress)}%</span>\n        </div>\n        <div className=\"w-full bg-gray-700 rounded-full h-2\">\n          <motion.div\n            className=\"progress-bar h-2 rounded-full\"\n            initial={{ width: 0 }}\n            animate={{ width: `${processState.overallProgress}%` }}\n            transition={{ duration: 0.5 }}\n          />\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default ProcessFlow;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,MAAM,EACNC,WAAW,EACXC,QAAQ,EACRC,KAAK,EACLC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,GAAG,QACE,cAAc;AACrB,SAASC,qBAAqB,QAAQ,gCAAgC;;AAEtE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAa,CAAC,GAAGL,qBAAqB,CAAC,CAAC;;EAEhD;EACA,MAAM,CAACM,cAAc,CAAC,GAAGlB,QAAQ,CAAkB,CACjD;IAAEmB,EAAE,EAAE,GAAG;IAAEC,QAAQ,EAAE,cAAc;IAAEC,MAAM,EAAE;EAAY,CAAC,EAC1D;IAAEF,EAAE,EAAE,GAAG;IAAEC,QAAQ,EAAE,cAAc;IAAEC,MAAM,EAAE;EAAY,CAAC,EAC1D;IAAEF,EAAE,EAAE,GAAG;IAAEC,QAAQ,EAAE,cAAc;IAAEC,MAAM,EAAE;EAAY,CAAC,EAC1D;IAAEF,EAAE,EAAE,GAAG;IAAEC,QAAQ,EAAE,cAAc;IAAEC,MAAM,EAAE;EAAY,CAAC,CAC3D,CAAC;;EAEF;EACA,MAAMC,iBAAiB,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,YAAY;;EAE5E;EACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACA,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,GAAGR,iBAAiB,eAAe,CAAC,CAAC;IACjDK,IAAI,CAACI,QAAQ,GAAG,cAAc;IAC9BH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IAE/BS,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B,CAAC;EAED,MAAMC,WAAW,GAAIC,MAAc,IAAK;IACtC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,oBAAOzB,OAAA,CAACX,MAAM;UAACqC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD,KAAK,UAAU;QAAE,oBAAO9B,OAAA,CAACV,WAAW;UAACoC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK,WAAW;QAAE,oBAAO9B,OAAA,CAACT,QAAQ;UAACmC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD,KAAK,SAAS;QAAE,oBAAO9B,OAAA,CAACR,KAAK;UAACkC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD,KAAK,UAAU;QAAE,oBAAO9B,OAAA,CAACP,QAAQ;UAACiC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxD,KAAK,UAAU;QAAE,oBAAO9B,OAAA,CAACN,QAAQ;UAACgC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxD;QAAS,oBAAO9B,OAAA,CAACP,QAAQ;UAACiC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClD;EACF,CAAC;EAID,MAAMC,YAAY,GAAIxB,MAAc,IAAK;IACvC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,oDAAoD;MAC7D,KAAK,QAAQ;QACX,OAAO,8DAA8D;MACvE,KAAK,OAAO;QACV,OAAO,2CAA2C;MACpD;QACE,OAAO,8CAA8C;IACzD;EACF,CAAC;EAED,MAAMyB,iBAAiB,GAAGA,CAACC,aAAqB,EAAEC,UAAkB,KAAK;IACvE,IAAID,aAAa,KAAK,WAAW,EAAE;MACjC,OAAO,eAAe;IACxB,CAAC,MAAM,IAAIA,aAAa,KAAK,QAAQ,EAAE;MACrC,OAAO,6CAA6C;IACtD;IACA,OAAO,aAAa;EACtB,CAAC;EAED,oBACEjC,OAAA;IAAK0B,SAAS,EAAC,oCAAoC;IAAAS,QAAA,gBACjDnC,OAAA;MAAK0B,SAAS,EAAC,kCAAkC;MAAAS,QAAA,gBAC/CnC,OAAA,CAACb,MAAM,CAACiD,GAAG;QACTV,SAAS,EAAC,+DAA+D;QACzEW,OAAO,EAAE;UAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG;QAAE,CAAE;QAC9BC,UAAU,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEC,MAAM,EAAEC,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAE;QAAAR,QAAA,eAE/DnC,OAAA,CAACH,GAAG;UAAC6B,SAAS,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACb9B,OAAA;QAAAmC,QAAA,gBACEnC,OAAA;UAAI0B,SAAS,EAAC,8BAA8B;UAAAS,QAAA,EAAC;QAAI;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtD9B,OAAA;UAAG0B,SAAS,EAAC,uBAAuB;UAAAS,QAAA,EAAC;QAAQ;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN9B,OAAA;MAAK0B,SAAS,EAAC,kBAAkB;MAAAS,QAAA,EAC9BhC,YAAY,CAACyC,KAAK,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAClC/C,OAAA,CAACb,MAAM,CAACiD,GAAG;QAETV,SAAS,EAAC,UAAU;QACpBsB,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCb,OAAO,EAAE;UAAEY,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BX,UAAU,EAAE;UAAEY,KAAK,EAAEJ,KAAK,GAAG;QAAI,CAAE;QAAAZ,QAAA,GAGlCY,KAAK,GAAG5C,YAAY,CAACyC,KAAK,CAACQ,MAAM,GAAG,CAAC,iBACpCpD,OAAA;UAAK0B,SAAS,EAAC,sCAAsC;UAAAS,QAAA,eACnDnC,OAAA;YACE0B,SAAS,EAAE,iBAAiBM,iBAAiB,CAACc,IAAI,CAACvC,MAAM,EAAEJ,YAAY,CAACyC,KAAK,CAACG,KAAK,GAAG,CAAC,CAAC,CAACxC,MAAM,CAAC;UAA+B;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGD9B,OAAA,CAACb,MAAM,CAACiD,GAAG;UACTV,SAAS,EAAE,8FAA8FK,YAAY,CAACe,IAAI,CAACvC,MAAM,CAAC,EAAG;UACrI8C,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,MAAM;UAAApB,QAAA,gBAGNnC,OAAA;YAAK0B,SAAS,EAAE,uCAAuCK,YAAY,CAACe,IAAI,CAACvC,MAAM,CAAC,EAAG;YAAA4B,QAAA,EAChFW,IAAI,CAACvC,MAAM,KAAK,QAAQ,gBACvBP,OAAA,CAACb,MAAM,CAACiD,GAAG;cACTC,OAAO,EAAE;gBAAEC,MAAM,EAAE;cAAI,CAAE;cACzBC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,CAAC;gBAAEC,MAAM,EAAEC,QAAQ;gBAAEC,IAAI,EAAE;cAAS,CAAE;cAAAR,QAAA,EAE7DX,WAAW,CAACsB,IAAI,CAACzC,EAAE;YAAC;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,GACXgB,IAAI,CAACvC,MAAM,KAAK,WAAW,gBAC7BP,OAAA,CAACb,MAAM,CAACiD,GAAG;cACTY,OAAO,EAAE;gBAAEM,KAAK,EAAE;cAAE,CAAE;cACtBjB,OAAO,EAAE;gBAAEiB,KAAK,EAAE;cAAE,CAAE;cACtBf,UAAU,EAAE;gBAAEiB,IAAI,EAAE,QAAQ;gBAAEC,SAAS,EAAE,GAAG;gBAAEC,OAAO,EAAE;cAAG,CAAE;cAAAvB,QAAA,eAE5DnC,OAAA,CAACV,WAAW;gBAACoC,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,GACXgB,IAAI,CAACvC,MAAM,KAAK,OAAO,gBACzBP,OAAA,CAACJ,WAAW;cAAC8B,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAEnCN,WAAW,CAACsB,IAAI,CAACzC,EAAE;UACpB;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN9B,OAAA;YAAK0B,SAAS,EAAC,gBAAgB;YAAAS,QAAA,gBAC7BnC,OAAA;cAAI0B,SAAS,EAAC,wBAAwB;cAAAS,QAAA,EAAEW,IAAI,CAACa;YAAK;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxD9B,OAAA;cAAG0B,SAAS,EAAC,4BAA4B;cAAAS,QAAA,EAAEW,IAAI,CAACc;YAAW;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAGhE9B,OAAA,CAACZ,eAAe;cAAA+C,QAAA,EACbW,IAAI,CAACvC,MAAM,KAAK,QAAQ,IAAIuC,IAAI,CAACe,QAAQ,KAAKC,SAAS,iBACtD9D,OAAA,CAACb,MAAM,CAACiD,GAAG;gBACTV,SAAS,EAAC,MAAM;gBAChBsB,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEc,MAAM,EAAE;gBAAE,CAAE;gBACnC1B,OAAO,EAAE;kBAAEY,OAAO,EAAE,CAAC;kBAAEc,MAAM,EAAE;gBAAO,CAAE;gBACxCC,IAAI,EAAE;kBAAEf,OAAO,EAAE,CAAC;kBAAEc,MAAM,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,gBAEhCnC,OAAA;kBAAK0B,SAAS,EAAC,8DAA8D;kBAAAS,QAAA,gBAC3EnC,OAAA;oBAAAmC,QAAA,EAAM;kBAAM;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnB9B,OAAA;oBAAAmC,QAAA,GAAOW,IAAI,CAACe,QAAQ,EAAC,GAAC;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACN9B,OAAA;kBAAK0B,SAAS,EAAC,qCAAqC;kBAAAS,QAAA,eAClDnC,OAAA,CAACb,MAAM,CAACiD,GAAG;oBACTV,SAAS,EAAC,+BAA+B;oBACzCsB,OAAO,EAAE;sBAAEiB,KAAK,EAAE;oBAAE,CAAE;oBACtB5B,OAAO,EAAE;sBAAE4B,KAAK,EAAE,GAAGnB,IAAI,CAACe,QAAQ;oBAAI,CAAE;oBACxCtB,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI;kBAAE;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC,EAGjBgB,IAAI,CAACvC,MAAM,KAAK,WAAW,iBAC1BP,OAAA,CAACb,MAAM,CAACiD,GAAG;cACTV,SAAS,EAAC,0DAA0D;cACpEsB,OAAO,EAAE;gBAAEC,OAAO,EAAE;cAAE,CAAE;cACxBZ,OAAO,EAAE;gBAAEY,OAAO,EAAE;cAAE,CAAE;cAAAd,QAAA,gBAExBnC,OAAA,CAACL,KAAK;gBAAC+B,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7B9B,OAAA;gBAAAmC,QAAA,GAAM,qBAAI,EAAC,IAAI+B,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CACb,EAGAgB,IAAI,CAACzC,EAAE,KAAK,UAAU,IAAIyC,IAAI,CAACvC,MAAM,KAAK,WAAW,iBACpDP,OAAA,CAACb,MAAM,CAACiD,GAAG;cACTV,SAAS,EAAC,gEAAgE;cAC1EsB,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEmB,CAAC,EAAE;cAAG,CAAE;cAC/B/B,OAAO,EAAE;gBAAEY,OAAO,EAAE,CAAC;gBAAEmB,CAAC,EAAE;cAAE,CAAE;cAC9B7B,UAAU,EAAE;gBAAEY,KAAK,EAAE;cAAI,CAAE;cAAAhB,QAAA,gBAE3BnC,OAAA;gBAAI0B,SAAS,EAAC,6DAA6D;gBAAAS,QAAA,gBACzEnC,OAAA,CAACV,WAAW;kBAACoC,SAAS,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wCAE1C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEL9B,OAAA;gBAAK0B,SAAS,EAAC,WAAW;gBAAAS,QAAA,gBACxBnC,OAAA;kBAAK0B,SAAS,EAAC,+BAA+B;kBAAAS,QAAA,eAC5CnC,OAAA;oBAAK0B,SAAS,EAAC,mCAAmC;oBAAAS,QAAA,gBAChDnC,OAAA;sBAAK0B,SAAS,EAAC,6BAA6B;sBAAAS,QAAA,gBAC1CnC,OAAA,CAACP,QAAQ;wBAACiC,SAAS,EAAC;sBAAsB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7C9B,OAAA;wBAAM0B,SAAS,EAAC,uBAAuB;wBAAAS,QAAA,EAAC;sBAAY;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACN9B,OAAA;sBAAM0B,SAAS,EAAC,uBAAuB;sBAAAS,QAAA,EAAC;oBAAM;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN9B,OAAA;kBACEqE,OAAO,EAAEzD,sBAAuB;kBAChCc,SAAS,EAAC,6LAA6L;kBAAAS,QAAA,gBAEvMnC,OAAA,CAACN,QAAQ;oBAACgC,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gEAEvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA,GA1HRgB,IAAI,CAACzC,EAAE;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA2HF,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN9B,OAAA,CAACb,MAAM,CAACiD,GAAG;MACTV,SAAS,EAAC,oBAAoB;MAC9BsB,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEmB,CAAC,EAAE;MAAG,CAAE;MAC/B/B,OAAO,EAAE;QAAEY,OAAO,EAAE,CAAC;QAAEmB,CAAC,EAAE;MAAE,CAAE;MAC9B7B,UAAU,EAAE;QAAEY,KAAK,EAAE;MAAI,CAAE;MAAAhB,QAAA,gBAE3BnC,OAAA;QAAK0B,SAAS,EAAC,8DAA8D;QAAAS,QAAA,gBAC3EnC,OAAA;UAAAmC,QAAA,EAAM;QAAI;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjB9B,OAAA;UAAAmC,QAAA,GAAOmC,IAAI,CAACC,KAAK,CAACpE,YAAY,CAACqE,eAAe,CAAC,EAAC,GAAC;QAAA;UAAA7C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eACN9B,OAAA;QAAK0B,SAAS,EAAC,qCAAqC;QAAAS,QAAA,eAClDnC,OAAA,CAACb,MAAM,CAACiD,GAAG;UACTV,SAAS,EAAC,+BAA+B;UACzCsB,OAAO,EAAE;YAAEiB,KAAK,EAAE;UAAE,CAAE;UACtB5B,OAAO,EAAE;YAAE4B,KAAK,EAAE,GAAG9D,YAAY,CAACqE,eAAe;UAAI,CAAE;UACvDjC,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAvOID,WAAqB;EAAA,QACAH,qBAAqB;AAAA;AAAA2E,EAAA,GAD1CxE,WAAqB;AAyO3B,eAAeA,WAAW;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}