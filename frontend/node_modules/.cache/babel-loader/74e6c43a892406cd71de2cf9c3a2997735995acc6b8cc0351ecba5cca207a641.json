{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\contexts\\\\ProcessFlowContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext } from 'react';\nimport { useProcessFlow } from '../hooks/useProcessFlow';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProcessFlowContext = /*#__PURE__*/createContext(undefined);\nexport const ProcessFlowProvider = ({\n  children\n}) => {\n  _s();\n  const processFlow = useProcessFlow();\n  return /*#__PURE__*/_jsxDEV(ProcessFlowContext.Provider, {\n    value: processFlow,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(ProcessFlowProvider, \"rLV8188fxMvdzrjTaJowDi5GwzQ=\", false, function () {\n  return [useProcessFlow];\n});\n_c = ProcessFlowProvider;\nexport const useProcessFlowContext = () => {\n  _s2();\n  const context = useContext(ProcessFlowContext);\n  if (context === undefined) {\n    throw new Error('useProcessFlowContext must be used within a ProcessFlowProvider');\n  }\n  return context;\n};\n_s2(useProcessFlowContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ProcessFlowProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useProcessFlow", "jsxDEV", "_jsxDEV", "ProcessFlowContext", "undefined", "ProcessFlowProvider", "children", "_s", "processFlow", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useProcessFlowContext", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/contexts/ProcessFlowContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, ReactNode } from 'react';\nimport { useProcessFlow, ProcessFlowState } from '../hooks/useProcessFlow';\n\ninterface ProcessFlowContextType {\n  processState: ProcessFlowState;\n  activateStep: (stepId: string) => void;\n  updateStepProgress: (stepId: string, progress: number) => void;\n  completeStep: (stepId: string) => void;\n  setStepError: (stepId: string, errorMessage?: string) => void;\n  resetFlow: () => void;\n  addFileProgress: (fileId: string, fileName: string) => void;\n  updateFileProgress: (fileId: string, stepId: string, progress: number) => void;\n  onFileUploaded: (fileId: string, fileName: string) => void;\n  onFileValidation: (fileId: string) => void;\n  onFileProcessing: (fileId: string) => void;\n  onReportGeneration: () => void;\n}\n\nconst ProcessFlowContext = createContext<ProcessFlowContextType | undefined>(undefined);\n\nexport const ProcessFlowProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\n  const processFlow = useProcessFlow();\n\n  return (\n    <ProcessFlowContext.Provider value={processFlow}>\n      {children}\n    </ProcessFlowContext.Provider>\n  );\n};\n\nexport const useProcessFlowContext = () => {\n  const context = useContext(ProcessFlowContext);\n  if (context === undefined) {\n    throw new Error('useProcessFlowContext must be used within a ProcessFlowProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,QAAmB,OAAO;AACnE,SAASC,cAAc,QAA0B,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAiB3E,MAAMC,kBAAkB,gBAAGL,aAAa,CAAqCM,SAAS,CAAC;AAEvF,OAAO,MAAMC,mBAAsD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtF,MAAMC,WAAW,GAAGR,cAAc,CAAC,CAAC;EAEpC,oBACEE,OAAA,CAACC,kBAAkB,CAACM,QAAQ;IAACC,KAAK,EAAEF,WAAY;IAAAF,QAAA,EAC7CA;EAAQ;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACkB,CAAC;AAElC,CAAC;AAACP,EAAA,CARWF,mBAAsD;EAAA,QAC7CL,cAAc;AAAA;AAAAe,EAAA,GADvBV,mBAAsD;AAUnE,OAAO,MAAMW,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzC,MAAMC,OAAO,GAAGnB,UAAU,CAACI,kBAAkB,CAAC;EAC9C,IAAIe,OAAO,KAAKd,SAAS,EAAE;IACzB,MAAM,IAAIe,KAAK,CAAC,iEAAiE,CAAC;EACpF;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,qBAAqB;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}