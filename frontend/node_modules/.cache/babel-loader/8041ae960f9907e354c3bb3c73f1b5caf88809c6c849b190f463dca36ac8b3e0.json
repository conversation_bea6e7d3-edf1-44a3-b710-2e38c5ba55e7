{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\components\\\\FileUploadSimple.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\nimport { Upload, FileText, CheckCircle, AlertCircle, Settings, Play, Download, Trash2, Eye, Zap } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FileUploadSimple = () => {\n  _s();\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [selectedMonth, setSelectedMonth] = useState('2024-01');\n  const [selectedFileType, setSelectedFileType] = useState('household');\n  const {\n    onFileUploaded,\n    onFileValidation,\n    onFileProcessing,\n    onReportGeneration,\n    resetFlow\n  } = useProcessFlowContext();\n  const handleFileUpload = event => {\n    const files = event.target.files;\n    if (files) {\n      Array.from(files).forEach(file => {\n        const newFile = {\n          id: Math.random().toString(36).substr(2, 9),\n          filename: file.name,\n          file_type: selectedFileType,\n          month: selectedMonth,\n          status: 'uploaded',\n          upload_time: new Date().toISOString(),\n          file_path: `/uploads/${file.name}`\n        };\n        setUploadedFiles(prev => [...prev, newFile]);\n\n        // 触发流程联动\n        onFileUploaded();\n      });\n    }\n  };\n  const validateFile = fileId => {\n    setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n      ...file,\n      status: 'validated'\n    } : file));\n\n    // 触发验证流程联动\n    onFileValidation();\n  };\n  const processFile = fileId => {\n    setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n      ...file,\n      status: 'processing'\n    } : file));\n\n    // 触发处理流程联动\n    onFileProcessing();\n    setTimeout(() => {\n      setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n        ...file,\n        status: 'processed'\n      } : file));\n    }, 3000);\n  };\n  const deleteFile = fileId => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  };\n  const processAllFiles = () => {\n    setIsProcessing(true);\n    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');\n    validatedFiles.forEach((file, index) => {\n      setTimeout(() => {\n        processFile(file.id);\n      }, index * 1000);\n    });\n    setTimeout(() => {\n      setIsProcessing(false);\n    }, validatedFiles.length * 1000 + 2000);\n  };\n\n  // 下载分析结果\n  const downloadAnalysisResult = (fileName, fileType) => {\n    // 模拟下载文件\n    const link = document.createElement('a');\n    link.href = '#'; // 实际项目中这里应该是后端API地址\n    link.download = `${fileName}_analysis_result.${fileType}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    // 显示下载成功提示\n    console.log(`下载 ${fileName} 的${fileType.toUpperCase()}分析结果`);\n  };\n\n  // 下载所有分析结果\n  const downloadAllResults = () => {\n    const processedFiles = uploadedFiles.filter(f => f.status === 'processed');\n    processedFiles.forEach(file => {\n      downloadAnalysisResult(file.name, 'excel');\n    });\n\n    // 下载完成后重置流程\n    setTimeout(() => {\n      resetFlow();\n      setUploadedFiles([]);\n    }, 1000);\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'uploaded':\n        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/30';\n      case 'validated':\n        return 'text-tech-green bg-tech-green/10 border-tech-green/30';\n      case 'processing':\n        return 'text-tech-cyan bg-tech-cyan/10 border-tech-cyan/30';\n      case 'processed':\n        return 'text-tech-purple bg-tech-purple/10 border-tech-purple/30';\n      case 'error':\n        return 'text-red-400 bg-red-400/10 border-red-400/30';\n      default:\n        return 'text-gray-400 bg-gray-400/10 border-gray-400/30';\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'uploaded':\n        return /*#__PURE__*/_jsxDEV(Upload, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 31\n        }, this);\n      case 'validated':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 32\n        }, this);\n      case 'processing':\n        return /*#__PURE__*/_jsxDEV(Settings, {\n          className: \"w-4 h-4 animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 33\n        }, this);\n      case 'processed':\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 32\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 28\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6 h-full flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-6\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-white\",\n          children: \"\\u6570\\u636E\\u6587\\u4EF6\\u4E0A\\u4F20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Zap, {\n            className: \"w-5 h-5 text-tech-cyan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-tech-cyan text-sm\",\n            children: \"\\u667A\\u80FD\\u5904\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300 mb-2\",\n            children: \"\\u6587\\u4EF6\\u7C7B\\u578B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedFileType,\n            onChange: e => setSelectedFileType(e.target.value),\n            className: \"tech-input w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"household\",\n              children: \"\\u5165\\u6237\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"complaint\",\n              children: \"\\u6295\\u8BC9\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300 mb-2\",\n            children: \"\\u6708\\u4EFD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"month\",\n            value: selectedMonth,\n            onChange: e => setSelectedMonth(e.target.value),\n            className: \"tech-input w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-2 border-dashed border-gray-600 hover:border-tech-cyan/50 rounded-lg p-8 text-center transition-all duration-300\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 bg-tech-cyan/20 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(Upload, {\n              className: \"w-8 h-8 text-tech-cyan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-medium text-white\",\n              children: \"\\u70B9\\u51FB\\u4E0A\\u4F20\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mt-1\",\n              children: \"\\u652F\\u6301 .xlsx, .xls \\u683C\\u5F0F\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            multiple: true,\n            accept: \".xlsx,.xls\",\n            onChange: handleFileUpload,\n            className: \"tech-button\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-6 flex flex-col\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.2\n      },\n      style: {\n        height: '400px'\n      } // 固定高度\n      ,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-white\",\n          children: [\"\\u5DF2\\u4E0A\\u4F20\\u6587\\u4EF6 (\", uploadedFiles.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: processAllFiles,\n            disabled: isProcessing || uploadedFiles.filter(f => f.status === 'validated').length === 0,\n            className: \"tech-button text-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: [/*#__PURE__*/_jsxDEV(Play, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), \"\\u4E00\\u952E\\u5904\\u7406\\u5168\\u90E8\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto space-y-3 pr-2\",\n        style: {\n          maxHeight: '300px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: uploadedFiles.map(file => /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"tech-border p-4 rounded-lg\",\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            exit: {\n              opacity: 0,\n              x: 20\n            },\n            layout: true,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-2 rounded-lg border ${getStatusColor(file.status)}`,\n                  children: getStatusIcon(file.status)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-white truncate\",\n                    children: file.filename\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: file.file_type === 'household' ? '入户数据' : '投诉数据'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 278,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: file.month\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 279,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-2 py-1 rounded-full text-xs ${getStatusColor(file.status)}`,\n                      children: [file.status === 'uploaded' && '已上传', file.status === 'validated' && '已验证', file.status === 'processing' && '处理中', file.status === 'processed' && '已处理', file.status === 'error' && '错误']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 280,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [file.status === 'uploaded' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => validateFile(file.id),\n                  className: \"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\",\n                  title: \"\\u9A8C\\u8BC1\\u6587\\u4EF6\",\n                  children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 23\n                }, this), file.status === 'validated' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => processFile(file.id),\n                  className: \"p-2 text-tech-cyan hover:bg-tech-cyan/10 rounded-lg transition-colors\",\n                  title: \"\\u667A\\u80FD\\u5904\\u7406\",\n                  children: /*#__PURE__*/_jsxDEV(Play, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 23\n                }, this), file.status === 'processed' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 text-tech-purple hover:bg-tech-purple/10 rounded-lg transition-colors\",\n                    title: \"\\u9884\\u89C8\\u7ED3\\u679C\",\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\",\n                    title: \"\\u4E0B\\u8F7D\\u7ED3\\u679C\",\n                    children: /*#__PURE__*/_jsxDEV(Download, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 324,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => deleteFile(file.id),\n                  className: \"p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors\",\n                  title: \"\\u5220\\u9664\\u6587\\u4EF6\",\n                  children: /*#__PURE__*/_jsxDEV(Trash2, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this), file.status === 'processing' && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"mt-3\",\n              initial: {\n                opacity: 0,\n                height: 0\n              },\n              animate: {\n                opacity: 1,\n                height: 'auto'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-700 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"progress-bar h-2 rounded-full\",\n                  initial: {\n                    width: 0\n                  },\n                  animate: {\n                    width: '100%'\n                  },\n                  transition: {\n                    duration: 3,\n                    ease: \"easeInOut\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-tech-cyan mt-1\",\n                children: \"\\u6B63\\u5728\\u8FDB\\u884C\\u667A\\u80FD\\u5206\\u6790...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 19\n            }, this)]\n          }, file.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), uploadedFiles.length === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"text-center py-12 text-gray-400\",\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u6682\\u65E0\\u4E0A\\u4F20\\u6587\\u4EF6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm mt-1\",\n            children: \"\\u8BF7\\u5148\\u4E0A\\u4F20\\u6570\\u636E\\u6587\\u4EF6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this), uploadedFiles.some(f => f.status === 'processed') && /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-6\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.4\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-white mb-4\",\n          children: \"\\u751F\\u6210\\u5206\\u6790\\u62A5\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"tech-button text-lg px-8 py-3\",\n          onClick: onReportGeneration,\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"w-5 h-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this), \"\\u751F\\u6210\\u667A\\u80FD\\u5206\\u6790\\u62A5\\u8868\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 165,\n    columnNumber: 5\n  }, this);\n};\n_s(FileUploadSimple, \"vRkyXj0doiNwcaxrrM43geiEbFY=\", false, function () {\n  return [useProcessFlowContext];\n});\n_c = FileUploadSimple;\nexport default FileUploadSimple;\nvar _c;\n$RefreshReg$(_c, \"FileUploadSimple\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "useProcessFlowContext", "Upload", "FileText", "CheckCircle", "AlertCircle", "Settings", "Play", "Download", "Trash2", "Eye", "Zap", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FileUploadSimple", "_s", "uploadedFiles", "setUploadedFiles", "isProcessing", "setIsProcessing", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMonth", "selectedFileType", "setSelectedFileType", "onFileUploaded", "onFileValidation", "onFileProcessing", "onReportGeneration", "resetFlow", "handleFileUpload", "event", "files", "target", "Array", "from", "for<PERSON>ach", "file", "newFile", "id", "Math", "random", "toString", "substr", "filename", "name", "file_type", "month", "status", "upload_time", "Date", "toISOString", "file_path", "prev", "validateFile", "fileId", "map", "processFile", "setTimeout", "deleteFile", "filter", "processAllFiles", "validatedFiles", "index", "length", "downloadAnalysisResult", "fileName", "fileType", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "toUpperCase", "downloadAllResults", "processedFiles", "f", "getStatusColor", "getStatusIcon", "className", "_jsxFileName", "lineNumber", "columnNumber", "children", "div", "initial", "opacity", "y", "animate", "value", "onChange", "e", "type", "multiple", "accept", "transition", "delay", "style", "height", "onClick", "disabled", "maxHeight", "x", "exit", "layout", "title", "width", "duration", "ease", "some", "_c", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/components/FileUploadSimple.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\nimport { \n  Upload, \n  FileText, \n  CheckCircle, \n  AlertCircle, \n  Settings, \n  Play, \n  Download,\n  Trash2,\n  Eye,\n  Zap\n} from 'lucide-react';\n\ninterface UploadedFile {\n  id: string;\n  filename: string;\n  file_type: 'household' | 'complaint';\n  month: string;\n  status: 'uploaded' | 'validated' | 'processing' | 'processed' | 'error';\n  upload_time: string;\n  file_path: string;\n  error_message?: string;\n}\n\nconst FileUploadSimple: React.FC = () => {\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [selectedMonth, setSelectedMonth] = useState('2024-01');\n  const [selectedFileType, setSelectedFileType] = useState<'household' | 'complaint'>('household');\n\n  const { onFileUploaded, onFileValidation, onFileProcessing, onReportGeneration, resetFlow } = useProcessFlowContext();\n\n  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files;\n    if (files) {\n      Array.from(files).forEach(file => {\n        const newFile: UploadedFile = {\n          id: Math.random().toString(36).substr(2, 9),\n          filename: file.name,\n          file_type: selectedFileType,\n          month: selectedMonth,\n          status: 'uploaded',\n          upload_time: new Date().toISOString(),\n          file_path: `/uploads/${file.name}`\n        };\n        \n        setUploadedFiles(prev => [...prev, newFile]);\n\n        // 触发流程联动\n        onFileUploaded();\n      });\n    }\n  };\n\n  const validateFile = (fileId: string) => {\n    setUploadedFiles(prev =>\n      prev.map(file =>\n        file.id === fileId\n          ? { ...file, status: 'validated' }\n          : file\n      )\n    );\n\n    // 触发验证流程联动\n    onFileValidation();\n  };\n\n  const processFile = (fileId: string) => {\n    setUploadedFiles(prev =>\n      prev.map(file =>\n        file.id === fileId\n          ? { ...file, status: 'processing' }\n          : file\n      )\n    );\n\n    // 触发处理流程联动\n    onFileProcessing();\n\n    setTimeout(() => {\n      setUploadedFiles(prev =>\n        prev.map(file =>\n          file.id === fileId\n            ? { ...file, status: 'processed' }\n            : file\n        )\n      );\n    }, 3000);\n  };\n\n  const deleteFile = (fileId: string) => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  };\n\n  const processAllFiles = () => {\n    setIsProcessing(true);\n    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');\n\n    validatedFiles.forEach((file, index) => {\n      setTimeout(() => {\n        processFile(file.id);\n      }, index * 1000);\n    });\n\n    setTimeout(() => {\n      setIsProcessing(false);\n    }, validatedFiles.length * 1000 + 2000);\n  };\n\n  // 下载分析结果\n  const downloadAnalysisResult = (fileName: string, fileType: 'excel' | 'pdf' | 'json') => {\n    // 模拟下载文件\n    const link = document.createElement('a');\n    link.href = '#'; // 实际项目中这里应该是后端API地址\n    link.download = `${fileName}_analysis_result.${fileType}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    // 显示下载成功提示\n    console.log(`下载 ${fileName} 的${fileType.toUpperCase()}分析结果`);\n  };\n\n  // 下载所有分析结果\n  const downloadAllResults = () => {\n    const processedFiles = uploadedFiles.filter(f => f.status === 'processed');\n\n    processedFiles.forEach(file => {\n      downloadAnalysisResult(file.name, 'excel');\n    });\n\n    // 下载完成后重置流程\n    setTimeout(() => {\n      resetFlow();\n      setUploadedFiles([]);\n    }, 1000);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'uploaded': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/30';\n      case 'validated': return 'text-tech-green bg-tech-green/10 border-tech-green/30';\n      case 'processing': return 'text-tech-cyan bg-tech-cyan/10 border-tech-cyan/30';\n      case 'processed': return 'text-tech-purple bg-tech-purple/10 border-tech-purple/30';\n      case 'error': return 'text-red-400 bg-red-400/10 border-red-400/30';\n      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/30';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'uploaded': return <Upload className=\"w-4 h-4\" />;\n      case 'validated': return <CheckCircle className=\"w-4 h-4\" />;\n      case 'processing': return <Settings className=\"w-4 h-4 animate-spin\" />;\n      case 'processed': return <FileText className=\"w-4 h-4\" />;\n      case 'error': return <AlertCircle className=\"w-4 h-4\" />;\n      default: return <FileText className=\"w-4 h-4\" />;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6 h-full flex flex-col\">\n      {/* 文件上传区域 */}\n      <motion.div\n        className=\"tech-card p-6\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-xl font-bold text-white\">数据文件上传</h2>\n          <div className=\"flex items-center space-x-2\">\n            <Zap className=\"w-5 h-5 text-tech-cyan\" />\n            <span className=\"text-tech-cyan text-sm\">智能处理</span>\n          </div>\n        </div>\n\n        {/* 文件类型和月份选择 */}\n        <div className=\"grid grid-cols-2 gap-4 mb-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n              文件类型\n            </label>\n            <select\n              value={selectedFileType}\n              onChange={(e) => setSelectedFileType(e.target.value as 'household' | 'complaint')}\n              className=\"tech-input w-full\"\n            >\n              <option value=\"household\">入户数据</option>\n              <option value=\"complaint\">投诉数据</option>\n            </select>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n              月份\n            </label>\n            <input\n              type=\"month\"\n              value={selectedMonth}\n              onChange={(e) => setSelectedMonth(e.target.value)}\n              className=\"tech-input w-full\"\n            />\n          </div>\n        </div>\n\n        {/* 文件上传区域 */}\n        <div className=\"border-2 border-dashed border-gray-600 hover:border-tech-cyan/50 rounded-lg p-8 text-center transition-all duration-300\">\n          <div className=\"flex flex-col items-center space-y-4\">\n            <div className=\"p-4 bg-tech-cyan/20 rounded-full\">\n              <Upload className=\"w-8 h-8 text-tech-cyan\" />\n            </div>\n            <div>\n              <p className=\"text-lg font-medium text-white\">\n                点击上传文件\n              </p>\n              <p className=\"text-sm text-gray-400 mt-1\">\n                支持 .xlsx, .xls 格式文件\n              </p>\n            </div>\n            <input\n              type=\"file\"\n              multiple\n              accept=\".xlsx,.xls\"\n              onChange={handleFileUpload}\n              className=\"tech-button\"\n            />\n          </div>\n        </div>\n      </motion.div>\n\n      {/* 已上传文件列表 */}\n      <motion.div\n        className=\"tech-card p-6 flex flex-col\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.2 }}\n        style={{ height: '400px' }} // 固定高度\n      >\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-white\">\n            已上传文件 ({uploadedFiles.length})\n          </h3>\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={processAllFiles}\n              disabled={isProcessing || uploadedFiles.filter(f => f.status === 'validated').length === 0}\n              className=\"tech-button text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <Play className=\"w-4 h-4 mr-2\" />\n              一键处理全部\n            </button>\n          </div>\n        </div>\n\n        <div className=\"flex-1 overflow-y-auto space-y-3 pr-2\" style={{ maxHeight: '300px' }}>\n          <AnimatePresence>\n            {uploadedFiles.map((file) => (\n              <motion.div\n                key={file.id}\n                className=\"tech-border p-4 rounded-lg\"\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: 20 }}\n                layout\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3 flex-1\">\n                    <div className={`p-2 rounded-lg border ${getStatusColor(file.status)}`}>\n                      {getStatusIcon(file.status)}\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <h4 className=\"font-medium text-white truncate\">\n                        {file.filename}\n                      </h4>\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-400\">\n                        <span>{file.file_type === 'household' ? '入户数据' : '投诉数据'}</span>\n                        <span>{file.month}</span>\n                        <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(file.status)}`}>\n                          {file.status === 'uploaded' && '已上传'}\n                          {file.status === 'validated' && '已验证'}\n                          {file.status === 'processing' && '处理中'}\n                          {file.status === 'processed' && '已处理'}\n                          {file.status === 'error' && '错误'}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2\">\n                    {file.status === 'uploaded' && (\n                      <button\n                        onClick={() => validateFile(file.id)}\n                        className=\"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\"\n                        title=\"验证文件\"\n                      >\n                        <CheckCircle className=\"w-4 h-4\" />\n                      </button>\n                    )}\n\n                    {file.status === 'validated' && (\n                      <button\n                        onClick={() => processFile(file.id)}\n                        className=\"p-2 text-tech-cyan hover:bg-tech-cyan/10 rounded-lg transition-colors\"\n                        title=\"智能处理\"\n                      >\n                        <Play className=\"w-4 h-4\" />\n                      </button>\n                    )}\n\n                    {file.status === 'processed' && (\n                      <>\n                        <button\n                          className=\"p-2 text-tech-purple hover:bg-tech-purple/10 rounded-lg transition-colors\"\n                          title=\"预览结果\"\n                        >\n                          <Eye className=\"w-4 h-4\" />\n                        </button>\n                        <button\n                          className=\"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\"\n                          title=\"下载结果\"\n                        >\n                          <Download className=\"w-4 h-4\" />\n                        </button>\n                      </>\n                    )}\n\n                    <button\n                      onClick={() => deleteFile(file.id)}\n                      className=\"p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors\"\n                      title=\"删除文件\"\n                    >\n                      <Trash2 className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                </div>\n\n                {file.status === 'processing' && (\n                  <motion.div\n                    className=\"mt-3\"\n                    initial={{ opacity: 0, height: 0 }}\n                    animate={{ opacity: 1, height: 'auto' }}\n                  >\n                    <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                      <motion.div\n                        className=\"progress-bar h-2 rounded-full\"\n                        initial={{ width: 0 }}\n                        animate={{ width: '100%' }}\n                        transition={{ duration: 3, ease: \"easeInOut\" }}\n                      />\n                    </div>\n                    <p className=\"text-xs text-tech-cyan mt-1\">正在进行智能分析...</p>\n                  </motion.div>\n                )}\n              </motion.div>\n            ))}\n          </AnimatePresence>\n\n          {uploadedFiles.length === 0 && (\n            <motion.div\n              className=\"text-center py-12 text-gray-400\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n            >\n              <FileText className=\"w-12 h-12 mx-auto mb-4 opacity-50\" />\n              <p>暂无上传文件</p>\n              <p className=\"text-sm mt-1\">请先上传数据文件</p>\n            </motion.div>\n          )}\n        </div>\n      </motion.div>\n\n      {/* 生成报表按钮 */}\n      {uploadedFiles.some(f => f.status === 'processed') && (\n        <motion.div\n          className=\"tech-card p-6\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n        >\n          <div className=\"text-center\">\n            <h3 className=\"text-lg font-semibold text-white mb-4\">\n              生成分析报表\n            </h3>\n            <button\n              className=\"tech-button text-lg px-8 py-3\"\n              onClick={onReportGeneration}\n            >\n              <FileText className=\"w-5 h-5 mr-2\" />\n              生成智能分析报表\n            </button>\n          </div>\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\nexport default FileUploadSimple;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SACEC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,WAAW,EACXC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,GAAG,EACHC,GAAG,QACE,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAatB,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAA4B,WAAW,CAAC;EAEhG,MAAM;IAAE4B,cAAc;IAAEC,gBAAgB;IAAEC,gBAAgB;IAAEC,kBAAkB;IAAEC;EAAU,CAAC,GAAG7B,qBAAqB,CAAC,CAAC;EAErH,MAAM8B,gBAAgB,GAAIC,KAA0C,IAAK;IACvE,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,EAAE;MACTE,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,OAAO,CAACC,IAAI,IAAI;QAChC,MAAMC,OAAqB,GAAG;UAC5BC,EAAE,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;UAC3CC,QAAQ,EAAEP,IAAI,CAACQ,IAAI;UACnBC,SAAS,EAAEvB,gBAAgB;UAC3BwB,KAAK,EAAE1B,aAAa;UACpB2B,MAAM,EAAE,UAAU;UAClBC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACrCC,SAAS,EAAE,YAAYf,IAAI,CAACQ,IAAI;QAClC,CAAC;QAED3B,gBAAgB,CAACmC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEf,OAAO,CAAC,CAAC;;QAE5C;QACAb,cAAc,CAAC,CAAC;MAClB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM6B,YAAY,GAAIC,MAAc,IAAK;IACvCrC,gBAAgB,CAACmC,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACnB,IAAI,IACXA,IAAI,CAACE,EAAE,KAAKgB,MAAM,GACd;MAAE,GAAGlB,IAAI;MAAEW,MAAM,EAAE;IAAY,CAAC,GAChCX,IACN,CACF,CAAC;;IAED;IACAX,gBAAgB,CAAC,CAAC;EACpB,CAAC;EAED,MAAM+B,WAAW,GAAIF,MAAc,IAAK;IACtCrC,gBAAgB,CAACmC,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACnB,IAAI,IACXA,IAAI,CAACE,EAAE,KAAKgB,MAAM,GACd;MAAE,GAAGlB,IAAI;MAAEW,MAAM,EAAE;IAAa,CAAC,GACjCX,IACN,CACF,CAAC;;IAED;IACAV,gBAAgB,CAAC,CAAC;IAElB+B,UAAU,CAAC,MAAM;MACfxC,gBAAgB,CAACmC,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACnB,IAAI,IACXA,IAAI,CAACE,EAAE,KAAKgB,MAAM,GACd;QAAE,GAAGlB,IAAI;QAAEW,MAAM,EAAE;MAAY,CAAC,GAChCX,IACN,CACF,CAAC;IACH,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMsB,UAAU,GAAIJ,MAAc,IAAK;IACrCrC,gBAAgB,CAACmC,IAAI,IAAIA,IAAI,CAACO,MAAM,CAACvB,IAAI,IAAIA,IAAI,CAACE,EAAE,KAAKgB,MAAM,CAAC,CAAC;EACnE,CAAC;EAED,MAAMM,eAAe,GAAGA,CAAA,KAAM;IAC5BzC,eAAe,CAAC,IAAI,CAAC;IACrB,MAAM0C,cAAc,GAAG7C,aAAa,CAAC2C,MAAM,CAACvB,IAAI,IAAIA,IAAI,CAACW,MAAM,KAAK,WAAW,CAAC;IAEhFc,cAAc,CAAC1B,OAAO,CAAC,CAACC,IAAI,EAAE0B,KAAK,KAAK;MACtCL,UAAU,CAAC,MAAM;QACfD,WAAW,CAACpB,IAAI,CAACE,EAAE,CAAC;MACtB,CAAC,EAAEwB,KAAK,GAAG,IAAI,CAAC;IAClB,CAAC,CAAC;IAEFL,UAAU,CAAC,MAAM;MACftC,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,EAAE0C,cAAc,CAACE,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;EACzC,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAGA,CAACC,QAAgB,EAAEC,QAAkC,KAAK;IACvF;IACA,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,GAAG,CAAC,CAAC;IACjBH,IAAI,CAACI,QAAQ,GAAG,GAAGN,QAAQ,oBAAoBC,QAAQ,EAAE;IACzDE,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;;IAE/B;IACAS,OAAO,CAACC,GAAG,CAAC,MAAMZ,QAAQ,KAAKC,QAAQ,CAACY,WAAW,CAAC,CAAC,MAAM,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,cAAc,GAAGhE,aAAa,CAAC2C,MAAM,CAACsB,CAAC,IAAIA,CAAC,CAAClC,MAAM,KAAK,WAAW,CAAC;IAE1EiC,cAAc,CAAC7C,OAAO,CAACC,IAAI,IAAI;MAC7B4B,sBAAsB,CAAC5B,IAAI,CAACQ,IAAI,EAAE,OAAO,CAAC;IAC5C,CAAC,CAAC;;IAEF;IACAa,UAAU,CAAC,MAAM;MACf7B,SAAS,CAAC,CAAC;MACXX,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMiE,cAAc,GAAInC,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,UAAU;QAAE,OAAO,uDAAuD;MAC/E,KAAK,WAAW;QAAE,OAAO,uDAAuD;MAChF,KAAK,YAAY;QAAE,OAAO,oDAAoD;MAC9E,KAAK,WAAW;QAAE,OAAO,0DAA0D;MACnF,KAAK,OAAO;QAAE,OAAO,8CAA8C;MACnE;QAAS,OAAO,iDAAiD;IACnE;EACF,CAAC;EAED,MAAMoC,aAAa,GAAIpC,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,UAAU;QAAE,oBAAOpC,OAAA,CAACX,MAAM;UAACoF,SAAS,EAAC;QAAS;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,WAAW;QAAE,oBAAO5E,OAAA,CAACT,WAAW;UAACkF,SAAS,EAAC;QAAS;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5D,KAAK,YAAY;QAAE,oBAAO5E,OAAA,CAACP,QAAQ;UAACgF,SAAS,EAAC;QAAsB;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvE,KAAK,WAAW;QAAE,oBAAO5E,OAAA,CAACV,QAAQ;UAACmF,SAAS,EAAC;QAAS;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD,KAAK,OAAO;QAAE,oBAAO5E,OAAA,CAACR,WAAW;UAACiF,SAAS,EAAC;QAAS;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxD;QAAS,oBAAO5E,OAAA,CAACV,QAAQ;UAACmF,SAAS,EAAC;QAAS;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClD;EACF,CAAC;EAED,oBACE5E,OAAA;IAAKyE,SAAS,EAAC,gCAAgC;IAAAI,QAAA,gBAE7C7E,OAAA,CAACd,MAAM,CAAC4F,GAAG;MACTL,SAAS,EAAC,eAAe;MACzBM,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBAE9B7E,OAAA;QAAKyE,SAAS,EAAC,wCAAwC;QAAAI,QAAA,gBACrD7E,OAAA;UAAIyE,SAAS,EAAC,8BAA8B;UAAAI,QAAA,EAAC;QAAM;UAAAvB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxD5E,OAAA;UAAKyE,SAAS,EAAC,6BAA6B;UAAAI,QAAA,gBAC1C7E,OAAA,CAACF,GAAG;YAAC2E,SAAS,EAAC;UAAwB;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1C5E,OAAA;YAAMyE,SAAS,EAAC,wBAAwB;YAAAI,QAAA,EAAC;UAAI;YAAAvB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5E,OAAA;QAAKyE,SAAS,EAAC,6BAA6B;QAAAI,QAAA,gBAC1C7E,OAAA;UAAA6E,QAAA,gBACE7E,OAAA;YAAOyE,SAAS,EAAC,8CAA8C;YAAAI,QAAA,EAAC;UAEhE;YAAAvB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5E,OAAA;YACEmF,KAAK,EAAExE,gBAAiB;YACxByE,QAAQ,EAAGC,CAAC,IAAKzE,mBAAmB,CAACyE,CAAC,CAAChE,MAAM,CAAC8D,KAAkC,CAAE;YAClFV,SAAS,EAAC,mBAAmB;YAAAI,QAAA,gBAE7B7E,OAAA;cAAQmF,KAAK,EAAC,WAAW;cAAAN,QAAA,EAAC;YAAI;cAAAvB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvC5E,OAAA;cAAQmF,KAAK,EAAC,WAAW;cAAAN,QAAA,EAAC;YAAI;cAAAvB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN5E,OAAA;UAAA6E,QAAA,gBACE7E,OAAA;YAAOyE,SAAS,EAAC,8CAA8C;YAAAI,QAAA,EAAC;UAEhE;YAAAvB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5E,OAAA;YACEsF,IAAI,EAAC,OAAO;YACZH,KAAK,EAAE1E,aAAc;YACrB2E,QAAQ,EAAGC,CAAC,IAAK3E,gBAAgB,CAAC2E,CAAC,CAAChE,MAAM,CAAC8D,KAAK,CAAE;YAClDV,SAAS,EAAC;UAAmB;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5E,OAAA;QAAKyE,SAAS,EAAC,yHAAyH;QAAAI,QAAA,eACtI7E,OAAA;UAAKyE,SAAS,EAAC,sCAAsC;UAAAI,QAAA,gBACnD7E,OAAA;YAAKyE,SAAS,EAAC,kCAAkC;YAAAI,QAAA,eAC/C7E,OAAA,CAACX,MAAM;cAACoF,SAAS,EAAC;YAAwB;cAAAnB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACN5E,OAAA;YAAA6E,QAAA,gBACE7E,OAAA;cAAGyE,SAAS,EAAC,gCAAgC;cAAAI,QAAA,EAAC;YAE9C;cAAAvB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ5E,OAAA;cAAGyE,SAAS,EAAC,4BAA4B;cAAAI,QAAA,EAAC;YAE1C;cAAAvB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN5E,OAAA;YACEsF,IAAI,EAAC,MAAM;YACXC,QAAQ;YACRC,MAAM,EAAC,YAAY;YACnBJ,QAAQ,EAAElE,gBAAiB;YAC3BuD,SAAS,EAAC;UAAa;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAtB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGb5E,OAAA,CAACd,MAAM,CAAC4F,GAAG;MACTL,SAAS,EAAC,6BAA6B;MACvCM,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BQ,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BC,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE,CAAC;MAAA;MAAAf,QAAA,gBAE5B7E,OAAA;QAAKyE,SAAS,EAAC,wCAAwC;QAAAI,QAAA,gBACrD7E,OAAA;UAAIyE,SAAS,EAAC,kCAAkC;UAAAI,QAAA,GAAC,kCACxC,EAACxE,aAAa,CAAC+C,MAAM,EAAC,GAC/B;QAAA;UAAAE,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL5E,OAAA;UAAKyE,SAAS,EAAC,gBAAgB;UAAAI,QAAA,eAC7B7E,OAAA;YACE6F,OAAO,EAAE5C,eAAgB;YACzB6C,QAAQ,EAAEvF,YAAY,IAAIF,aAAa,CAAC2C,MAAM,CAACsB,CAAC,IAAIA,CAAC,CAAClC,MAAM,KAAK,WAAW,CAAC,CAACgB,MAAM,KAAK,CAAE;YAC3FqB,SAAS,EAAC,qEAAqE;YAAAI,QAAA,gBAE/E7E,OAAA,CAACN,IAAI;cAAC+E,SAAS,EAAC;YAAc;cAAAnB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wCAEnC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5E,OAAA;QAAKyE,SAAS,EAAC,uCAAuC;QAACkB,KAAK,EAAE;UAAEI,SAAS,EAAE;QAAQ,CAAE;QAAAlB,QAAA,gBACnF7E,OAAA,CAACb,eAAe;UAAA0F,QAAA,EACbxE,aAAa,CAACuC,GAAG,CAAEnB,IAAI,iBACtBzB,OAAA,CAACd,MAAM,CAAC4F,GAAG;YAETL,SAAS,EAAC,4BAA4B;YACtCM,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEgB,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCd,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEgB,CAAC,EAAE;YAAE,CAAE;YAC9BC,IAAI,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAEgB,CAAC,EAAE;YAAG,CAAE;YAC5BE,MAAM;YAAArB,QAAA,gBAEN7E,OAAA;cAAKyE,SAAS,EAAC,mCAAmC;cAAAI,QAAA,gBAChD7E,OAAA;gBAAKyE,SAAS,EAAC,oCAAoC;gBAAAI,QAAA,gBACjD7E,OAAA;kBAAKyE,SAAS,EAAE,yBAAyBF,cAAc,CAAC9C,IAAI,CAACW,MAAM,CAAC,EAAG;kBAAAyC,QAAA,EACpEL,aAAa,CAAC/C,IAAI,CAACW,MAAM;gBAAC;kBAAAkB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACN5E,OAAA;kBAAKyE,SAAS,EAAC,gBAAgB;kBAAAI,QAAA,gBAC7B7E,OAAA;oBAAIyE,SAAS,EAAC,iCAAiC;oBAAAI,QAAA,EAC5CpD,IAAI,CAACO;kBAAQ;oBAAAsB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACL5E,OAAA;oBAAKyE,SAAS,EAAC,mDAAmD;oBAAAI,QAAA,gBAChE7E,OAAA;sBAAA6E,QAAA,EAAOpD,IAAI,CAACS,SAAS,KAAK,WAAW,GAAG,MAAM,GAAG;oBAAM;sBAAAoB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC/D5E,OAAA;sBAAA6E,QAAA,EAAOpD,IAAI,CAACU;oBAAK;sBAAAmB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzB5E,OAAA;sBAAMyE,SAAS,EAAE,kCAAkCF,cAAc,CAAC9C,IAAI,CAACW,MAAM,CAAC,EAAG;sBAAAyC,QAAA,GAC9EpD,IAAI,CAACW,MAAM,KAAK,UAAU,IAAI,KAAK,EACnCX,IAAI,CAACW,MAAM,KAAK,WAAW,IAAI,KAAK,EACpCX,IAAI,CAACW,MAAM,KAAK,YAAY,IAAI,KAAK,EACrCX,IAAI,CAACW,MAAM,KAAK,WAAW,IAAI,KAAK,EACpCX,IAAI,CAACW,MAAM,KAAK,OAAO,IAAI,IAAI;oBAAA;sBAAAkB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAtB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5E,OAAA;gBAAKyE,SAAS,EAAC,6BAA6B;gBAAAI,QAAA,GACzCpD,IAAI,CAACW,MAAM,KAAK,UAAU,iBACzBpC,OAAA;kBACE6F,OAAO,EAAEA,CAAA,KAAMnD,YAAY,CAACjB,IAAI,CAACE,EAAE,CAAE;kBACrC8C,SAAS,EAAC,yEAAyE;kBACnF0B,KAAK,EAAC,0BAAM;kBAAAtB,QAAA,eAEZ7E,OAAA,CAACT,WAAW;oBAACkF,SAAS,EAAC;kBAAS;oBAAAnB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CACT,EAEAnD,IAAI,CAACW,MAAM,KAAK,WAAW,iBAC1BpC,OAAA;kBACE6F,OAAO,EAAEA,CAAA,KAAMhD,WAAW,CAACpB,IAAI,CAACE,EAAE,CAAE;kBACpC8C,SAAS,EAAC,uEAAuE;kBACjF0B,KAAK,EAAC,0BAAM;kBAAAtB,QAAA,eAEZ7E,OAAA,CAACN,IAAI;oBAAC+E,SAAS,EAAC;kBAAS;oBAAAnB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CACT,EAEAnD,IAAI,CAACW,MAAM,KAAK,WAAW,iBAC1BpC,OAAA,CAAAE,SAAA;kBAAA2E,QAAA,gBACE7E,OAAA;oBACEyE,SAAS,EAAC,2EAA2E;oBACrF0B,KAAK,EAAC,0BAAM;oBAAAtB,QAAA,eAEZ7E,OAAA,CAACH,GAAG;sBAAC4E,SAAS,EAAC;oBAAS;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACT5E,OAAA;oBACEyE,SAAS,EAAC,yEAAyE;oBACnF0B,KAAK,EAAC,0BAAM;oBAAAtB,QAAA,eAEZ7E,OAAA,CAACL,QAAQ;sBAAC8E,SAAS,EAAC;oBAAS;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA,eACT,CACH,eAED5E,OAAA;kBACE6F,OAAO,EAAEA,CAAA,KAAM9C,UAAU,CAACtB,IAAI,CAACE,EAAE,CAAE;kBACnC8C,SAAS,EAAC,mEAAmE;kBAC7E0B,KAAK,EAAC,0BAAM;kBAAAtB,QAAA,eAEZ7E,OAAA,CAACJ,MAAM;oBAAC6E,SAAS,EAAC;kBAAS;oBAAAnB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAtB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELnD,IAAI,CAACW,MAAM,KAAK,YAAY,iBAC3BpC,OAAA,CAACd,MAAM,CAAC4F,GAAG;cACTL,SAAS,EAAC,MAAM;cAChBM,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEY,MAAM,EAAE;cAAE,CAAE;cACnCV,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEY,MAAM,EAAE;cAAO,CAAE;cAAAf,QAAA,gBAExC7E,OAAA;gBAAKyE,SAAS,EAAC,qCAAqC;gBAAAI,QAAA,eAClD7E,OAAA,CAACd,MAAM,CAAC4F,GAAG;kBACTL,SAAS,EAAC,+BAA+B;kBACzCM,OAAO,EAAE;oBAAEqB,KAAK,EAAE;kBAAE,CAAE;kBACtBlB,OAAO,EAAE;oBAAEkB,KAAK,EAAE;kBAAO,CAAE;kBAC3BX,UAAU,EAAE;oBAAEY,QAAQ,EAAE,CAAC;oBAAEC,IAAI,EAAE;kBAAY;gBAAE;kBAAAhD,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAtB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5E,OAAA;gBAAGyE,SAAS,EAAC,6BAA6B;gBAAAI,QAAA,EAAC;cAAW;gBAAAvB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CACb;UAAA,GA9FInD,IAAI,CAACE,EAAE;YAAA2B,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+FF,CACb;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa,CAAC,EAEjBvE,aAAa,CAAC+C,MAAM,KAAK,CAAC,iBACzBpD,OAAA,CAACd,MAAM,CAAC4F,GAAG;UACTL,SAAS,EAAC,iCAAiC;UAC3CM,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UAAAH,QAAA,gBAExB7E,OAAA,CAACV,QAAQ;YAACmF,SAAS,EAAC;UAAmC;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1D5E,OAAA;YAAA6E,QAAA,EAAG;UAAM;YAAAvB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACb5E,OAAA;YAAGyE,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAC;UAAQ;YAAAvB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CACb;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAtB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGZvE,aAAa,CAACkG,IAAI,CAACjC,CAAC,IAAIA,CAAC,CAAClC,MAAM,KAAK,WAAW,CAAC,iBAChDpC,OAAA,CAACd,MAAM,CAAC4F,GAAG;MACTL,SAAS,EAAC,eAAe;MACzBM,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BQ,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAb,QAAA,eAE3B7E,OAAA;QAAKyE,SAAS,EAAC,aAAa;QAAAI,QAAA,gBAC1B7E,OAAA;UAAIyE,SAAS,EAAC,uCAAuC;UAAAI,QAAA,EAAC;QAEtD;UAAAvB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL5E,OAAA;UACEyE,SAAS,EAAC,+BAA+B;UACzCoB,OAAO,EAAE7E,kBAAmB;UAAA6D,QAAA,gBAE5B7E,OAAA,CAACV,QAAQ;YAACmF,SAAS,EAAC;UAAc;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oDAEvC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAtB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb;EAAA;IAAAtB,QAAA,EAAAoB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxE,EAAA,CAlXID,gBAA0B;EAAA,QAMgEf,qBAAqB;AAAA;AAAAoH,EAAA,GAN/GrG,gBAA0B;AAoXhC,eAAeA,gBAAgB;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}