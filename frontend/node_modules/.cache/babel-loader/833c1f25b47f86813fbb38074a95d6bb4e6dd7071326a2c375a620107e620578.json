{"ast": null, "code": "/**\n * lucide-react v0.292.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst FolderHeart = createLucideIcon(\"FolderHeart\", [[\"path\", {\n  d: \"M11 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v1.5\",\n  key: \"6hud8k\"\n}], [\"path\", {\n  d: \"M13.9 17.45c-1.2-1.2-1.14-2.8-.2-3.73a2.43 2.43 0 0 1 3.44 0l.36.34.34-.34a2.43 2.43 0 0 1 3.45-.01v0c.95.95 1 2.53-.2 3.74L17.5 21Z\",\n  key: \"vgq86i\"\n}]]);\nexport { FolderHeart as default };\n//# sourceMappingURL=folder-heart.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}