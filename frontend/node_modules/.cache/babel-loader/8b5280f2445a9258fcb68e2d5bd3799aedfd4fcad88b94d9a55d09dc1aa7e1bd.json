{"ast": null, "code": "import { moveItem } from '../../../utils/array.mjs';\nimport { mix } from '../../../utils/mix.mjs';\nfunction checkReorder(order, value, offset, velocity) {\n  if (!velocity) return order;\n  const index = order.findIndex(item => item.value === value);\n  if (index === -1) return order;\n  const nextOffset = velocity > 0 ? 1 : -1;\n  const nextItem = order[index + nextOffset];\n  if (!nextItem) return order;\n  const item = order[index];\n  const nextLayout = nextItem.layout;\n  const nextItemCenter = mix(nextLayout.min, nextLayout.max, 0.5);\n  if (nextOffset === 1 && item.layout.max + offset > nextItemCenter || nextOffset === -1 && item.layout.min + offset < nextItemCenter) {\n    return moveItem(order, index, index + nextOffset);\n  }\n  return order;\n}\nexport { checkReorder };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}