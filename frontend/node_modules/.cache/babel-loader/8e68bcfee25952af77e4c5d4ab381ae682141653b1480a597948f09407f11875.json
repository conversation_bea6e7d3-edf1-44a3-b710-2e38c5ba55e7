{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\components\\\\FileUploadSimple.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\nimport { Upload, FileText, CheckCircle, Settings, Play, Trash2, Terminal, Eye, X } from 'lucide-react';\nimport { StreamOutput } from './StreamOutput';\nimport { configureStageOutput } from '../data/streamOutputs/configureStage';\nimport { analyzeStageOutput } from '../data/streamOutputs/analyzeStage';\nimport { generateStageOutput } from '../data/streamOutputs/generateStage';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FileUploadSimple = () => {\n  _s();\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [currentStreamStage, setCurrentStreamStage] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [showPreview, setShowPreview] = useState(false);\n  const fileInputRef = useRef(null);\n  const {\n    onFileUploaded,\n    onFileValidation,\n    onFileProcessing,\n    onReportGeneration,\n    resetFlow,\n    processState\n  } = useProcessFlowContext();\n  const handleFileUpload = async event => {\n    const files = event.target.files;\n    if (files) {\n      for (const file of Array.from(files)) {\n        const newFile = {\n          id: Math.random().toString(36).substr(2, 9),\n          filename: file.name,\n          file_type: file.type || 'text/plain',\n          size: file.size,\n          status: 'uploaded',\n          upload_time: new Date().toISOString(),\n          file_path: `/uploads/${file.name}`\n        };\n\n        // 读取文件内容用于预览\n        try {\n          const content = await readFileContent(file);\n          newFile.preview_content = content.substring(0, 1000); // 只保存前1000字符用于预览\n        } catch (error) {\n          console.error('读取文件内容失败:', error);\n        }\n        setUploadedFiles(prev => [...prev, newFile]);\n        onFileUploaded(newFile.id, newFile.filename);\n      }\n    }\n  };\n\n  // 读取文件内容的辅助函数\n  const readFileContent = file => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = e => {\n        var _e$target;\n        resolve((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n      };\n      reader.onerror = reject;\n      reader.readAsText(file, 'UTF-8');\n    });\n  };\n  const validateFile = fileId => {\n    setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n      ...file,\n      status: 'validated'\n    } : file));\n    onFileValidation(fileId);\n  };\n  const processFile = fileId => {\n    setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n      ...file,\n      status: 'processing'\n    } : file));\n    onFileProcessing(fileId);\n\n    // 模拟处理完成\n    setTimeout(() => {\n      setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n        ...file,\n        status: 'processed'\n      } : file));\n    }, 5000);\n  };\n  const deleteFile = fileId => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  };\n\n  // 预览文件\n  const previewFile = file => {\n    setSelectedFile(file);\n    setShowPreview(true);\n  };\n  const processAllFiles = () => {\n    setIsProcessing(true);\n    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');\n\n    // 开始流式输出 - 解析配置阶段\n    setCurrentStreamStage('configure');\n\n    // 模拟处理流程\n    setTimeout(() => {\n      // 解析配置完成，开始智能分析\n      setCurrentStreamStage('analyze');\n\n      // 同时开始处理文件\n      validatedFiles.forEach((file, index) => {\n        setTimeout(() => {\n          processFile(file.id);\n        }, index * 1000);\n      });\n      setTimeout(() => {\n        // 智能分析完成，直接开始报表生成（不需要用户点击）\n        setCurrentStreamStage('generate');\n        onReportGeneration(); // 自动触发报表生成\n\n        setTimeout(() => {\n          // 所有处理完成\n          setIsProcessing(false);\n          setCurrentStreamStage(null);\n        }, 8000); // 报表生成阶段时间\n      }, 6000); // 智能分析阶段时间\n    }, 4000); // 解析配置阶段时间\n  };\n\n  // 配置下载目录（可配置）\n  const downloadDirectory = process.env.REACT_APP_DOWNLOAD_DIR || '/downloads';\n  const downloadAnalysisReport = () => {\n    // 下载预设的分析结果报表\n    const link = document.createElement('a');\n    link.href = `${downloadDirectory}/文本分析结果报表.pdf`; // 从配置目录下载\n    link.download = '文本分析结果报表.pdf';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    // 下载完成后重置\n    setTimeout(() => {\n      setUploadedFiles([]);\n      resetFlow();\n    }, 1000);\n    console.log('下载文本分析结果报表完成，系统已重置');\n  };\n\n  // 获取文件进度\n  const getFileProgress = fileId => {\n    return processState.fileProgresses.find(fp => fp.fileId === fileId);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6 h-full flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-4\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-white\",\n          children: \"\\u6570\\u636E\\u6587\\u4EF6\\u4E0A\\u4F20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 max-w-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              ref: fileInputRef,\n              type: \"file\",\n              multiple: true,\n              accept: \".txt,.doc,.docx,.pdf,.xlsx,.xls,.csv\",\n              onChange: handleFileUpload,\n              className: \"hidden\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var _fileInputRef$current;\n                return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n              },\n              className: \"tech-button w-full px-6 py-3 text-base font-medium\",\n              children: [/*#__PURE__*/_jsxDEV(Upload, {\n                className: \"w-5 h-5 mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), \"\\u9009\\u62E9\\u6587\\u672C\\u6587\\u4EF6\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"tech-card flex flex-col\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        style: {\n          height: '400px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-4 border-b border-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold text-white\",\n            children: [\"\\u5DF2\\u4E0A\\u4F20\\u6587\\u4EF6 (\", uploadedFiles.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), uploadedFiles.filter(f => f.status === 'validated').length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: processAllFiles,\n            disabled: isProcessing,\n            className: \"tech-button text-sm px-4 py-2 disabled:opacity-50\",\n            children: [/*#__PURE__*/_jsxDEV(Play, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this), isProcessing ? '处理中...' : '一键处理全部']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 overflow-y-auto p-4\",\n          children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n            children: uploadedFiles.map(file => /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"bg-gray-800/50 rounded-lg p-4 mb-3 border border-gray-700\",\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              exit: {\n                opacity: 0,\n                y: -20\n              },\n              layout: true,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(FileText, {\n                      className: \"w-5 h-5 text-tech-cyan\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-medium text-white\",\n                        children: file.filename\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 248,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-400\",\n                        children: [(file.size / 1024).toFixed(1), \" KB \\u2022 \", new Date(file.upload_time).toLocaleString()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 249,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `px-2 py-1 rounded text-xs ${file.status === 'uploaded' ? 'bg-blue-900/50 text-blue-300' : file.status === 'validated' ? 'bg-green-900/50 text-green-300' : file.status === 'processing' ? 'bg-yellow-900/50 text-yellow-300' : file.status === 'processed' ? 'bg-purple-900/50 text-purple-300' : 'bg-red-900/50 text-red-300'}`,\n                    children: file.status === 'uploaded' ? '已上传' : file.status === 'validated' ? '已验证' : file.status === 'processing' ? '处理中' : file.status === 'processed' ? '已完成' : '错误'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => previewFile(file),\n                    className: \"p-2 text-tech-purple hover:bg-tech-purple/10 rounded-lg transition-colors\",\n                    title: \"\\u5728\\u7EBF\\u9884\\u89C8\",\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 23\n                  }, this), file.status === 'uploaded' && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => validateFile(file.id),\n                    className: \"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\",\n                    title: \"\\u9A8C\\u8BC1\\u6587\\u4EF6\",\n                    children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 25\n                  }, this), file.status === 'validated' && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => processFile(file.id),\n                    className: \"p-2 text-tech-cyan hover:bg-tech-cyan/10 rounded-lg transition-colors\",\n                    title: \"\\u667A\\u80FD\\u5904\\u7406\",\n                    children: /*#__PURE__*/_jsxDEV(Settings, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 296,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => deleteFile(file.id),\n                    className: \"p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors\",\n                    title: \"\\u5220\\u9664\\u6587\\u4EF6\",\n                    children: /*#__PURE__*/_jsxDEV(Trash2, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this), (() => {\n                const fileProgress = getFileProgress(file.id);\n                if (fileProgress && fileProgress.stepProgress > 0) {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between text-xs text-gray-400 mb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [fileProgress.currentStep === 'configure' ? '配置中' : fileProgress.currentStep === 'analyze' ? '分析中' : fileProgress.currentStep === 'generate' ? '生成中' : '处理中', \"...\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 317,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [fileProgress.stepProgress, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 320,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-full bg-gray-700 rounded-full h-1.5\",\n                      children: /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"progress-bar h-1.5 rounded-full\",\n                        initial: {\n                          width: 0\n                        },\n                        animate: {\n                          width: `${fileProgress.stepProgress}%`\n                        },\n                        transition: {\n                          duration: 0.3\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 323,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 322,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 25\n                  }, this);\n                }\n                return null;\n              })()]\n            }, file.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), uploadedFiles.length === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"text-center py-12 text-gray-400\",\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(FileText, {\n              className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u6682\\u65E0\\u4E0A\\u4F20\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm mt-1\",\n              children: \"\\u8BF7\\u5148\\u4E0A\\u4F20\\u6570\\u636E\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"tech-card flex flex-col\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        style: {\n          height: '400px'\n        },\n        children: currentStreamStage ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [currentStreamStage === 'configure' && /*#__PURE__*/_jsxDEV(StreamOutput, {\n            content: configureStageOutput,\n            isActive: true,\n            title: \"\\u89E3\\u6790\\u914D\\u7F6E\\u9636\\u6BB5\",\n            onComplete: () => {},\n            speed: 100\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 17\n          }, this), currentStreamStage === 'analyze' && /*#__PURE__*/_jsxDEV(StreamOutput, {\n            content: analyzeStageOutput,\n            isActive: true,\n            title: \"\\u667A\\u80FD\\u5206\\u6790\\u9636\\u6BB5\",\n            onComplete: () => {},\n            speed: 120\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 17\n          }, this), currentStreamStage === 'generate' && /*#__PURE__*/_jsxDEV(StreamOutput, {\n            content: generateStageOutput,\n            isActive: true,\n            title: \"\\u62A5\\u8868\\u751F\\u6210\\u9636\\u6BB5\",\n            onComplete: () => {},\n            speed: 80\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 flex items-center justify-center text-gray-400\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(Terminal, {\n              className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"AI\\u5904\\u7406\\u8F93\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm\",\n              children: \"\\u70B9\\u51FB\\\"\\u4E00\\u952E\\u5904\\u7406\\u5168\\u90E8\\\"\\u5F00\\u59CB\\u667A\\u80FD\\u5206\\u6790\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: showPreview && selectedFile && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        exit: {\n          opacity: 0\n        },\n        onClick: () => setShowPreview(false),\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"tech-card max-w-4xl w-full max-h-[80vh] flex flex-col\",\n          initial: {\n            scale: 0.9,\n            opacity: 0\n          },\n          animate: {\n            scale: 1,\n            opacity: 1\n          },\n          exit: {\n            scale: 0.9,\n            opacity: 0\n          },\n          onClick: e => e.stopPropagation(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-4 border-b border-gray-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-white\",\n                children: selectedFile.filename\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-400\",\n                children: [(selectedFile.size / 1024).toFixed(1), \" KB \\u2022 \", new Date(selectedFile.upload_time).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowPreview(false),\n              className: \"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(X, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 overflow-auto p-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-900/50 rounded-lg p-4 font-mono text-sm text-gray-300 whitespace-pre-wrap\",\n              children: selectedFile.preview_content || '无法预览此文件内容'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this);\n};\n_s(FileUploadSimple, \"Yd9qS14ftQaAUwLScmGI/2jam4U=\", false, function () {\n  return [useProcessFlowContext];\n});\n_c = FileUploadSimple;\nexport default FileUploadSimple;\nvar _c;\n$RefreshReg$(_c, \"FileUploadSimple\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "motion", "AnimatePresence", "useProcessFlowContext", "Upload", "FileText", "CheckCircle", "Settings", "Play", "Trash2", "Terminal", "Eye", "X", "StreamOutput", "configureStageOutput", "analyzeStageOutput", "generateStageOutput", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FileUploadSimple", "_s", "uploadedFiles", "setUploadedFiles", "isProcessing", "setIsProcessing", "currentStreamStage", "setCurrentStreamStage", "selectedFile", "setSelectedFile", "showPreview", "setShowPreview", "fileInputRef", "onFileUploaded", "onFileValidation", "onFileProcessing", "onReportGeneration", "resetFlow", "processState", "handleFileUpload", "event", "files", "target", "file", "Array", "from", "newFile", "id", "Math", "random", "toString", "substr", "filename", "name", "file_type", "type", "size", "status", "upload_time", "Date", "toISOString", "file_path", "content", "readFileContent", "preview_content", "substring", "error", "console", "prev", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "e", "_e$target", "result", "onerror", "readAsText", "validateFile", "fileId", "map", "processFile", "setTimeout", "deleteFile", "filter", "previewFile", "processAllFiles", "validatedFiles", "for<PERSON>ach", "index", "downloadDirectory", "process", "env", "REACT_APP_DOWNLOAD_DIR", "downloadAnalysisReport", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "log", "getFileProgress", "fileProgresses", "find", "fp", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "multiple", "accept", "onChange", "onClick", "_fileInputRef$current", "current", "style", "height", "length", "f", "disabled", "exit", "layout", "toFixed", "toLocaleString", "title", "fileProgress", "stepProgress", "currentStep", "width", "transition", "duration", "isActive", "onComplete", "speed", "scale", "stopPropagation", "_c", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/components/FileUploadSimple.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\nimport {\n  Upload,\n  FileText,\n  CheckCircle,\n  AlertCircle,\n  Settings,\n  Play,\n  Download,\n  Trash2,\n  Terminal,\n  Eye,\n  X\n} from 'lucide-react';\nimport { StreamOutput } from './StreamOutput';\nimport { configureStageOutput } from '../data/streamOutputs/configureStage';\nimport { analyzeStageOutput } from '../data/streamOutputs/analyzeStage';\nimport { generateStageOutput } from '../data/streamOutputs/generateStage';\n\ninterface UploadedFile {\n  id: string;\n  filename: string;\n  file_type: string;\n  size: number;\n  status: 'uploaded' | 'validated' | 'processing' | 'processed' | 'error';\n  upload_time: string;\n  file_path: string;\n  error_message?: string;\n  preview_content?: string;\n}\n\nconst FileUploadSimple: React.FC = () => {\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [currentStreamStage, setCurrentStreamStage] = useState<'configure' | 'analyze' | 'generate' | null>(null);\n  const [selectedFile, setSelectedFile] = useState<UploadedFile | null>(null);\n  const [showPreview, setShowPreview] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const { onFileUploaded, onFileValidation, onFileProcessing, onReportGeneration, resetFlow, processState } = useProcessFlowContext();\n\n  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files;\n    if (files) {\n      for (const file of Array.from(files)) {\n        const newFile: UploadedFile = {\n          id: Math.random().toString(36).substr(2, 9),\n          filename: file.name,\n          file_type: file.type || 'text/plain',\n          size: file.size,\n          status: 'uploaded',\n          upload_time: new Date().toISOString(),\n          file_path: `/uploads/${file.name}`\n        };\n\n        // 读取文件内容用于预览\n        try {\n          const content = await readFileContent(file);\n          newFile.preview_content = content.substring(0, 1000); // 只保存前1000字符用于预览\n        } catch (error) {\n          console.error('读取文件内容失败:', error);\n        }\n\n        setUploadedFiles(prev => [...prev, newFile]);\n        onFileUploaded(newFile.id, newFile.filename);\n      }\n    }\n  };\n\n  // 读取文件内容的辅助函数\n  const readFileContent = (file: File): Promise<string> => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        resolve(e.target?.result as string);\n      };\n      reader.onerror = reject;\n      reader.readAsText(file, 'UTF-8');\n    });\n  };\n\n  const validateFile = (fileId: string) => {\n    setUploadedFiles(prev => prev.map(file => \n      file.id === fileId ? { ...file, status: 'validated' } : file\n    ));\n    onFileValidation(fileId);\n  };\n\n  const processFile = (fileId: string) => {\n    setUploadedFiles(prev => prev.map(file => \n      file.id === fileId ? { ...file, status: 'processing' } : file\n    ));\n    onFileProcessing(fileId);\n    \n    // 模拟处理完成\n    setTimeout(() => {\n      setUploadedFiles(prev => prev.map(file => \n        file.id === fileId ? { ...file, status: 'processed' } : file\n      ));\n    }, 5000);\n  };\n\n  const deleteFile = (fileId: string) => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  };\n\n  // 预览文件\n  const previewFile = (file: UploadedFile) => {\n    setSelectedFile(file);\n    setShowPreview(true);\n  };\n\n  const processAllFiles = () => {\n    setIsProcessing(true);\n    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');\n    \n    // 开始流式输出 - 解析配置阶段\n    setCurrentStreamStage('configure');\n    \n    // 模拟处理流程\n    setTimeout(() => {\n      // 解析配置完成，开始智能分析\n      setCurrentStreamStage('analyze');\n      \n      // 同时开始处理文件\n      validatedFiles.forEach((file, index) => {\n        setTimeout(() => {\n          processFile(file.id);\n        }, index * 1000);\n      });\n      \n      setTimeout(() => {\n        // 智能分析完成，直接开始报表生成（不需要用户点击）\n        setCurrentStreamStage('generate');\n        onReportGeneration(); // 自动触发报表生成\n        \n        setTimeout(() => {\n          // 所有处理完成\n          setIsProcessing(false);\n          setCurrentStreamStage(null);\n        }, 8000); // 报表生成阶段时间\n      }, 6000); // 智能分析阶段时间\n    }, 4000); // 解析配置阶段时间\n  };\n\n  // 配置下载目录（可配置）\n  const downloadDirectory = process.env.REACT_APP_DOWNLOAD_DIR || '/downloads';\n\n  const downloadAnalysisReport = () => {\n    // 下载预设的分析结果报表\n    const link = document.createElement('a');\n    link.href = `${downloadDirectory}/文本分析结果报表.pdf`; // 从配置目录下载\n    link.download = '文本分析结果报表.pdf';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    // 下载完成后重置\n    setTimeout(() => {\n      setUploadedFiles([]);\n      resetFlow();\n    }, 1000);\n\n    console.log('下载文本分析结果报表完成，系统已重置');\n  };\n\n  // 获取文件进度\n  const getFileProgress = (fileId: string) => {\n    return processState.fileProgresses.find(fp => fp.fileId === fileId);\n  };\n\n  return (\n    <div className=\"space-y-6 h-full flex flex-col\">\n      {/* 顶部控制区域 */}\n      <motion.div\n        className=\"tech-card p-4\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        <div className=\"flex items-center justify-between\">\n          <h2 className=\"text-xl font-bold text-white\">数据文件上传</h2>\n          \n          {/* 文件上传控制 */}\n          <div className=\"flex items-center space-x-4\">\n            {/* 文件选择器 - 扩展长度 */}\n            <div className=\"flex-1 max-w-md\">\n              <input\n                ref={fileInputRef}\n                type=\"file\"\n                multiple\n                accept=\".txt,.doc,.docx,.pdf,.xlsx,.xls,.csv\"\n                onChange={handleFileUpload}\n                className=\"hidden\"\n              />\n              <button\n                onClick={() => fileInputRef.current?.click()}\n                className=\"tech-button w-full px-6 py-3 text-base font-medium\"\n              >\n                <Upload className=\"w-5 h-5 mr-3\" />\n                选择文本文件\n              </button>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* 主要内容区域 */}\n      <div className=\"flex-1 space-y-6\">\n        {/* 已上传文件列表 - 横向扩充 */}\n        <motion.div\n          className=\"tech-card flex flex-col\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          style={{ height: '400px' }}\n        >\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-700\">\n            <h3 className=\"font-semibold text-white\">已上传文件 ({uploadedFiles.length})</h3>\n            {uploadedFiles.filter(f => f.status === 'validated').length > 0 && (\n              <button\n                onClick={processAllFiles}\n                disabled={isProcessing}\n                className=\"tech-button text-sm px-4 py-2 disabled:opacity-50\"\n              >\n                <Play className=\"w-4 h-4 mr-2\" />\n                {isProcessing ? '处理中...' : '一键处理全部'}\n              </button>\n            )}\n          </div>\n          \n          <div className=\"flex-1 overflow-y-auto p-4\">\n            <AnimatePresence>\n              {uploadedFiles.map((file) => (\n                <motion.div\n                  key={file.id}\n                  className=\"bg-gray-800/50 rounded-lg p-4 mb-3 border border-gray-700\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: -20 }}\n                  layout\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-3\">\n                        <FileText className=\"w-5 h-5 text-tech-cyan\" />\n                        <div>\n                          <h4 className=\"font-medium text-white\">{file.filename}</h4>\n                          <p className=\"text-sm text-gray-400\">\n                            {(file.size / 1024).toFixed(1)} KB • {new Date(file.upload_time).toLocaleString()}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-2\">\n                      {/* 状态指示器 */}\n                      <div className={`px-2 py-1 rounded text-xs ${\n                        file.status === 'uploaded' ? 'bg-blue-900/50 text-blue-300' :\n                        file.status === 'validated' ? 'bg-green-900/50 text-green-300' :\n                        file.status === 'processing' ? 'bg-yellow-900/50 text-yellow-300' :\n                        file.status === 'processed' ? 'bg-purple-900/50 text-purple-300' :\n                        'bg-red-900/50 text-red-300'\n                      }`}>\n                        {file.status === 'uploaded' ? '已上传' :\n                         file.status === 'validated' ? '已验证' :\n                         file.status === 'processing' ? '处理中' :\n                         file.status === 'processed' ? '已完成' : '错误'}\n                      </div>\n                      \n                      {/* 操作按钮 */}\n                      <button\n                        onClick={() => previewFile(file)}\n                        className=\"p-2 text-tech-purple hover:bg-tech-purple/10 rounded-lg transition-colors\"\n                        title=\"在线预览\"\n                      >\n                        <Eye className=\"w-4 h-4\" />\n                      </button>\n\n                      {file.status === 'uploaded' && (\n                        <button\n                          onClick={() => validateFile(file.id)}\n                          className=\"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\"\n                          title=\"验证文件\"\n                        >\n                          <CheckCircle className=\"w-4 h-4\" />\n                        </button>\n                      )}\n\n                      {file.status === 'validated' && (\n                        <button\n                          onClick={() => processFile(file.id)}\n                          className=\"p-2 text-tech-cyan hover:bg-tech-cyan/10 rounded-lg transition-colors\"\n                          title=\"智能处理\"\n                        >\n                          <Settings className=\"w-4 h-4\" />\n                        </button>\n                      )}\n\n                      <button\n                        onClick={() => deleteFile(file.id)}\n                        className=\"p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors\"\n                        title=\"删除文件\"\n                      >\n                        <Trash2 className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </div>\n                  \n                  {/* 文件级进度显示 */}\n                  {(() => {\n                    const fileProgress = getFileProgress(file.id);\n                    if (fileProgress && fileProgress.stepProgress > 0) {\n                      return (\n                        <div className=\"mt-2\">\n                          <div className=\"flex items-center justify-between text-xs text-gray-400 mb-1\">\n                            <span>{fileProgress.currentStep === 'configure' ? '配置中' : \n                                   fileProgress.currentStep === 'analyze' ? '分析中' : \n                                   fileProgress.currentStep === 'generate' ? '生成中' : '处理中'}...</span>\n                            <span>{fileProgress.stepProgress}%</span>\n                          </div>\n                          <div className=\"w-full bg-gray-700 rounded-full h-1.5\">\n                            <motion.div\n                              className=\"progress-bar h-1.5 rounded-full\"\n                              initial={{ width: 0 }}\n                              animate={{ width: `${fileProgress.stepProgress}%` }}\n                              transition={{ duration: 0.3 }}\n                            />\n                          </div>\n                        </div>\n                      );\n                    }\n                    return null;\n                  })()}\n                </motion.div>\n              ))}\n            </AnimatePresence>\n\n            {uploadedFiles.length === 0 && (\n              <motion.div\n                className=\"text-center py-12 text-gray-400\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n              >\n                <FileText className=\"w-12 h-12 mx-auto mb-4 opacity-50\" />\n                <p>暂无上传文件</p>\n                <p className=\"text-sm mt-1\">请先上传数据文件</p>\n              </motion.div>\n            )}\n          </div>\n        </motion.div>\n\n        {/* AI处理输出区域 - 移至原生成智能分析报表位置 */}\n        <motion.div\n          className=\"tech-card flex flex-col\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          style={{ height: '400px' }}\n        >\n          {currentStreamStage ? (\n            <>\n              {currentStreamStage === 'configure' && (\n                <StreamOutput\n                  content={configureStageOutput}\n                  isActive={true}\n                  title=\"解析配置阶段\"\n                  onComplete={() => {}}\n                  speed={100}\n                />\n              )}\n              {currentStreamStage === 'analyze' && (\n                <StreamOutput\n                  content={analyzeStageOutput}\n                  isActive={true}\n                  title=\"智能分析阶段\"\n                  onComplete={() => {}}\n                  speed={120}\n                />\n              )}\n              {currentStreamStage === 'generate' && (\n                <StreamOutput\n                  content={generateStageOutput}\n                  isActive={true}\n                  title=\"报表生成阶段\"\n                  onComplete={() => {}}\n                  speed={80}\n                />\n              )}\n            </>\n          ) : (\n            <div className=\"flex-1 flex items-center justify-center text-gray-400\">\n              <div className=\"text-center\">\n                <Terminal className=\"w-12 h-12 mx-auto mb-2 opacity-50\" />\n                <p>AI处理输出</p>\n                <p className=\"text-sm\">点击\"一键处理全部\"开始智能分析</p>\n              </div>\n            </div>\n          )}\n        </motion.div>\n      </div>\n\n      {/* 文件预览模态框 */}\n      <AnimatePresence>\n        {showPreview && selectedFile && (\n          <motion.div\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            onClick={() => setShowPreview(false)}\n          >\n            <motion.div\n              className=\"tech-card max-w-4xl w-full max-h-[80vh] flex flex-col\"\n              initial={{ scale: 0.9, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              exit={{ scale: 0.9, opacity: 0 }}\n              onClick={(e) => e.stopPropagation()}\n            >\n              {/* 预览头部 */}\n              <div className=\"flex items-center justify-between p-4 border-b border-gray-700\">\n                <div>\n                  <h3 className=\"text-lg font-semibold text-white\">{selectedFile.filename}</h3>\n                  <p className=\"text-sm text-gray-400\">\n                    {(selectedFile.size / 1024).toFixed(1)} KB • {new Date(selectedFile.upload_time).toLocaleString()}\n                  </p>\n                </div>\n                <button\n                  onClick={() => setShowPreview(false)}\n                  className=\"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors\"\n                >\n                  <X className=\"w-5 h-5\" />\n                </button>\n              </div>\n\n              {/* 预览内容 */}\n              <div className=\"flex-1 overflow-auto p-4\">\n                <div className=\"bg-gray-900/50 rounded-lg p-4 font-mono text-sm text-gray-300 whitespace-pre-wrap\">\n                  {selectedFile.preview_content || '无法预览此文件内容'}\n                </div>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n};\n\nexport default FileUploadSimple;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SACEC,MAAM,EACNC,QAAQ,EACRC,WAAW,EAEXC,QAAQ,EACRC,IAAI,EAEJC,MAAM,EACNC,QAAQ,EACRC,GAAG,EACHC,CAAC,QACI,cAAc;AACrB,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,oBAAoB,QAAQ,sCAAsC;AAC3E,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,mBAAmB,QAAQ,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAc1E,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7B,QAAQ,CAA8C,IAAI,CAAC;EAC/G,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMkC,YAAY,GAAGjC,MAAM,CAAmB,IAAI,CAAC;EAEnD,MAAM;IAAEkC,cAAc;IAAEC,gBAAgB;IAAEC,gBAAgB;IAAEC,kBAAkB;IAAEC,SAAS;IAAEC;EAAa,CAAC,GAAGpC,qBAAqB,CAAC,CAAC;EAEnI,MAAMqC,gBAAgB,GAAG,MAAOC,KAA0C,IAAK;IAC7E,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,EAAE;MACT,KAAK,MAAME,IAAI,IAAIC,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC,EAAE;QACpC,MAAMK,OAAqB,GAAG;UAC5BC,EAAE,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;UAC3CC,QAAQ,EAAET,IAAI,CAACU,IAAI;UACnBC,SAAS,EAAEX,IAAI,CAACY,IAAI,IAAI,YAAY;UACpCC,IAAI,EAAEb,IAAI,CAACa,IAAI;UACfC,MAAM,EAAE,UAAU;UAClBC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACrCC,SAAS,EAAE,YAAYlB,IAAI,CAACU,IAAI;QAClC,CAAC;;QAED;QACA,IAAI;UACF,MAAMS,OAAO,GAAG,MAAMC,eAAe,CAACpB,IAAI,CAAC;UAC3CG,OAAO,CAACkB,eAAe,GAAGF,OAAO,CAACG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACnC;QAEA3C,gBAAgB,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEtB,OAAO,CAAC,CAAC;QAC5Cb,cAAc,CAACa,OAAO,CAACC,EAAE,EAAED,OAAO,CAACM,QAAQ,CAAC;MAC9C;IACF;EACF,CAAC;;EAED;EACA,MAAMW,eAAe,GAAIpB,IAAU,IAAsB;IACvD,OAAO,IAAI0B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;QAAA,IAAAC,SAAA;QACrBN,OAAO,EAAAM,SAAA,GAACD,CAAC,CAACjC,MAAM,cAAAkC,SAAA,uBAARA,SAAA,CAAUC,MAAgB,CAAC;MACrC,CAAC;MACDL,MAAM,CAACM,OAAO,GAAGP,MAAM;MACvBC,MAAM,CAACO,UAAU,CAACpC,IAAI,EAAE,OAAO,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMqC,YAAY,GAAIC,MAAc,IAAK;IACvC1D,gBAAgB,CAAC6C,IAAI,IAAIA,IAAI,CAACc,GAAG,CAACvC,IAAI,IACpCA,IAAI,CAACI,EAAE,KAAKkC,MAAM,GAAG;MAAE,GAAGtC,IAAI;MAAEc,MAAM,EAAE;IAAY,CAAC,GAAGd,IAC1D,CAAC,CAAC;IACFT,gBAAgB,CAAC+C,MAAM,CAAC;EAC1B,CAAC;EAED,MAAME,WAAW,GAAIF,MAAc,IAAK;IACtC1D,gBAAgB,CAAC6C,IAAI,IAAIA,IAAI,CAACc,GAAG,CAACvC,IAAI,IACpCA,IAAI,CAACI,EAAE,KAAKkC,MAAM,GAAG;MAAE,GAAGtC,IAAI;MAAEc,MAAM,EAAE;IAAa,CAAC,GAAGd,IAC3D,CAAC,CAAC;IACFR,gBAAgB,CAAC8C,MAAM,CAAC;;IAExB;IACAG,UAAU,CAAC,MAAM;MACf7D,gBAAgB,CAAC6C,IAAI,IAAIA,IAAI,CAACc,GAAG,CAACvC,IAAI,IACpCA,IAAI,CAACI,EAAE,KAAKkC,MAAM,GAAG;QAAE,GAAGtC,IAAI;QAAEc,MAAM,EAAE;MAAY,CAAC,GAAGd,IAC1D,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAM0C,UAAU,GAAIJ,MAAc,IAAK;IACrC1D,gBAAgB,CAAC6C,IAAI,IAAIA,IAAI,CAACkB,MAAM,CAAC3C,IAAI,IAAIA,IAAI,CAACI,EAAE,KAAKkC,MAAM,CAAC,CAAC;EACnE,CAAC;;EAED;EACA,MAAMM,WAAW,GAAI5C,IAAkB,IAAK;IAC1Cd,eAAe,CAACc,IAAI,CAAC;IACrBZ,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMyD,eAAe,GAAGA,CAAA,KAAM;IAC5B/D,eAAe,CAAC,IAAI,CAAC;IACrB,MAAMgE,cAAc,GAAGnE,aAAa,CAACgE,MAAM,CAAC3C,IAAI,IAAIA,IAAI,CAACc,MAAM,KAAK,WAAW,CAAC;;IAEhF;IACA9B,qBAAqB,CAAC,WAAW,CAAC;;IAElC;IACAyD,UAAU,CAAC,MAAM;MACf;MACAzD,qBAAqB,CAAC,SAAS,CAAC;;MAEhC;MACA8D,cAAc,CAACC,OAAO,CAAC,CAAC/C,IAAI,EAAEgD,KAAK,KAAK;QACtCP,UAAU,CAAC,MAAM;UACfD,WAAW,CAACxC,IAAI,CAACI,EAAE,CAAC;QACtB,CAAC,EAAE4C,KAAK,GAAG,IAAI,CAAC;MAClB,CAAC,CAAC;MAEFP,UAAU,CAAC,MAAM;QACf;QACAzD,qBAAqB,CAAC,UAAU,CAAC;QACjCS,kBAAkB,CAAC,CAAC,CAAC,CAAC;;QAEtBgD,UAAU,CAAC,MAAM;UACf;UACA3D,eAAe,CAAC,KAAK,CAAC;UACtBE,qBAAqB,CAAC,IAAI,CAAC;QAC7B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC;;EAED;EACA,MAAMiE,iBAAiB,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,YAAY;EAE5E,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACA,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,GAAGR,iBAAiB,eAAe,CAAC,CAAC;IACjDK,IAAI,CAACI,QAAQ,GAAG,cAAc;IAC9BH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;;IAE/B;IACAb,UAAU,CAAC,MAAM;MACf7D,gBAAgB,CAAC,EAAE,CAAC;MACpBc,SAAS,CAAC,CAAC;IACb,CAAC,EAAE,IAAI,CAAC;IAER8B,OAAO,CAACuC,GAAG,CAAC,oBAAoB,CAAC;EACnC,CAAC;;EAED;EACA,MAAMC,eAAe,GAAI1B,MAAc,IAAK;IAC1C,OAAO3C,YAAY,CAACsE,cAAc,CAACC,IAAI,CAACC,EAAE,IAAIA,EAAE,CAAC7B,MAAM,KAAKA,MAAM,CAAC;EACrE,CAAC;EAED,oBACEhE,OAAA;IAAK8F,SAAS,EAAC,gCAAgC;IAAAC,QAAA,gBAE7C/F,OAAA,CAACjB,MAAM,CAACiH,GAAG;MACTF,SAAS,EAAC,eAAe;MACzBG,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAE9B/F,OAAA;QAAK8F,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD/F,OAAA;UAAI8F,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAC;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGxDxG,OAAA;UAAK8F,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAE1C/F,OAAA;YAAK8F,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B/F,OAAA;cACEyG,GAAG,EAAE1F,YAAa;cAClBuB,IAAI,EAAC,MAAM;cACXoE,QAAQ;cACRC,MAAM,EAAC,sCAAsC;cAC7CC,QAAQ,EAAEtF,gBAAiB;cAC3BwE,SAAS,EAAC;YAAQ;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACFxG,OAAA;cACE6G,OAAO,EAAEA,CAAA;gBAAA,IAAAC,qBAAA;gBAAA,QAAAA,qBAAA,GAAM/F,YAAY,CAACgG,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBvB,KAAK,CAAC,CAAC;cAAA,CAAC;cAC7CO,SAAS,EAAC,oDAAoD;cAAAC,QAAA,gBAE9D/F,OAAA,CAACd,MAAM;gBAAC4G,SAAS,EAAC;cAAc;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wCAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbxG,OAAA;MAAK8F,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/B/F,OAAA,CAACjB,MAAM,CAACiH,GAAG;QACTF,SAAS,EAAC,yBAAyB;QACnCG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9Ba,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAlB,QAAA,gBAE3B/F,OAAA;UAAK8F,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAC7E/F,OAAA;YAAI8F,SAAS,EAAC,0BAA0B;YAAAC,QAAA,GAAC,kCAAO,EAAC1F,aAAa,CAAC6G,MAAM,EAAC,GAAC;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC3EnG,aAAa,CAACgE,MAAM,CAAC8C,CAAC,IAAIA,CAAC,CAAC3E,MAAM,KAAK,WAAW,CAAC,CAAC0E,MAAM,GAAG,CAAC,iBAC7DlH,OAAA;YACE6G,OAAO,EAAEtC,eAAgB;YACzB6C,QAAQ,EAAE7G,YAAa;YACvBuF,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAE7D/F,OAAA,CAACV,IAAI;cAACwG,SAAS,EAAC;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAChCjG,YAAY,GAAG,QAAQ,GAAG,QAAQ;UAAA;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENxG,OAAA;UAAK8F,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC/F,OAAA,CAAChB,eAAe;YAAA+G,QAAA,EACb1F,aAAa,CAAC4D,GAAG,CAAEvC,IAAI,iBACtB1B,OAAA,CAACjB,MAAM,CAACiH,GAAG;cAETF,SAAS,EAAC,2DAA2D;cACrEG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BkB,IAAI,EAAE;gBAAEnB,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAC7BmB,MAAM;cAAAvB,QAAA,gBAEN/F,OAAA;gBAAK8F,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD/F,OAAA;kBAAK8F,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eACrB/F,OAAA;oBAAK8F,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C/F,OAAA,CAACb,QAAQ;sBAAC2G,SAAS,EAAC;oBAAwB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/CxG,OAAA;sBAAA+F,QAAA,gBACE/F,OAAA;wBAAI8F,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,EAAErE,IAAI,CAACS;sBAAQ;wBAAAkE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3DxG,OAAA;wBAAG8F,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GACjC,CAACrE,IAAI,CAACa,IAAI,GAAG,IAAI,EAAEgF,OAAO,CAAC,CAAC,CAAC,EAAC,aAAM,EAAC,IAAI7E,IAAI,CAAChB,IAAI,CAACe,WAAW,CAAC,CAAC+E,cAAc,CAAC,CAAC;sBAAA;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENxG,OAAA;kBAAK8F,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAE1C/F,OAAA;oBAAK8F,SAAS,EAAE,6BACdpE,IAAI,CAACc,MAAM,KAAK,UAAU,GAAG,8BAA8B,GAC3Dd,IAAI,CAACc,MAAM,KAAK,WAAW,GAAG,gCAAgC,GAC9Dd,IAAI,CAACc,MAAM,KAAK,YAAY,GAAG,kCAAkC,GACjEd,IAAI,CAACc,MAAM,KAAK,WAAW,GAAG,kCAAkC,GAChE,4BAA4B,EAC3B;oBAAAuD,QAAA,EACArE,IAAI,CAACc,MAAM,KAAK,UAAU,GAAG,KAAK,GAClCd,IAAI,CAACc,MAAM,KAAK,WAAW,GAAG,KAAK,GACnCd,IAAI,CAACc,MAAM,KAAK,YAAY,GAAG,KAAK,GACpCd,IAAI,CAACc,MAAM,KAAK,WAAW,GAAG,KAAK,GAAG;kBAAI;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eAGNxG,OAAA;oBACE6G,OAAO,EAAEA,CAAA,KAAMvC,WAAW,CAAC5C,IAAI,CAAE;oBACjCoE,SAAS,EAAC,2EAA2E;oBACrF2B,KAAK,EAAC,0BAAM;oBAAA1B,QAAA,eAEZ/F,OAAA,CAACP,GAAG;sBAACqG,SAAS,EAAC;oBAAS;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,EAER9E,IAAI,CAACc,MAAM,KAAK,UAAU,iBACzBxC,OAAA;oBACE6G,OAAO,EAAEA,CAAA,KAAM9C,YAAY,CAACrC,IAAI,CAACI,EAAE,CAAE;oBACrCgE,SAAS,EAAC,yEAAyE;oBACnF2B,KAAK,EAAC,0BAAM;oBAAA1B,QAAA,eAEZ/F,OAAA,CAACZ,WAAW;sBAAC0G,SAAS,EAAC;oBAAS;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CACT,EAEA9E,IAAI,CAACc,MAAM,KAAK,WAAW,iBAC1BxC,OAAA;oBACE6G,OAAO,EAAEA,CAAA,KAAM3C,WAAW,CAACxC,IAAI,CAACI,EAAE,CAAE;oBACpCgE,SAAS,EAAC,uEAAuE;oBACjF2B,KAAK,EAAC,0BAAM;oBAAA1B,QAAA,eAEZ/F,OAAA,CAACX,QAAQ;sBAACyG,SAAS,EAAC;oBAAS;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CACT,eAEDxG,OAAA;oBACE6G,OAAO,EAAEA,CAAA,KAAMzC,UAAU,CAAC1C,IAAI,CAACI,EAAE,CAAE;oBACnCgE,SAAS,EAAC,mEAAmE;oBAC7E2B,KAAK,EAAC,0BAAM;oBAAA1B,QAAA,eAEZ/F,OAAA,CAACT,MAAM;sBAACuG,SAAS,EAAC;oBAAS;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL,CAAC,MAAM;gBACN,MAAMkB,YAAY,GAAGhC,eAAe,CAAChE,IAAI,CAACI,EAAE,CAAC;gBAC7C,IAAI4F,YAAY,IAAIA,YAAY,CAACC,YAAY,GAAG,CAAC,EAAE;kBACjD,oBACE3H,OAAA;oBAAK8F,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB/F,OAAA;sBAAK8F,SAAS,EAAC,8DAA8D;sBAAAC,QAAA,gBAC3E/F,OAAA;wBAAA+F,QAAA,GAAO2B,YAAY,CAACE,WAAW,KAAK,WAAW,GAAG,KAAK,GAChDF,YAAY,CAACE,WAAW,KAAK,SAAS,GAAG,KAAK,GAC9CF,YAAY,CAACE,WAAW,KAAK,UAAU,GAAG,KAAK,GAAG,KAAK,EAAC,KAAG;sBAAA;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzExG,OAAA;wBAAA+F,QAAA,GAAO2B,YAAY,CAACC,YAAY,EAAC,GAAC;sBAAA;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC,eACNxG,OAAA;sBAAK8F,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,eACpD/F,OAAA,CAACjB,MAAM,CAACiH,GAAG;wBACTF,SAAS,EAAC,iCAAiC;wBAC3CG,OAAO,EAAE;0BAAE4B,KAAK,EAAE;wBAAE,CAAE;wBACtBzB,OAAO,EAAE;0BAAEyB,KAAK,EAAE,GAAGH,YAAY,CAACC,YAAY;wBAAI,CAAE;wBACpDG,UAAU,EAAE;0BAAEC,QAAQ,EAAE;wBAAI;sBAAE;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAEV;gBACA,OAAO,IAAI;cACb,CAAC,EAAE,CAAC;YAAA,GAlGC9E,IAAI,CAACI,EAAE;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmGF,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACa,CAAC,EAEjBnG,aAAa,CAAC6G,MAAM,KAAK,CAAC,iBACzBlH,OAAA,CAACjB,MAAM,CAACiH,GAAG;YACTF,SAAS,EAAC,iCAAiC;YAC3CG,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YAAAH,QAAA,gBAExB/F,OAAA,CAACb,QAAQ;cAAC2G,SAAS,EAAC;YAAmC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DxG,OAAA;cAAA+F,QAAA,EAAG;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACbxG,OAAA;cAAG8F,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbxG,OAAA,CAACjB,MAAM,CAACiH,GAAG;QACTF,SAAS,EAAC,yBAAyB;QACnCG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9Ba,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAlB,QAAA,EAE1BtF,kBAAkB,gBACjBT,OAAA,CAAAE,SAAA;UAAA6F,QAAA,GACGtF,kBAAkB,KAAK,WAAW,iBACjCT,OAAA,CAACL,YAAY;YACXkD,OAAO,EAAEjD,oBAAqB;YAC9BoI,QAAQ,EAAE,IAAK;YACfP,KAAK,EAAC,sCAAQ;YACdQ,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;YACrBC,KAAK,EAAE;UAAI;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACF,EACA/F,kBAAkB,KAAK,SAAS,iBAC/BT,OAAA,CAACL,YAAY;YACXkD,OAAO,EAAEhD,kBAAmB;YAC5BmI,QAAQ,EAAE,IAAK;YACfP,KAAK,EAAC,sCAAQ;YACdQ,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;YACrBC,KAAK,EAAE;UAAI;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACF,EACA/F,kBAAkB,KAAK,UAAU,iBAChCT,OAAA,CAACL,YAAY;YACXkD,OAAO,EAAE/C,mBAAoB;YAC7BkI,QAAQ,EAAE,IAAK;YACfP,KAAK,EAAC,sCAAQ;YACdQ,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;YACrBC,KAAK,EAAE;UAAG;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CACF;QAAA,eACD,CAAC,gBAEHxG,OAAA;UAAK8F,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eACpE/F,OAAA;YAAK8F,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/F,OAAA,CAACR,QAAQ;cAACsG,SAAS,EAAC;YAAmC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DxG,OAAA;cAAA+F,QAAA,EAAG;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACbxG,OAAA;cAAG8F,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAgB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNxG,OAAA,CAAChB,eAAe;MAAA+G,QAAA,EACblF,WAAW,IAAIF,YAAY,iBAC1BX,OAAA,CAACjB,MAAM,CAACiH,GAAG;QACTF,SAAS,EAAC,sFAAsF;QAChGG,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBE,OAAO,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACxBmB,IAAI,EAAE;UAAEnB,OAAO,EAAE;QAAE,CAAE;QACrBW,OAAO,EAAEA,CAAA,KAAM/F,cAAc,CAAC,KAAK,CAAE;QAAAiF,QAAA,eAErC/F,OAAA,CAACjB,MAAM,CAACiH,GAAG;UACTF,SAAS,EAAC,uDAAuD;UACjEG,OAAO,EAAE;YAAEkC,KAAK,EAAE,GAAG;YAAEjC,OAAO,EAAE;UAAE,CAAE;UACpCE,OAAO,EAAE;YAAE+B,KAAK,EAAE,CAAC;YAAEjC,OAAO,EAAE;UAAE,CAAE;UAClCmB,IAAI,EAAE;YAAEc,KAAK,EAAE,GAAG;YAAEjC,OAAO,EAAE;UAAE,CAAE;UACjCW,OAAO,EAAGnD,CAAC,IAAKA,CAAC,CAAC0E,eAAe,CAAC,CAAE;UAAArC,QAAA,gBAGpC/F,OAAA;YAAK8F,SAAS,EAAC,gEAAgE;YAAAC,QAAA,gBAC7E/F,OAAA;cAAA+F,QAAA,gBACE/F,OAAA;gBAAI8F,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAEpF,YAAY,CAACwB;cAAQ;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7ExG,OAAA;gBAAG8F,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACjC,CAACpF,YAAY,CAAC4B,IAAI,GAAG,IAAI,EAAEgF,OAAO,CAAC,CAAC,CAAC,EAAC,aAAM,EAAC,IAAI7E,IAAI,CAAC/B,YAAY,CAAC8B,WAAW,CAAC,CAAC+E,cAAc,CAAC,CAAC;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNxG,OAAA;cACE6G,OAAO,EAAEA,CAAA,KAAM/F,cAAc,CAAC,KAAK,CAAE;cACrCgF,SAAS,EAAC,mFAAmF;cAAAC,QAAA,eAE7F/F,OAAA,CAACN,CAAC;gBAACoG,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNxG,OAAA;YAAK8F,SAAS,EAAC,0BAA0B;YAAAC,QAAA,eACvC/F,OAAA;cAAK8F,SAAS,EAAC,mFAAmF;cAAAC,QAAA,EAC/FpF,YAAY,CAACoC,eAAe,IAAI;YAAW;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEV,CAAC;AAACpG,EAAA,CA7ZID,gBAA0B;EAAA,QAQ8ElB,qBAAqB;AAAA;AAAAoJ,EAAA,GAR7HlI,gBAA0B;AA+ZhC,eAAeA,gBAAgB;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}