{"ast": null, "code": "/**\n * lucide-react v0.292.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BookOpenText = createLucideIcon(\"BookOpenText\", [[\"path\", {\n  d: \"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z\",\n  key: \"vv98re\"\n}], [\"path\", {\n  d: \"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z\",\n  key: \"1cyq3y\"\n}], [\"path\", {\n  d: \"M6 8h2\",\n  key: \"30oboj\"\n}], [\"path\", {\n  d: \"M6 12h2\",\n  key: \"32wvfc\"\n}], [\"path\", {\n  d: \"M16 8h2\",\n  key: \"msurwy\"\n}], [\"path\", {\n  d: \"M16 12h2\",\n  key: \"7q9ll5\"\n}]]);\nexport { BookOpenText as default };", "map": {"version": 3, "names": ["BookOpenText", "createLucideIcon", "d", "key"], "sources": ["D:\\pyworkspace\\analysisFlow\\frontend\\node_modules\\lucide-react\\src\\icons\\book-open-text.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BookOpenText\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAzaDZhNCA0IDAgMCAxIDQgNHYxNGEzIDMgMCAwIDAtMy0zSDJ6IiAvPgogIDxwYXRoIGQ9Ik0yMiAzaC02YTQgNCAwIDAgMC00IDR2MTRhMyAzIDAgMCAxIDMtM2g3eiIgLz4KICA8cGF0aCBkPSJNNiA4aDIiIC8+CiAgPHBhdGggZD0iTTYgMTJoMiIgLz4KICA8cGF0aCBkPSJNMTYgOGgyIiAvPgogIDxwYXRoIGQ9Ik0xNiAxMmgyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/book-open-text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookOpenText = createLucideIcon('BookOpenText', [\n  ['path', { d: 'M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z', key: 'vv98re' }],\n  ['path', { d: 'M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z', key: '1cyq3y' }],\n  ['path', { d: 'M6 8h2', key: '30oboj' }],\n  ['path', { d: 'M6 12h2', key: '32wvfc' }],\n  ['path', { d: 'M16 8h2', key: 'msurwy' }],\n  ['path', { d: 'M16 12h2', key: '7q9ll5' }],\n]);\n\nexport default BookOpenText;\n"], "mappings": ";;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,MAAQ;EAAEC,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,4CAA8C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3E,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}