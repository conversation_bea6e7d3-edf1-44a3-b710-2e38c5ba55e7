{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport ProcessFlow from './components/ProcessFlow';\nimport FileUploadSimple from './components/FileUploadSimple';\nimport Header from './components/Header';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-tech-dark\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-tech-grid bg-tech-grid opacity-20 pointer-events-none\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 min-h-[calc(100vh-120px)]\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"lg:col-span-1 flex flex-col\",\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            children: /*#__PURE__*/_jsxDEV(ProcessFlow, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"lg:col-span-2 flex flex-col\",\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.2\n            },\n            children: /*#__PURE__*/_jsxDEV(FileUploadSimple, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "motion", "ProcessFlow", "FileUploadSimple", "Header", "jsxDEV", "_jsxDEV", "App", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "x", "animate", "transition", "duration", "delay", "_c", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport ProcessFlow from './components/ProcessFlow';\nimport FileUploadSimple from './components/FileUploadSimple';\nimport Header from './components/Header';\n\nfunction App() {\n  return (\n    <div className=\"min-h-screen bg-tech-dark\">\n      {/* 背景网格效果 */}\n      <div className=\"fixed inset-0 bg-tech-grid bg-tech-grid opacity-20 pointer-events-none\" />\n      \n      {/* 主要内容 */}\n      <div className=\"relative z-10\">\n        <Header />\n        \n        <main className=\"container mx-auto px-4 py-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 min-h-[calc(100vh-120px)]\">\n            {/* 左侧流程展示区域 */}\n            <motion.div\n              className=\"lg:col-span-1 flex flex-col\"\n              initial={{ opacity: 0, x: -50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n            >\n              <ProcessFlow />\n            </motion.div>\n\n            {/* 右侧文件上传和操作区域 */}\n            <motion.div\n              className=\"lg:col-span-2 flex flex-col\"\n              initial={{ opacity: 0, x: 50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n            >\n              <FileUploadSimple />\n            </motion.div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,MAAM,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBAExCH,OAAA;MAAKE,SAAS,EAAC;IAAwE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG1FP,OAAA;MAAKE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BH,OAAA,CAACF,MAAM;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEVP,OAAA;QAAME,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC3CH,OAAA;UAAKE,SAAS,EAAC,iEAAiE;UAAAC,QAAA,gBAE9EH,OAAA,CAACL,MAAM,CAACa,GAAG;YACTN,SAAS,EAAC,6BAA6B;YACvCO,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAX,QAAA,eAE9BH,OAAA,CAACJ,WAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGbP,OAAA,CAACL,MAAM,CAACa,GAAG;YACTN,SAAS,EAAC,6BAA6B;YACvCO,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAAAZ,QAAA,eAE1CH,OAAA,CAACH,gBAAgB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACS,EAAA,GApCQf,GAAG;AAsCZ,eAAeA,GAAG;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}