{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\components\\\\FileUploadSimple.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\nimport { Upload, FileText, CheckCircle, AlertCircle, Settings, Play, Download, Trash2, Terminal } from 'lucide-react';\nimport { StreamOutput } from './StreamOutput';\nimport { configureStageOutput } from '../data/streamOutputs/configureStage';\nimport { analyzeStageOutput } from '../data/streamOutputs/analyzeStage';\nimport { generateStageOutput } from '../data/streamOutputs/generateStage';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FileUploadSimple = () => {\n  _s();\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [selectedFileType, setSelectedFileType] = useState('household');\n  const [currentStreamStage, setCurrentStreamStage] = useState(null);\n  const fileInputRef = useRef(null);\n  const {\n    onFileUploaded,\n    onFileValidation,\n    onFileProcessing,\n    onReportGeneration,\n    resetFlow,\n    processState\n  } = useProcessFlowContext();\n  const handleFileUpload = event => {\n    const files = event.target.files;\n    if (files) {\n      Array.from(files).forEach(file => {\n        const newFile = {\n          id: Math.random().toString(36).substr(2, 9),\n          filename: file.name,\n          file_type: selectedFileType,\n          month: new Date().toISOString().slice(0, 7),\n          // YYYY-MM格式\n          status: 'uploaded',\n          upload_time: new Date().toISOString(),\n          file_path: `/uploads/${file.name}`\n        };\n        setUploadedFiles(prev => [...prev, newFile]);\n\n        // 触发流程联动\n        onFileUploaded(newFile.id, newFile.filename);\n      });\n    }\n  };\n  const validateFile = fileId => {\n    setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n      ...file,\n      status: 'validated'\n    } : file));\n\n    // 触发验证流程联动\n    onFileValidation(fileId);\n  };\n  const processFile = fileId => {\n    setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n      ...file,\n      status: 'processing'\n    } : file));\n\n    // 触发处理流程联动\n    onFileProcessing(fileId);\n    setTimeout(() => {\n      setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n        ...file,\n        status: 'processed'\n      } : file));\n    }, 3000);\n  };\n  const deleteFile = fileId => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  };\n  const processAllFiles = () => {\n    setIsProcessing(true);\n    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');\n\n    // 开始流式输出 - 解析配置阶段\n    setCurrentStreamStage('configure');\n\n    // 模拟处理流程\n    setTimeout(() => {\n      // 解析配置完成，开始智能分析\n      setCurrentStreamStage('analyze');\n\n      // 同时开始处理文件\n      validatedFiles.forEach((file, index) => {\n        setTimeout(() => {\n          processFile(file.id);\n        }, index * 1000);\n      });\n      setTimeout(() => {\n        // 智能分析完成，开始报表生成\n        setCurrentStreamStage('generate');\n        setTimeout(() => {\n          // 所有处理完成\n          setIsProcessing(false);\n          setCurrentStreamStage(null);\n        }, 8000); // 报表生成阶段时间\n      }, 6000); // 智能分析阶段时间\n    }, 4000); // 解析配置阶段时间\n  };\n\n  // 下载分析结果\n  const downloadAnalysisResult = (fileName, fileType) => {\n    // 模拟下载文件\n    const link = document.createElement('a');\n    link.href = '#'; // 实际项目中这里应该是后端API地址\n    link.download = `${fileName}_analysis_result.${fileType}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    // 显示下载成功提示\n    console.log(`下载 ${fileName} 的${fileType.toUpperCase()}分析结果`);\n  };\n\n  // 下载所有分析结果\n  const downloadAllResults = () => {\n    const processedFiles = uploadedFiles.filter(f => f.status === 'processed');\n    processedFiles.forEach(file => {\n      downloadAnalysisResult(file.filename, 'excel');\n    });\n\n    // 下载完成后重置流程\n    setTimeout(() => {\n      resetFlow();\n      setUploadedFiles([]);\n    }, 1000);\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'uploaded':\n        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/30';\n      case 'validated':\n        return 'text-green-400 bg-green-400/10 border-green-400/30';\n      case 'processing':\n        return 'text-blue-400 bg-blue-400/10 border-blue-400/30';\n      case 'processed':\n        return 'text-purple-400 bg-purple-400/10 border-purple-400/30';\n      case 'error':\n        return 'text-red-400 bg-red-400/10 border-red-400/30';\n      default:\n        return 'text-gray-400 bg-gray-400/10 border-gray-400/30';\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'uploaded':\n        return /*#__PURE__*/_jsxDEV(Upload, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 31\n        }, this);\n      case 'validated':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 32\n        }, this);\n      case 'processing':\n        return /*#__PURE__*/_jsxDEV(Settings, {\n          className: \"w-4 h-4 animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 33\n        }, this);\n      case 'processed':\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 32\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 28\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 23\n        }, this);\n    }\n  };\n\n  // 获取文件的流程进度信息\n  const getFileProgress = fileId => {\n    return processState.fileProgresses.find(fp => fp.fileId === fileId);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6 h-full flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-4\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-white\",\n          children: \"\\u6570\\u636E\\u6587\\u4EF6\\u4E0A\\u4F20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"text-sm font-medium text-gray-300\",\n              children: \"\\u7C7B\\u578B:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedFileType,\n              onChange: e => setSelectedFileType(e.target.value),\n              className: \"tech-input text-sm px-3 py-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"household\",\n                children: \"\\u5165\\u6237\\u6570\\u636E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"complaint\",\n                children: \"\\u6295\\u8BC9\\u6570\\u636E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              ref: fileInputRef,\n              type: \"file\",\n              multiple: true,\n              accept: \".xlsx,.xls,.csv\",\n              onChange: handleFileUpload,\n              className: \"hidden\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var _fileInputRef$current;\n                return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n              },\n              className: \"tech-button text-sm px-4 py-2\",\n              children: [/*#__PURE__*/_jsxDEV(Upload, {\n                className: \"w-4 h-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), \"\\u9009\\u62E9\\u6587\\u4EF6\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"tech-card flex flex-col\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        style: {\n          height: '400px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-4 border-b border-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold text-white\",\n            children: [\"\\u5DF2\\u4E0A\\u4F20\\u6587\\u4EF6 (\", uploadedFiles.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-2\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: processAllFiles,\n              disabled: isProcessing || uploadedFiles.filter(f => f.status === 'validated').length === 0,\n              className: \"tech-button text-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: [/*#__PURE__*/_jsxDEV(Play, {\n                className: \"w-4 h-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this), \"\\u4E00\\u952E\\u5904\\u7406\\u5168\\u90E8\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 overflow-y-auto space-y-3 p-4\",\n          style: {\n            maxHeight: '300px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n            children: uploadedFiles.map(file => /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"tech-border p-4 rounded-lg\",\n              initial: {\n                opacity: 0,\n                x: -20\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              exit: {\n                opacity: 0,\n                x: 20\n              },\n              layout: true,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3 flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-2 rounded-lg border ${getStatusColor(file.status)}`,\n                    children: getStatusIcon(file.status)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-white truncate\",\n                      children: file.filename\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 280,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: file.file_type === 'household' ? '入户数据' : '投诉数据'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 284,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: file.month\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 285,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-2 py-1 rounded-full text-xs ${getStatusColor(file.status)}`,\n                        children: [file.status === 'uploaded' && '已上传', file.status === 'validated' && '已验证', file.status === 'processing' && '处理中', file.status === 'processed' && '已处理', file.status === 'error' && '错误']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 286,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 283,\n                      columnNumber: 25\n                    }, this), (() => {\n                      const fileProgress = getFileProgress(file.id);\n                      if (fileProgress && fileProgress.stepProgress > 0) {\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mt-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex justify-between text-xs text-gray-400 mb-1\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: [\"\\u5F53\\u524D\\u6B65\\u9AA4: \", fileProgress.currentStep]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 302,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: [fileProgress.stepProgress, \"%\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 303,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 301,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-1\",\n                            children: /*#__PURE__*/_jsxDEV(motion.div, {\n                              className: \"bg-gradient-to-r from-blue-500 to-cyan-400 h-1 rounded-full\",\n                              initial: {\n                                width: 0\n                              },\n                              animate: {\n                                width: `${fileProgress.stepProgress}%`\n                              },\n                              transition: {\n                                duration: 0.3\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 306,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 305,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 300,\n                          columnNumber: 31\n                        }, this);\n                      }\n                      return null;\n                    })()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [file.status === 'uploaded' && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => validateFile(file.id),\n                    className: \"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\",\n                    title: \"\\u9A8C\\u8BC1\\u6587\\u4EF6\",\n                    children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 328,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 25\n                  }, this), file.status === 'validated' && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => processFile(file.id),\n                    className: \"p-2 text-tech-blue hover:bg-tech-blue/10 rounded-lg transition-colors\",\n                    title: \"\\u5904\\u7406\\u6587\\u4EF6\",\n                    children: /*#__PURE__*/_jsxDEV(Settings, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => deleteFile(file.id),\n                    className: \"p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors\",\n                    title: \"\\u5220\\u9664\\u6587\\u4EF6\",\n                    children: /*#__PURE__*/_jsxDEV(Trash2, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 347,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this)\n            }, file.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), uploadedFiles.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex items-center justify-center text-gray-400\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(FileText, {\n                className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u6682\\u65E0\\u4E0A\\u4F20\\u6587\\u4EF6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm\",\n                children: \"\\u8BF7\\u5148\\u4E0A\\u4F20\\u6570\\u636E\\u6587\\u4EF6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"tech-card flex flex-col\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        style: {\n          height: '400px'\n        },\n        children: currentStreamStage ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [currentStreamStage === 'configure' && /*#__PURE__*/_jsxDEV(StreamOutput, {\n            content: configureStageOutput,\n            isActive: true,\n            title: \"\\u89E3\\u6790\\u914D\\u7F6E\\u9636\\u6BB5\",\n            onComplete: () => {},\n            speed: 100\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 17\n          }, this), currentStreamStage === 'analyze' && /*#__PURE__*/_jsxDEV(StreamOutput, {\n            content: analyzeStageOutput,\n            isActive: true,\n            title: \"\\u667A\\u80FD\\u5206\\u6790\\u9636\\u6BB5\",\n            onComplete: () => {},\n            speed: 120\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 17\n          }, this), currentStreamStage === 'generate' && /*#__PURE__*/_jsxDEV(StreamOutput, {\n            content: generateStageOutput,\n            isActive: true,\n            title: \"\\u62A5\\u8868\\u751F\\u6210\\u9636\\u6BB5\",\n            onComplete: () => {},\n            speed: 80\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 flex items-center justify-center text-gray-400\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(Terminal, {\n              className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"AI\\u5904\\u7406\\u8F93\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm\",\n              children: \"\\u70B9\\u51FB\\\"\\u4E00\\u952E\\u5904\\u7406\\u5168\\u90E8\\\"\\u5F00\\u59CB\\u667A\\u80FD\\u5206\\u6790\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-6\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.4\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"tech-button text-lg px-8 py-3\",\n            onClick: onReportGeneration,\n            children: [/*#__PURE__*/_jsxDEV(FileText, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this), \"\\u751F\\u6210\\u667A\\u80FD\\u5206\\u6790\\u62A5\\u8868\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this), uploadedFiles.some(f => f.status === 'processed') && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"tech-card p-4 bg-green-900/20 border-green-500/30\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-green-400 font-semibold mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                className: \"w-5 h-5 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 19\n              }, this), \"\\u5206\\u6790\\u7ED3\\u679C\\u4E0B\\u8F7D\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [uploadedFiles.filter(f => f.status === 'processed').map(file => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between bg-gray-800/50 p-3 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: file.filename\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => downloadAnalysisResult(file.filename, 'excel'),\n                    className: \"text-xs bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(Download, {\n                      className: \"w-3 h-3 mr-1 inline\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 27\n                    }, this), \"Excel\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => downloadAnalysisResult(file.filename, 'pdf'),\n                    className: \"text-xs bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(Download, {\n                      className: \"w-3 h-3 mr-1 inline\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 27\n                    }, this), \"PDF\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => downloadAnalysisResult(file.filename, 'json'),\n                    className: \"text-xs bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(Download, {\n                      className: \"w-3 h-3 mr-1 inline\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 468,\n                      columnNumber: 27\n                    }, this), \"JSON\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 23\n                }, this)]\n              }, file.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 21\n              }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: downloadAllResults,\n                className: \"w-full mt-4 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-2 px-4 rounded-lg transition-all duration-300 flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(Download, {\n                  className: \"w-4 h-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this), \"\\u4E0B\\u8F7D\\u6240\\u6709\\u5206\\u6790\\u7ED3\\u679C\\u5E76\\u91CD\\u7F6E\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this);\n};\n_s(FileUploadSimple, \"mOsWkqGOrsjiHAa8nNRBAUYR51w=\", false, function () {\n  return [useProcessFlowContext];\n});\n_c = FileUploadSimple;\nexport default FileUploadSimple;\nvar _c;\n$RefreshReg$(_c, \"FileUploadSimple\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "motion", "AnimatePresence", "useProcessFlowContext", "Upload", "FileText", "CheckCircle", "AlertCircle", "Settings", "Play", "Download", "Trash2", "Terminal", "StreamOutput", "configureStageOutput", "analyzeStageOutput", "generateStageOutput", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FileUploadSimple", "_s", "uploadedFiles", "setUploadedFiles", "isProcessing", "setIsProcessing", "selectedFileType", "setSelectedFileType", "currentStreamStage", "setCurrentStreamStage", "fileInputRef", "onFileUploaded", "onFileValidation", "onFileProcessing", "onReportGeneration", "resetFlow", "processState", "handleFileUpload", "event", "files", "target", "Array", "from", "for<PERSON>ach", "file", "newFile", "id", "Math", "random", "toString", "substr", "filename", "name", "file_type", "month", "Date", "toISOString", "slice", "status", "upload_time", "file_path", "prev", "validateFile", "fileId", "map", "processFile", "setTimeout", "deleteFile", "filter", "processAllFiles", "validatedFiles", "index", "downloadAnalysisResult", "fileName", "fileType", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "toUpperCase", "downloadAllResults", "processedFiles", "f", "getStatusColor", "getStatusIcon", "className", "_jsxFileName", "lineNumber", "columnNumber", "getFileProgress", "fileProgresses", "find", "fp", "children", "div", "initial", "opacity", "y", "animate", "value", "onChange", "e", "ref", "type", "multiple", "accept", "onClick", "_fileInputRef$current", "current", "style", "height", "length", "disabled", "maxHeight", "x", "exit", "layout", "fileProgress", "stepProgress", "currentStep", "width", "transition", "duration", "title", "content", "isActive", "onComplete", "speed", "delay", "some", "_c", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/components/FileUploadSimple.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\nimport { \n  Upload, \n  FileText, \n  CheckCircle, \n  AlertCircle, \n  Settings, \n  Play, \n  Download,\n  Trash2,\n  Eye,\n  Zap,\n  Terminal\n} from 'lucide-react';\nimport { StreamOutput } from './StreamOutput';\nimport { configureStageOutput } from '../data/streamOutputs/configureStage';\nimport { analyzeStageOutput } from '../data/streamOutputs/analyzeStage';\nimport { generateStageOutput } from '../data/streamOutputs/generateStage';\n\ninterface UploadedFile {\n  id: string;\n  filename: string;\n  file_type: 'household' | 'complaint';\n  month: string;\n  status: 'uploaded' | 'validated' | 'processing' | 'processed' | 'error';\n  upload_time: string;\n  file_path: string;\n  error_message?: string;\n}\n\nconst FileUploadSimple: React.FC = () => {\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [selectedFileType, setSelectedFileType] = useState<'household' | 'complaint'>('household');\n  const [currentStreamStage, setCurrentStreamStage] = useState<'configure' | 'analyze' | 'generate' | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const { onFileUploaded, onFileValidation, onFileProcessing, onReportGeneration, resetFlow, processState } = useProcessFlowContext();\n\n  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files;\n    if (files) {\n      Array.from(files).forEach(file => {\n        const newFile: UploadedFile = {\n          id: Math.random().toString(36).substr(2, 9),\n          filename: file.name,\n          file_type: selectedFileType,\n          month: new Date().toISOString().slice(0, 7), // YYYY-MM格式\n          status: 'uploaded',\n          upload_time: new Date().toISOString(),\n          file_path: `/uploads/${file.name}`\n        };\n        \n        setUploadedFiles(prev => [...prev, newFile]);\n        \n        // 触发流程联动\n        onFileUploaded(newFile.id, newFile.filename);\n      });\n    }\n  };\n\n  const validateFile = (fileId: string) => {\n    setUploadedFiles(prev => \n      prev.map(file => \n        file.id === fileId \n          ? { ...file, status: 'validated' }\n          : file\n      )\n    );\n    \n    // 触发验证流程联动\n    onFileValidation(fileId);\n  };\n\n  const processFile = (fileId: string) => {\n    setUploadedFiles(prev => \n      prev.map(file => \n        file.id === fileId \n          ? { ...file, status: 'processing' }\n          : file\n      )\n    );\n\n    // 触发处理流程联动\n    onFileProcessing(fileId);\n\n    setTimeout(() => {\n      setUploadedFiles(prev => \n        prev.map(file => \n          file.id === fileId \n            ? { ...file, status: 'processed' }\n            : file\n        )\n      );\n    }, 3000);\n  };\n\n  const deleteFile = (fileId: string) => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  };\n\n  const processAllFiles = () => {\n    setIsProcessing(true);\n    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');\n    \n    // 开始流式输出 - 解析配置阶段\n    setCurrentStreamStage('configure');\n    \n    // 模拟处理流程\n    setTimeout(() => {\n      // 解析配置完成，开始智能分析\n      setCurrentStreamStage('analyze');\n      \n      // 同时开始处理文件\n      validatedFiles.forEach((file, index) => {\n        setTimeout(() => {\n          processFile(file.id);\n        }, index * 1000);\n      });\n      \n      setTimeout(() => {\n        // 智能分析完成，开始报表生成\n        setCurrentStreamStage('generate');\n        \n        setTimeout(() => {\n          // 所有处理完成\n          setIsProcessing(false);\n          setCurrentStreamStage(null);\n        }, 8000); // 报表生成阶段时间\n      }, 6000); // 智能分析阶段时间\n    }, 4000); // 解析配置阶段时间\n  };\n\n  // 下载分析结果\n  const downloadAnalysisResult = (fileName: string, fileType: 'excel' | 'pdf' | 'json') => {\n    // 模拟下载文件\n    const link = document.createElement('a');\n    link.href = '#'; // 实际项目中这里应该是后端API地址\n    link.download = `${fileName}_analysis_result.${fileType}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    \n    // 显示下载成功提示\n    console.log(`下载 ${fileName} 的${fileType.toUpperCase()}分析结果`);\n  };\n\n  // 下载所有分析结果\n  const downloadAllResults = () => {\n    const processedFiles = uploadedFiles.filter(f => f.status === 'processed');\n    \n    processedFiles.forEach(file => {\n      downloadAnalysisResult(file.filename, 'excel');\n    });\n    \n    // 下载完成后重置流程\n    setTimeout(() => {\n      resetFlow();\n      setUploadedFiles([]);\n    }, 1000);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'uploaded': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/30';\n      case 'validated': return 'text-green-400 bg-green-400/10 border-green-400/30';\n      case 'processing': return 'text-blue-400 bg-blue-400/10 border-blue-400/30';\n      case 'processed': return 'text-purple-400 bg-purple-400/10 border-purple-400/30';\n      case 'error': return 'text-red-400 bg-red-400/10 border-red-400/30';\n      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/30';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'uploaded': return <Upload className=\"w-4 h-4\" />;\n      case 'validated': return <CheckCircle className=\"w-4 h-4\" />;\n      case 'processing': return <Settings className=\"w-4 h-4 animate-spin\" />;\n      case 'processed': return <FileText className=\"w-4 h-4\" />;\n      case 'error': return <AlertCircle className=\"w-4 h-4\" />;\n      default: return <FileText className=\"w-4 h-4\" />;\n    }\n  };\n\n  // 获取文件的流程进度信息\n  const getFileProgress = (fileId: string) => {\n    return processState.fileProgresses.find(fp => fp.fileId === fileId);\n  };\n\n  return (\n    <div className=\"space-y-6 h-full flex flex-col\">\n      {/* 顶部控制区域 */}\n      <motion.div\n        className=\"tech-card p-4\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        <div className=\"flex items-center justify-between\">\n          <h2 className=\"text-xl font-bold text-white\">数据文件上传</h2>\n          \n          {/* 文件上传控制 */}\n          <div className=\"flex items-center space-x-4\">\n            {/* 文件类型选择 */}\n            <div className=\"flex items-center space-x-2\">\n              <label className=\"text-sm font-medium text-gray-300\">类型:</label>\n              <select\n                value={selectedFileType}\n                onChange={(e) => setSelectedFileType(e.target.value as 'household' | 'complaint')}\n                className=\"tech-input text-sm px-3 py-1\"\n              >\n                <option value=\"household\">入户数据</option>\n                <option value=\"complaint\">投诉数据</option>\n              </select>\n            </div>\n            \n            {/* 上传按钮 */}\n            <div>\n              <input\n                ref={fileInputRef}\n                type=\"file\"\n                multiple\n                accept=\".xlsx,.xls,.csv\"\n                onChange={handleFileUpload}\n                className=\"hidden\"\n              />\n              <button\n                onClick={() => fileInputRef.current?.click()}\n                className=\"tech-button text-sm px-4 py-2\"\n              >\n                <Upload className=\"w-4 h-4 mr-2\" />\n                选择文件\n              </button>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* 主要内容区域 */}\n      <div className=\"flex-1 space-y-6\">\n        {/* 已上传文件列表 - 横向扩充 */}\n        <motion.div\n          className=\"tech-card flex flex-col\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          style={{ height: '400px' }}\n        >\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-700\">\n            <h3 className=\"font-semibold text-white\">已上传文件 ({uploadedFiles.length})</h3>\n            <div className=\"flex space-x-2\">\n              <button\n                onClick={processAllFiles}\n                disabled={isProcessing || uploadedFiles.filter(f => f.status === 'validated').length === 0}\n                className=\"tech-button text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                <Play className=\"w-4 h-4 mr-2\" />\n                一键处理全部\n              </button>\n            </div>\n          </div>\n\n          <div className=\"flex-1 overflow-y-auto space-y-3 p-4\" style={{ maxHeight: '300px' }}>\n            <AnimatePresence>\n              {uploadedFiles.map((file) => (\n                <motion.div\n                  key={file.id}\n                  className=\"tech-border p-4 rounded-lg\"\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  exit={{ opacity: 0, x: 20 }}\n                  layout\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-3 flex-1\">\n                      <div className={`p-2 rounded-lg border ${getStatusColor(file.status)}`}>\n                        {getStatusIcon(file.status)}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <h4 className=\"font-medium text-white truncate\">\n                          {file.filename}\n                        </h4>\n                        <div className=\"flex items-center space-x-4 text-sm text-gray-400\">\n                          <span>{file.file_type === 'household' ? '入户数据' : '投诉数据'}</span>\n                          <span>{file.month}</span>\n                          <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(file.status)}`}>\n                            {file.status === 'uploaded' && '已上传'}\n                            {file.status === 'validated' && '已验证'}\n                            {file.status === 'processing' && '处理中'}\n                            {file.status === 'processed' && '已处理'}\n                            {file.status === 'error' && '错误'}\n                          </span>\n                        </div>\n                        \n                        {/* 文件级进度显示 */}\n                        {(() => {\n                          const fileProgress = getFileProgress(file.id);\n                          if (fileProgress && fileProgress.stepProgress > 0) {\n                            return (\n                              <div className=\"mt-2\">\n                                <div className=\"flex justify-between text-xs text-gray-400 mb-1\">\n                                  <span>当前步骤: {fileProgress.currentStep}</span>\n                                  <span>{fileProgress.stepProgress}%</span>\n                                </div>\n                                <div className=\"w-full bg-gray-700 rounded-full h-1\">\n                                  <motion.div\n                                    className=\"bg-gradient-to-r from-blue-500 to-cyan-400 h-1 rounded-full\"\n                                    initial={{ width: 0 }}\n                                    animate={{ width: `${fileProgress.stepProgress}%` }}\n                                    transition={{ duration: 0.3 }}\n                                  />\n                                </div>\n                              </div>\n                            );\n                          }\n                          return null;\n                        })()}\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center space-x-2\">\n                      {file.status === 'uploaded' && (\n                        <button\n                          onClick={() => validateFile(file.id)}\n                          className=\"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\"\n                          title=\"验证文件\"\n                        >\n                          <CheckCircle className=\"w-4 h-4\" />\n                        </button>\n                      )}\n                      \n                      {file.status === 'validated' && (\n                        <button\n                          onClick={() => processFile(file.id)}\n                          className=\"p-2 text-tech-blue hover:bg-tech-blue/10 rounded-lg transition-colors\"\n                          title=\"处理文件\"\n                        >\n                          <Settings className=\"w-4 h-4\" />\n                        </button>\n                      )}\n                      \n                      <button\n                        onClick={() => deleteFile(file.id)}\n                        className=\"p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors\"\n                        title=\"删除文件\"\n                      >\n                        <Trash2 className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </AnimatePresence>\n\n            {uploadedFiles.length === 0 && (\n              <div className=\"flex-1 flex items-center justify-center text-gray-400\">\n                <div className=\"text-center\">\n                  <FileText className=\"w-12 h-12 mx-auto mb-2 opacity-50\" />\n                  <p>暂无上传文件</p>\n                  <p className=\"text-sm\">请先上传数据文件</p>\n                </div>\n              </div>\n            )}\n          </div>\n        </motion.div>\n\n        {/* AI处理输出区域 - 移至原生成智能分析报表位置 */}\n        <motion.div\n          className=\"tech-card flex flex-col\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          style={{ height: '400px' }}\n        >\n          {currentStreamStage ? (\n            <>\n              {currentStreamStage === 'configure' && (\n                <StreamOutput\n                  content={configureStageOutput}\n                  isActive={true}\n                  title=\"解析配置阶段\"\n                  onComplete={() => {}}\n                  speed={100}\n                />\n              )}\n              {currentStreamStage === 'analyze' && (\n                <StreamOutput\n                  content={analyzeStageOutput}\n                  isActive={true}\n                  title=\"智能分析阶段\"\n                  onComplete={() => {}}\n                  speed={120}\n                />\n              )}\n              {currentStreamStage === 'generate' && (\n                <StreamOutput\n                  content={generateStageOutput}\n                  isActive={true}\n                  title=\"报表生成阶段\"\n                  onComplete={() => {}}\n                  speed={80}\n                />\n              )}\n            </>\n          ) : (\n            <div className=\"flex-1 flex items-center justify-center text-gray-400\">\n              <div className=\"text-center\">\n                <Terminal className=\"w-12 h-12 mx-auto mb-2 opacity-50\" />\n                <p>AI处理输出</p>\n                <p className=\"text-sm\">点击\"一键处理全部\"开始智能分析</p>\n              </div>\n            </div>\n          )}\n        </motion.div>\n      </div>\n\n      {/* 底部操作区域 */}\n      <motion.div\n        className=\"tech-card p-6\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.4 }}\n      >\n        <div className=\"flex items-center justify-center\">\n          <div className=\"flex flex-col space-y-4\">\n            <button\n              className=\"tech-button text-lg px-8 py-3\"\n              onClick={onReportGeneration}\n            >\n              <FileText className=\"w-5 h-5 mr-2\" />\n              生成智能分析报表\n            </button>\n\n            {/* 分析结果下载区域 */}\n            {uploadedFiles.some(f => f.status === 'processed') && (\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"tech-card p-4 bg-green-900/20 border-green-500/30\"\n              >\n                <h4 className=\"text-green-400 font-semibold mb-3 flex items-center\">\n                  <CheckCircle className=\"w-5 h-5 mr-2\" />\n                  分析结果下载\n                </h4>\n\n                <div className=\"space-y-2\">\n                  {uploadedFiles.filter(f => f.status === 'processed').map(file => (\n                    <div key={file.id} className=\"flex items-center justify-between bg-gray-800/50 p-3 rounded-lg\">\n                      <span className=\"text-gray-300\">{file.filename}</span>\n                      <div className=\"flex space-x-2\">\n                        <button\n                          onClick={() => downloadAnalysisResult(file.filename, 'excel')}\n                          className=\"text-xs bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded transition-colors\"\n                        >\n                          <Download className=\"w-3 h-3 mr-1 inline\" />\n                          Excel\n                        </button>\n                        <button\n                          onClick={() => downloadAnalysisResult(file.filename, 'pdf')}\n                          className=\"text-xs bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded transition-colors\"\n                        >\n                          <Download className=\"w-3 h-3 mr-1 inline\" />\n                          PDF\n                        </button>\n                        <button\n                          onClick={() => downloadAnalysisResult(file.filename, 'json')}\n                          className=\"text-xs bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded transition-colors\"\n                        >\n                          <Download className=\"w-3 h-3 mr-1 inline\" />\n                          JSON\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n\n                  <button\n                    onClick={downloadAllResults}\n                    className=\"w-full mt-4 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-2 px-4 rounded-lg transition-all duration-300 flex items-center justify-center\"\n                  >\n                    <Download className=\"w-4 h-4 mr-2\" />\n                    下载所有分析结果并重置\n                  </button>\n                </div>\n              </motion.div>\n            )}\n          </div>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default FileUploadSimple;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SACEC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,WAAW,EACXC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,MAAM,EAGNC,QAAQ,QACH,cAAc;AACrB,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,oBAAoB,QAAQ,sCAAsC;AAC3E,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,mBAAmB,QAAQ,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAa1E,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAA4B,WAAW,CAAC;EAChG,MAAM,CAAC8B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/B,QAAQ,CAA8C,IAAI,CAAC;EAC/G,MAAMgC,YAAY,GAAG/B,MAAM,CAAmB,IAAI,CAAC;EAEnD,MAAM;IAAEgC,cAAc;IAAEC,gBAAgB;IAAEC,gBAAgB;IAAEC,kBAAkB;IAAEC,SAAS;IAAEC;EAAa,CAAC,GAAGlC,qBAAqB,CAAC,CAAC;EAEnI,MAAMmC,gBAAgB,GAAIC,KAA0C,IAAK;IACvE,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,EAAE;MACTE,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,OAAO,CAACC,IAAI,IAAI;QAChC,MAAMC,OAAqB,GAAG;UAC5BC,EAAE,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;UAC3CC,QAAQ,EAAEP,IAAI,CAACQ,IAAI;UACnBC,SAAS,EAAE3B,gBAAgB;UAC3B4B,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;UAAE;UAC7CC,MAAM,EAAE,UAAU;UAClBC,WAAW,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACrCI,SAAS,EAAE,YAAYhB,IAAI,CAACQ,IAAI;QAClC,CAAC;QAED7B,gBAAgB,CAACsC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEhB,OAAO,CAAC,CAAC;;QAE5C;QACAd,cAAc,CAACc,OAAO,CAACC,EAAE,EAAED,OAAO,CAACM,QAAQ,CAAC;MAC9C,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMW,YAAY,GAAIC,MAAc,IAAK;IACvCxC,gBAAgB,CAACsC,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACpB,IAAI,IACXA,IAAI,CAACE,EAAE,KAAKiB,MAAM,GACd;MAAE,GAAGnB,IAAI;MAAEc,MAAM,EAAE;IAAY,CAAC,GAChCd,IACN,CACF,CAAC;;IAED;IACAZ,gBAAgB,CAAC+B,MAAM,CAAC;EAC1B,CAAC;EAED,MAAME,WAAW,GAAIF,MAAc,IAAK;IACtCxC,gBAAgB,CAACsC,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACpB,IAAI,IACXA,IAAI,CAACE,EAAE,KAAKiB,MAAM,GACd;MAAE,GAAGnB,IAAI;MAAEc,MAAM,EAAE;IAAa,CAAC,GACjCd,IACN,CACF,CAAC;;IAED;IACAX,gBAAgB,CAAC8B,MAAM,CAAC;IAExBG,UAAU,CAAC,MAAM;MACf3C,gBAAgB,CAACsC,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACpB,IAAI,IACXA,IAAI,CAACE,EAAE,KAAKiB,MAAM,GACd;QAAE,GAAGnB,IAAI;QAAEc,MAAM,EAAE;MAAY,CAAC,GAChCd,IACN,CACF,CAAC;IACH,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMuB,UAAU,GAAIJ,MAAc,IAAK;IACrCxC,gBAAgB,CAACsC,IAAI,IAAIA,IAAI,CAACO,MAAM,CAACxB,IAAI,IAAIA,IAAI,CAACE,EAAE,KAAKiB,MAAM,CAAC,CAAC;EACnE,CAAC;EAED,MAAMM,eAAe,GAAGA,CAAA,KAAM;IAC5B5C,eAAe,CAAC,IAAI,CAAC;IACrB,MAAM6C,cAAc,GAAGhD,aAAa,CAAC8C,MAAM,CAACxB,IAAI,IAAIA,IAAI,CAACc,MAAM,KAAK,WAAW,CAAC;;IAEhF;IACA7B,qBAAqB,CAAC,WAAW,CAAC;;IAElC;IACAqC,UAAU,CAAC,MAAM;MACf;MACArC,qBAAqB,CAAC,SAAS,CAAC;;MAEhC;MACAyC,cAAc,CAAC3B,OAAO,CAAC,CAACC,IAAI,EAAE2B,KAAK,KAAK;QACtCL,UAAU,CAAC,MAAM;UACfD,WAAW,CAACrB,IAAI,CAACE,EAAE,CAAC;QACtB,CAAC,EAAEyB,KAAK,GAAG,IAAI,CAAC;MAClB,CAAC,CAAC;MAEFL,UAAU,CAAC,MAAM;QACf;QACArC,qBAAqB,CAAC,UAAU,CAAC;QAEjCqC,UAAU,CAAC,MAAM;UACf;UACAzC,eAAe,CAAC,KAAK,CAAC;UACtBI,qBAAqB,CAAC,IAAI,CAAC;QAC7B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC;;EAED;EACA,MAAM2C,sBAAsB,GAAGA,CAACC,QAAgB,EAAEC,QAAkC,KAAK;IACvF;IACA,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,GAAG,CAAC,CAAC;IACjBH,IAAI,CAACI,QAAQ,GAAG,GAAGN,QAAQ,oBAAoBC,QAAQ,EAAE;IACzDE,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;;IAE/B;IACAS,OAAO,CAACC,GAAG,CAAC,MAAMZ,QAAQ,KAAKC,QAAQ,CAACY,WAAW,CAAC,CAAC,MAAM,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,cAAc,GAAGlE,aAAa,CAAC8C,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAAC/B,MAAM,KAAK,WAAW,CAAC;IAE1E8B,cAAc,CAAC7C,OAAO,CAACC,IAAI,IAAI;MAC7B4B,sBAAsB,CAAC5B,IAAI,CAACO,QAAQ,EAAE,OAAO,CAAC;IAChD,CAAC,CAAC;;IAEF;IACAe,UAAU,CAAC,MAAM;MACf/B,SAAS,CAAC,CAAC;MACXZ,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMmE,cAAc,GAAIhC,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,UAAU;QAAE,OAAO,uDAAuD;MAC/E,KAAK,WAAW;QAAE,OAAO,oDAAoD;MAC7E,KAAK,YAAY;QAAE,OAAO,iDAAiD;MAC3E,KAAK,WAAW;QAAE,OAAO,uDAAuD;MAChF,KAAK,OAAO;QAAE,OAAO,8CAA8C;MACnE;QAAS,OAAO,iDAAiD;IACnE;EACF,CAAC;EAED,MAAMiC,aAAa,GAAIjC,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,UAAU;QAAE,oBAAOzC,OAAA,CAACd,MAAM;UAACyF,SAAS,EAAC;QAAS;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,WAAW;QAAE,oBAAO9E,OAAA,CAACZ,WAAW;UAACuF,SAAS,EAAC;QAAS;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5D,KAAK,YAAY;QAAE,oBAAO9E,OAAA,CAACV,QAAQ;UAACqF,SAAS,EAAC;QAAsB;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvE,KAAK,WAAW;QAAE,oBAAO9E,OAAA,CAACb,QAAQ;UAACwF,SAAS,EAAC;QAAS;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD,KAAK,OAAO;QAAE,oBAAO9E,OAAA,CAACX,WAAW;UAACsF,SAAS,EAAC;QAAS;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxD;QAAS,oBAAO9E,OAAA,CAACb,QAAQ;UAACwF,SAAS,EAAC;QAAS;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIjC,MAAc,IAAK;IAC1C,OAAO3B,YAAY,CAAC6D,cAAc,CAACC,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACpC,MAAM,KAAKA,MAAM,CAAC;EACrE,CAAC;EAED,oBACE9C,OAAA;IAAK2E,SAAS,EAAC,gCAAgC;IAAAQ,QAAA,gBAE7CnF,OAAA,CAACjB,MAAM,CAACqG,GAAG;MACTT,SAAS,EAAC,eAAe;MACzBU,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAE9BnF,OAAA;QAAK2E,SAAS,EAAC,mCAAmC;QAAAQ,QAAA,gBAChDnF,OAAA;UAAI2E,SAAS,EAAC,8BAA8B;UAAAQ,QAAA,EAAC;QAAM;UAAA3B,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGxD9E,OAAA;UAAK2E,SAAS,EAAC,6BAA6B;UAAAQ,QAAA,gBAE1CnF,OAAA;YAAK2E,SAAS,EAAC,6BAA6B;YAAAQ,QAAA,gBAC1CnF,OAAA;cAAO2E,SAAS,EAAC,mCAAmC;cAAAQ,QAAA,EAAC;YAAG;cAAA3B,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChE9E,OAAA;cACEyF,KAAK,EAAEhF,gBAAiB;cACxBiF,QAAQ,EAAGC,CAAC,IAAKjF,mBAAmB,CAACiF,CAAC,CAACpE,MAAM,CAACkE,KAAkC,CAAE;cAClFd,SAAS,EAAC,8BAA8B;cAAAQ,QAAA,gBAExCnF,OAAA;gBAAQyF,KAAK,EAAC,WAAW;gBAAAN,QAAA,EAAC;cAAI;gBAAA3B,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvC9E,OAAA;gBAAQyF,KAAK,EAAC,WAAW;gBAAAN,QAAA,EAAC;cAAI;gBAAA3B,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN9E,OAAA;YAAAmF,QAAA,gBACEnF,OAAA;cACE4F,GAAG,EAAE/E,YAAa;cAClBgF,IAAI,EAAC,MAAM;cACXC,QAAQ;cACRC,MAAM,EAAC,iBAAiB;cACxBL,QAAQ,EAAEtE,gBAAiB;cAC3BuD,SAAS,EAAC;YAAQ;cAAAnB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACF9E,OAAA;cACEgG,OAAO,EAAEA,CAAA;gBAAA,IAAAC,qBAAA;gBAAA,QAAAA,qBAAA,GAAMpF,YAAY,CAACqF,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBhC,KAAK,CAAC,CAAC;cAAA,CAAC;cAC7CU,SAAS,EAAC,+BAA+B;cAAAQ,QAAA,gBAEzCnF,OAAA,CAACd,MAAM;gBAACyF,SAAS,EAAC;cAAc;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAErC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAtB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGb9E,OAAA;MAAK2E,SAAS,EAAC,kBAAkB;MAAAQ,QAAA,gBAE/BnF,OAAA,CAACjB,MAAM,CAACqG,GAAG;QACTT,SAAS,EAAC,yBAAyB;QACnCU,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BY,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAjB,QAAA,gBAE3BnF,OAAA;UAAK2E,SAAS,EAAC,gEAAgE;UAAAQ,QAAA,gBAC7EnF,OAAA;YAAI2E,SAAS,EAAC,0BAA0B;YAAAQ,QAAA,GAAC,kCAAO,EAAC9E,aAAa,CAACgG,MAAM,EAAC,GAAC;UAAA;YAAA7C,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5E9E,OAAA;YAAK2E,SAAS,EAAC,gBAAgB;YAAAQ,QAAA,eAC7BnF,OAAA;cACEgG,OAAO,EAAE5C,eAAgB;cACzBkD,QAAQ,EAAE/F,YAAY,IAAIF,aAAa,CAAC8C,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAAC/B,MAAM,KAAK,WAAW,CAAC,CAAC4D,MAAM,KAAK,CAAE;cAC3F1B,SAAS,EAAC,qEAAqE;cAAAQ,QAAA,gBAE/EnF,OAAA,CAACT,IAAI;gBAACoF,SAAS,EAAC;cAAc;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wCAEnC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9E,OAAA;UAAK2E,SAAS,EAAC,sCAAsC;UAACwB,KAAK,EAAE;YAAEI,SAAS,EAAE;UAAQ,CAAE;UAAApB,QAAA,gBAClFnF,OAAA,CAAChB,eAAe;YAAAmG,QAAA,EACb9E,aAAa,CAAC0C,GAAG,CAAEpB,IAAI,iBACtB3B,OAAA,CAACjB,MAAM,CAACqG,GAAG;cAETT,SAAS,EAAC,4BAA4B;cACtCU,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEkB,CAAC,EAAE,CAAC;cAAG,CAAE;cAChChB,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEkB,CAAC,EAAE;cAAE,CAAE;cAC9BC,IAAI,EAAE;gBAAEnB,OAAO,EAAE,CAAC;gBAAEkB,CAAC,EAAE;cAAG,CAAE;cAC5BE,MAAM;cAAAvB,QAAA,eAENnF,OAAA;gBAAK2E,SAAS,EAAC,mCAAmC;gBAAAQ,QAAA,gBAChDnF,OAAA;kBAAK2E,SAAS,EAAC,oCAAoC;kBAAAQ,QAAA,gBACjDnF,OAAA;oBAAK2E,SAAS,EAAE,yBAAyBF,cAAc,CAAC9C,IAAI,CAACc,MAAM,CAAC,EAAG;oBAAA0C,QAAA,EACpET,aAAa,CAAC/C,IAAI,CAACc,MAAM;kBAAC;oBAAAe,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACN9E,OAAA;oBAAK2E,SAAS,EAAC,gBAAgB;oBAAAQ,QAAA,gBAC7BnF,OAAA;sBAAI2E,SAAS,EAAC,iCAAiC;sBAAAQ,QAAA,EAC5CxD,IAAI,CAACO;oBAAQ;sBAAAsB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC,eACL9E,OAAA;sBAAK2E,SAAS,EAAC,mDAAmD;sBAAAQ,QAAA,gBAChEnF,OAAA;wBAAAmF,QAAA,EAAOxD,IAAI,CAACS,SAAS,KAAK,WAAW,GAAG,MAAM,GAAG;sBAAM;wBAAAoB,QAAA,EAAAoB,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC/D9E,OAAA;wBAAAmF,QAAA,EAAOxD,IAAI,CAACU;sBAAK;wBAAAmB,QAAA,EAAAoB,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACzB9E,OAAA;wBAAM2E,SAAS,EAAE,kCAAkCF,cAAc,CAAC9C,IAAI,CAACc,MAAM,CAAC,EAAG;wBAAA0C,QAAA,GAC9ExD,IAAI,CAACc,MAAM,KAAK,UAAU,IAAI,KAAK,EACnCd,IAAI,CAACc,MAAM,KAAK,WAAW,IAAI,KAAK,EACpCd,IAAI,CAACc,MAAM,KAAK,YAAY,IAAI,KAAK,EACrCd,IAAI,CAACc,MAAM,KAAK,WAAW,IAAI,KAAK,EACpCd,IAAI,CAACc,MAAM,KAAK,OAAO,IAAI,IAAI;sBAAA;wBAAAe,QAAA,EAAAoB,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC;oBAAA;sBAAAtB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,EAGL,CAAC,MAAM;sBACN,MAAM6B,YAAY,GAAG5B,eAAe,CAACpD,IAAI,CAACE,EAAE,CAAC;sBAC7C,IAAI8E,YAAY,IAAIA,YAAY,CAACC,YAAY,GAAG,CAAC,EAAE;wBACjD,oBACE5G,OAAA;0BAAK2E,SAAS,EAAC,MAAM;0BAAAQ,QAAA,gBACnBnF,OAAA;4BAAK2E,SAAS,EAAC,iDAAiD;4BAAAQ,QAAA,gBAC9DnF,OAAA;8BAAAmF,QAAA,GAAM,4BAAM,EAACwB,YAAY,CAACE,WAAW;4BAAA;8BAAArD,QAAA,EAAAoB,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,eAC7C9E,OAAA;8BAAAmF,QAAA,GAAOwB,YAAY,CAACC,YAAY,EAAC,GAAC;4BAAA;8BAAApD,QAAA,EAAAoB,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAtB,QAAA,EAAAoB,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC,CAAC,eACN9E,OAAA;4BAAK2E,SAAS,EAAC,qCAAqC;4BAAAQ,QAAA,eAClDnF,OAAA,CAACjB,MAAM,CAACqG,GAAG;8BACTT,SAAS,EAAC,6DAA6D;8BACvEU,OAAO,EAAE;gCAAEyB,KAAK,EAAE;8BAAE,CAAE;8BACtBtB,OAAO,EAAE;gCAAEsB,KAAK,EAAE,GAAGH,YAAY,CAACC,YAAY;8BAAI,CAAE;8BACpDG,UAAU,EAAE;gCAAEC,QAAQ,EAAE;8BAAI;4BAAE;8BAAAxD,QAAA,EAAAoB,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC/B;0BAAC;4BAAAtB,QAAA,EAAAoB,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAtB,QAAA,EAAAoB,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAEV;sBACA,OAAO,IAAI;oBACb,CAAC,EAAE,CAAC;kBAAA;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN9E,OAAA;kBAAK2E,SAAS,EAAC,6BAA6B;kBAAAQ,QAAA,GACzCxD,IAAI,CAACc,MAAM,KAAK,UAAU,iBACzBzC,OAAA;oBACEgG,OAAO,EAAEA,CAAA,KAAMnD,YAAY,CAAClB,IAAI,CAACE,EAAE,CAAE;oBACrC8C,SAAS,EAAC,yEAAyE;oBACnFsC,KAAK,EAAC,0BAAM;oBAAA9B,QAAA,eAEZnF,OAAA,CAACZ,WAAW;sBAACuF,SAAS,EAAC;oBAAS;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CACT,EAEAnD,IAAI,CAACc,MAAM,KAAK,WAAW,iBAC1BzC,OAAA;oBACEgG,OAAO,EAAEA,CAAA,KAAMhD,WAAW,CAACrB,IAAI,CAACE,EAAE,CAAE;oBACpC8C,SAAS,EAAC,uEAAuE;oBACjFsC,KAAK,EAAC,0BAAM;oBAAA9B,QAAA,eAEZnF,OAAA,CAACV,QAAQ;sBAACqF,SAAS,EAAC;oBAAS;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CACT,eAED9E,OAAA;oBACEgG,OAAO,EAAEA,CAAA,KAAM9C,UAAU,CAACvB,IAAI,CAACE,EAAE,CAAE;oBACnC8C,SAAS,EAAC,mEAAmE;oBAC7EsC,KAAK,EAAC,0BAAM;oBAAA9B,QAAA,eAEZnF,OAAA,CAACP,MAAM;sBAACkF,SAAS,EAAC;oBAAS;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAtB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAnFDnD,IAAI,CAACE,EAAE;cAAA2B,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoFF,CACb;UAAC;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACa,CAAC,EAEjBzE,aAAa,CAACgG,MAAM,KAAK,CAAC,iBACzBrG,OAAA;YAAK2E,SAAS,EAAC,uDAAuD;YAAAQ,QAAA,eACpEnF,OAAA;cAAK2E,SAAS,EAAC,aAAa;cAAAQ,QAAA,gBAC1BnF,OAAA,CAACb,QAAQ;gBAACwF,SAAS,EAAC;cAAmC;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1D9E,OAAA;gBAAAmF,QAAA,EAAG;cAAM;gBAAA3B,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACb9E,OAAA;gBAAG2E,SAAS,EAAC,SAAS;gBAAAQ,QAAA,EAAC;cAAQ;gBAAA3B,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb9E,OAAA,CAACjB,MAAM,CAACqG,GAAG;QACTT,SAAS,EAAC,yBAAyB;QACnCU,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BY,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAjB,QAAA,EAE1BxE,kBAAkB,gBACjBX,OAAA,CAAAE,SAAA;UAAAiF,QAAA,GACGxE,kBAAkB,KAAK,WAAW,iBACjCX,OAAA,CAACL,YAAY;YACXuH,OAAO,EAAEtH,oBAAqB;YAC9BuH,QAAQ,EAAE,IAAK;YACfF,KAAK,EAAC,sCAAQ;YACdG,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;YACrBC,KAAK,EAAE;UAAI;YAAA7D,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACF,EACAnE,kBAAkB,KAAK,SAAS,iBAC/BX,OAAA,CAACL,YAAY;YACXuH,OAAO,EAAErH,kBAAmB;YAC5BsH,QAAQ,EAAE,IAAK;YACfF,KAAK,EAAC,sCAAQ;YACdG,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;YACrBC,KAAK,EAAE;UAAI;YAAA7D,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACF,EACAnE,kBAAkB,KAAK,UAAU,iBAChCX,OAAA,CAACL,YAAY;YACXuH,OAAO,EAAEpH,mBAAoB;YAC7BqH,QAAQ,EAAE,IAAK;YACfF,KAAK,EAAC,sCAAQ;YACdG,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;YACrBC,KAAK,EAAE;UAAG;YAAA7D,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CACF;QAAA,eACD,CAAC,gBAEH9E,OAAA;UAAK2E,SAAS,EAAC,uDAAuD;UAAAQ,QAAA,eACpEnF,OAAA;YAAK2E,SAAS,EAAC,aAAa;YAAAQ,QAAA,gBAC1BnF,OAAA,CAACN,QAAQ;cAACiF,SAAS,EAAC;YAAmC;cAAAnB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1D9E,OAAA;cAAAmF,QAAA,EAAG;YAAM;cAAA3B,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACb9E,OAAA;cAAG2E,SAAS,EAAC,SAAS;cAAAQ,QAAA,EAAC;YAAgB;cAAA3B,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAtB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN9E,OAAA,CAACjB,MAAM,CAACqG,GAAG;MACTT,SAAS,EAAC,eAAe;MACzBU,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BwB,UAAU,EAAE;QAAEO,KAAK,EAAE;MAAI,CAAE;MAAAnC,QAAA,eAE3BnF,OAAA;QAAK2E,SAAS,EAAC,kCAAkC;QAAAQ,QAAA,eAC/CnF,OAAA;UAAK2E,SAAS,EAAC,yBAAyB;UAAAQ,QAAA,gBACtCnF,OAAA;YACE2E,SAAS,EAAC,+BAA+B;YACzCqB,OAAO,EAAE/E,kBAAmB;YAAAkE,QAAA,gBAE5BnF,OAAA,CAACb,QAAQ;cAACwF,SAAS,EAAC;YAAc;cAAAnB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oDAEvC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAGRzE,aAAa,CAACkH,IAAI,CAAC/C,CAAC,IAAIA,CAAC,CAAC/B,MAAM,KAAK,WAAW,CAAC,iBAChDzC,OAAA,CAACjB,MAAM,CAACqG,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BZ,SAAS,EAAC,mDAAmD;YAAAQ,QAAA,gBAE7DnF,OAAA;cAAI2E,SAAS,EAAC,qDAAqD;cAAAQ,QAAA,gBACjEnF,OAAA,CAACZ,WAAW;gBAACuF,SAAS,EAAC;cAAc;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wCAE1C;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEL9E,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAQ,QAAA,GACvB9E,aAAa,CAAC8C,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAAC/B,MAAM,KAAK,WAAW,CAAC,CAACM,GAAG,CAACpB,IAAI,iBAC3D3B,OAAA;gBAAmB2E,SAAS,EAAC,iEAAiE;gBAAAQ,QAAA,gBAC5FnF,OAAA;kBAAM2E,SAAS,EAAC,eAAe;kBAAAQ,QAAA,EAAExD,IAAI,CAACO;gBAAQ;kBAAAsB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtD9E,OAAA;kBAAK2E,SAAS,EAAC,gBAAgB;kBAAAQ,QAAA,gBAC7BnF,OAAA;oBACEgG,OAAO,EAAEA,CAAA,KAAMzC,sBAAsB,CAAC5B,IAAI,CAACO,QAAQ,EAAE,OAAO,CAAE;oBAC9DyC,SAAS,EAAC,wFAAwF;oBAAAQ,QAAA,gBAElGnF,OAAA,CAACR,QAAQ;sBAACmF,SAAS,EAAC;oBAAqB;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,SAE9C;kBAAA;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT9E,OAAA;oBACEgG,OAAO,EAAEA,CAAA,KAAMzC,sBAAsB,CAAC5B,IAAI,CAACO,QAAQ,EAAE,KAAK,CAAE;oBAC5DyC,SAAS,EAAC,sFAAsF;oBAAAQ,QAAA,gBAEhGnF,OAAA,CAACR,QAAQ;sBAACmF,SAAS,EAAC;oBAAqB;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,OAE9C;kBAAA;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT9E,OAAA;oBACEgG,OAAO,EAAEA,CAAA,KAAMzC,sBAAsB,CAAC5B,IAAI,CAACO,QAAQ,EAAE,MAAM,CAAE;oBAC7DyC,SAAS,EAAC,0FAA0F;oBAAAQ,QAAA,gBAEpGnF,OAAA,CAACR,QAAQ;sBAACmF,SAAS,EAAC;oBAAqB;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,QAE9C;kBAAA;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GAxBEnD,IAAI,CAACE,EAAE;gBAAA2B,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyBZ,CACN,CAAC,eAEF9E,OAAA;gBACEgG,OAAO,EAAE1B,kBAAmB;gBAC5BK,SAAS,EAAC,6LAA6L;gBAAAQ,QAAA,gBAEvMnF,OAAA,CAACR,QAAQ;kBAACmF,SAAS,EAAC;gBAAc;kBAAAnB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sEAEvC;cAAA;gBAAAtB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAtB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAtB,QAAA,EAAAoB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC1E,EAAA,CAzcID,gBAA0B;EAAA,QAO8ElB,qBAAqB;AAAA;AAAAuI,EAAA,GAP7HrH,gBAA0B;AA2chC,eAAeA,gBAAgB;AAAC,IAAAqH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}