{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport ProcessFlow from './components/ProcessFlow';\nimport FileUploadSimple from './components/FileUploadSimple';\nimport Header from './components/Header';\nimport { ProcessFlowProvider, useProcessFlowContext } from './contexts/ProcessFlowContext';\n\n// 内部组件，用于访问Context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContent = () => {\n  _s();\n  const {\n    processState\n  } = useProcessFlowContext();\n\n  // 检查是否有任何步骤正在处理中\n  const isProcessing = processState.steps.some(step => step.status === 'active') || processState.overallProgress > 0 && processState.overallProgress < 100;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-tech-dark\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-tech-grid bg-tech-grid opacity-20 pointer-events-none\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        isProcessing: isProcessing\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 min-h-[calc(100vh-120px)]\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"lg:col-span-1 flex flex-col\",\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            children: /*#__PURE__*/_jsxDEV(ProcessFlow, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"lg:col-span-2 flex flex-col\",\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.2\n            },\n            children: /*#__PURE__*/_jsxDEV(FileUploadSimple, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n};\n_s(AppContent, \"ZTtg0zH22t11rUv1mhOdEAoX2Mc=\", false, function () {\n  return [useProcessFlowContext];\n});\n_c = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ProcessFlowProvider, {\n    children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "motion", "ProcessFlow", "FileUploadSimple", "Header", "ProcessFlowProvider", "useProcessFlowContext", "jsxDEV", "_jsxDEV", "A<PERSON><PERSON><PERSON>nt", "_s", "processState", "isProcessing", "steps", "some", "step", "status", "overallProgress", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "x", "animate", "transition", "duration", "delay", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport ProcessFlow from './components/ProcessFlow';\nimport FileUploadSimple from './components/FileUploadSimple';\nimport Header from './components/Header';\nimport { ProcessFlowProvider, useProcessFlowContext } from './contexts/ProcessFlowContext';\n\n// 内部组件，用于访问Context\nconst AppContent: React.FC = () => {\n  const { processState } = useProcessFlowContext();\n\n  // 检查是否有任何步骤正在处理中\n  const isProcessing = processState.steps.some(step =>\n    step.status === 'active'\n  ) || (processState.overallProgress > 0 && processState.overallProgress < 100);\n\n  return (\n    <div className=\"min-h-screen bg-tech-dark\">\n      {/* 背景网格效果 */}\n      <div className=\"fixed inset-0 bg-tech-grid bg-tech-grid opacity-20 pointer-events-none\" />\n\n      {/* 主要内容 */}\n      <div className=\"relative z-10\">\n        <Header isProcessing={isProcessing} />\n\n          <main className=\"container mx-auto px-4 py-8\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 min-h-[calc(100vh-120px)]\">\n              {/* 左侧流程展示区域 */}\n              <motion.div\n                className=\"lg:col-span-1 flex flex-col\"\n                initial={{ opacity: 0, x: -50 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.6 }}\n              >\n                <ProcessFlow />\n              </motion.div>\n\n              {/* 右侧文件上传和操作区域 */}\n              <motion.div\n                className=\"lg:col-span-2 flex flex-col\"\n                initial={{ opacity: 0, x: 50 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n              >\n                <FileUploadSimple />\n              </motion.div>\n            </div>\n          </main>\n        </div>\n      </div>\n  );\n};\n\nfunction App() {\n  return (\n    <ProcessFlowProvider>\n      <AppContent />\n    </ProcessFlowProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,mBAAmB,EAAEC,qBAAqB,QAAQ,+BAA+B;;AAE1F;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IAAEC;EAAa,CAAC,GAAGL,qBAAqB,CAAC,CAAC;;EAEhD;EACA,MAAMM,YAAY,GAAGD,YAAY,CAACE,KAAK,CAACC,IAAI,CAACC,IAAI,IAC/CA,IAAI,CAACC,MAAM,KAAK,QAClB,CAAC,IAAKL,YAAY,CAACM,eAAe,GAAG,CAAC,IAAIN,YAAY,CAACM,eAAe,GAAG,GAAI;EAE7E,oBACET,OAAA;IAAKU,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBAExCX,OAAA;MAAKU,SAAS,EAAC;IAAwE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG1Ff,OAAA;MAAKU,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BX,OAAA,CAACJ,MAAM;QAACQ,YAAY,EAAEA;MAAa;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEpCf,OAAA;QAAMU,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC3CX,OAAA;UAAKU,SAAS,EAAC,iEAAiE;UAAAC,QAAA,gBAE9EX,OAAA,CAACP,MAAM,CAACuB,GAAG;YACTN,SAAS,EAAC,6BAA6B;YACvCO,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAX,QAAA,eAE9BX,OAAA,CAACN,WAAW;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGbf,OAAA,CAACP,MAAM,CAACuB,GAAG;YACTN,SAAS,EAAC,6BAA6B;YACvCO,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAAAZ,QAAA,eAE1CX,OAAA,CAACL,gBAAgB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEZ,CAAC;AAACb,EAAA,CA3CID,UAAoB;EAAA,QACCH,qBAAqB;AAAA;AAAA0B,EAAA,GAD1CvB,UAAoB;AA6C1B,SAASwB,GAAGA,CAAA,EAAG;EACb,oBACEzB,OAAA,CAACH,mBAAmB;IAAAc,QAAA,eAClBX,OAAA,CAACC,UAAU;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAE1B;AAACW,GAAA,GANQD,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}