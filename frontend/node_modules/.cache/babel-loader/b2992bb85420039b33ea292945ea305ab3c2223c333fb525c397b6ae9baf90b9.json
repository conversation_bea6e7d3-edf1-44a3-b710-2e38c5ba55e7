{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useCallback } from 'react';\nconst initialSteps = [{\n  id: 'upload',\n  title: '文件上传',\n  description: '上传入户数据和投诉数据文件',\n  status: 'pending'\n}, {\n  id: 'validate',\n  title: '内容检测',\n  description: '验证文件格式和必要字段',\n  status: 'pending'\n}, {\n  id: 'configure',\n  title: '解析配置',\n  description: '配置智能解析规则',\n  status: 'pending'\n}, {\n  id: 'analyze',\n  title: '智能分析',\n  description: '提取转写文本并进行智能解析',\n  status: 'pending'\n}, {\n  id: 'generate',\n  title: '报表生成',\n  description: '生成分析报表和可视化图表',\n  status: 'pending'\n}, {\n  id: 'download',\n  title: '结果下载',\n  description: '下载分析结果和报表文件',\n  status: 'pending'\n}];\nexport const useProcessFlow = () => {\n  _s();\n  const [processState, setProcessState] = useState({\n    currentStep: '',\n    steps: initialSteps,\n    overallProgress: 0\n  });\n\n  // 激活某个步骤\n  const activateStep = useCallback(stepId => {\n    setProcessState(prev => ({\n      ...prev,\n      currentStep: stepId,\n      steps: prev.steps.map(step => ({\n        ...step,\n        status: step.id === stepId ? 'active' : step.status === 'active' ? 'pending' : step.status,\n        progress: step.id === stepId ? 0 : step.progress\n      }))\n    }));\n  }, []);\n\n  // 更新步骤进度\n  const updateStepProgress = useCallback((stepId, progress) => {\n    setProcessState(prev => ({\n      ...prev,\n      steps: prev.steps.map(step => step.id === stepId ? {\n        ...step,\n        progress\n      } : step)\n    }));\n  }, []);\n\n  // 完成某个步骤\n  const completeStep = useCallback(stepId => {\n    setProcessState(prev => {\n      const updatedSteps = prev.steps.map(step => step.id === stepId ? {\n        ...step,\n        status: 'completed',\n        progress: 100\n      } : step);\n      const completedCount = updatedSteps.filter(s => s.status === 'completed').length;\n      const overallProgress = completedCount / updatedSteps.length * 100;\n      return {\n        ...prev,\n        steps: updatedSteps,\n        overallProgress\n      };\n    });\n  }, []);\n\n  // 设置步骤错误\n  const setStepError = useCallback((stepId, errorMessage) => {\n    setProcessState(prev => ({\n      ...prev,\n      steps: prev.steps.map(step => step.id === stepId ? {\n        ...step,\n        status: 'error',\n        progress: 0\n      } : step)\n    }));\n  }, []);\n\n  // 重置所有步骤\n  const resetFlow = useCallback(() => {\n    setProcessState({\n      currentStep: '',\n      steps: initialSteps,\n      overallProgress: 0\n    });\n  }, []);\n\n  // 文件上传时的联动\n  const onFileUploaded = useCallback(() => {\n    activateStep('upload');\n    setTimeout(() => completeStep('upload'), 1000);\n  }, [activateStep, completeStep]);\n\n  // 文件验证时的联动\n  const onFileValidation = useCallback(() => {\n    activateStep('validate');\n    // 模拟验证过程\n    let progress = 0;\n    const interval = setInterval(() => {\n      progress += 20;\n      updateStepProgress('validate', progress);\n      if (progress >= 100) {\n        clearInterval(interval);\n        completeStep('validate');\n      }\n    }, 200);\n  }, [activateStep, updateStepProgress, completeStep]);\n\n  // 文件处理时的联动\n  const onFileProcessing = useCallback(() => {\n    activateStep('configure');\n    setTimeout(() => {\n      completeStep('configure');\n      activateStep('analyze');\n\n      // 模拟分析过程\n      let progress = 0;\n      const interval = setInterval(() => {\n        progress += 10;\n        updateStepProgress('analyze', progress);\n        if (progress >= 100) {\n          clearInterval(interval);\n          completeStep('analyze');\n        }\n      }, 300);\n    }, 1000);\n  }, [activateStep, completeStep, updateStepProgress]);\n\n  // 报表生成时的联动\n  const onReportGeneration = useCallback(() => {\n    activateStep('generate');\n    // 模拟报表生成过程\n    let progress = 0;\n    const interval = setInterval(() => {\n      progress += 15;\n      updateStepProgress('generate', progress);\n      if (progress >= 100) {\n        clearInterval(interval);\n        completeStep('generate');\n        activateStep('download');\n        setTimeout(() => completeStep('download'), 500);\n      }\n    }, 400);\n  }, [activateStep, updateStepProgress, completeStep]);\n  return {\n    processState,\n    activateStep,\n    updateStepProgress,\n    completeStep,\n    setStepError,\n    resetFlow,\n    // 联动方法\n    onFileUploaded,\n    onFileValidation,\n    onFileProcessing,\n    onReportGeneration\n  };\n};\n_s(useProcessFlow, \"62ZJuH65eHLHFJC2fSlnZXi+T/Q=\");", "map": {"version": 3, "names": ["useState", "useCallback", "initialSteps", "id", "title", "description", "status", "useProcessFlow", "_s", "processState", "setProcessState", "currentStep", "steps", "overallProgress", "activateStep", "stepId", "prev", "map", "step", "progress", "updateStepProgress", "completeStep", "updatedSteps", "completedCount", "filter", "s", "length", "setStepError", "errorMessage", "resetFlow", "onFileUploaded", "setTimeout", "onFileValidation", "interval", "setInterval", "clearInterval", "onFileProcessing", "onReportGeneration"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/hooks/useProcessFlow.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\n\nexport interface ProcessStep {\n  id: string;\n  title: string;\n  description: string;\n  status: 'pending' | 'active' | 'completed' | 'error';\n  progress?: number;\n}\n\nexport interface FileProgress {\n  fileId: string;\n  fileName: string;\n  currentStep: string;\n  stepProgress: number;\n}\n\nexport interface ProcessFlowState {\n  currentStep: string;\n  steps: ProcessStep[];\n  overallProgress: number;\n  fileProgresses: FileProgress[];\n}\n\nconst initialSteps: ProcessStep[] = [\n  {\n    id: 'upload',\n    title: '文件上传',\n    description: '上传入户数据和投诉数据文件',\n    status: 'pending'\n  },\n  {\n    id: 'validate',\n    title: '内容检测',\n    description: '验证文件格式和必要字段',\n    status: 'pending'\n  },\n  {\n    id: 'configure',\n    title: '解析配置',\n    description: '配置智能解析规则',\n    status: 'pending'\n  },\n  {\n    id: 'analyze',\n    title: '智能分析',\n    description: '提取转写文本并进行智能解析',\n    status: 'pending'\n  },\n  {\n    id: 'generate',\n    title: '报表生成',\n    description: '生成分析报表和可视化图表',\n    status: 'pending'\n  },\n  {\n    id: 'download',\n    title: '结果下载',\n    description: '下载分析结果和报表文件',\n    status: 'pending'\n  }\n];\n\nexport const useProcessFlow = () => {\n  const [processState, setProcessState] = useState<ProcessFlowState>({\n    currentStep: '',\n    steps: initialSteps,\n    overallProgress: 0\n  });\n\n  // 激活某个步骤\n  const activateStep = useCallback((stepId: string) => {\n    setProcessState(prev => ({\n      ...prev,\n      currentStep: stepId,\n      steps: prev.steps.map(step => ({\n        ...step,\n        status: step.id === stepId ? 'active' : \n                step.status === 'active' ? 'pending' : step.status,\n        progress: step.id === stepId ? 0 : step.progress\n      }))\n    }));\n  }, []);\n\n  // 更新步骤进度\n  const updateStepProgress = useCallback((stepId: string, progress: number) => {\n    setProcessState(prev => ({\n      ...prev,\n      steps: prev.steps.map(step => \n        step.id === stepId ? { ...step, progress } : step\n      )\n    }));\n  }, []);\n\n  // 完成某个步骤\n  const completeStep = useCallback((stepId: string) => {\n    setProcessState(prev => {\n      const updatedSteps = prev.steps.map(step => \n        step.id === stepId ? { ...step, status: 'completed' as const, progress: 100 } : step\n      );\n      \n      const completedCount = updatedSteps.filter(s => s.status === 'completed').length;\n      const overallProgress = (completedCount / updatedSteps.length) * 100;\n      \n      return {\n        ...prev,\n        steps: updatedSteps,\n        overallProgress\n      };\n    });\n  }, []);\n\n  // 设置步骤错误\n  const setStepError = useCallback((stepId: string, errorMessage?: string) => {\n    setProcessState(prev => ({\n      ...prev,\n      steps: prev.steps.map(step => \n        step.id === stepId ? { \n          ...step, \n          status: 'error' as const, \n          progress: 0 \n        } : step\n      )\n    }));\n  }, []);\n\n  // 重置所有步骤\n  const resetFlow = useCallback(() => {\n    setProcessState({\n      currentStep: '',\n      steps: initialSteps,\n      overallProgress: 0\n    });\n  }, []);\n\n  // 文件上传时的联动\n  const onFileUploaded = useCallback(() => {\n    activateStep('upload');\n    setTimeout(() => completeStep('upload'), 1000);\n  }, [activateStep, completeStep]);\n\n  // 文件验证时的联动\n  const onFileValidation = useCallback(() => {\n    activateStep('validate');\n    // 模拟验证过程\n    let progress = 0;\n    const interval = setInterval(() => {\n      progress += 20;\n      updateStepProgress('validate', progress);\n      if (progress >= 100) {\n        clearInterval(interval);\n        completeStep('validate');\n      }\n    }, 200);\n  }, [activateStep, updateStepProgress, completeStep]);\n\n  // 文件处理时的联动\n  const onFileProcessing = useCallback(() => {\n    activateStep('configure');\n    setTimeout(() => {\n      completeStep('configure');\n      activateStep('analyze');\n      \n      // 模拟分析过程\n      let progress = 0;\n      const interval = setInterval(() => {\n        progress += 10;\n        updateStepProgress('analyze', progress);\n        if (progress >= 100) {\n          clearInterval(interval);\n          completeStep('analyze');\n        }\n      }, 300);\n    }, 1000);\n  }, [activateStep, completeStep, updateStepProgress]);\n\n  // 报表生成时的联动\n  const onReportGeneration = useCallback(() => {\n    activateStep('generate');\n    // 模拟报表生成过程\n    let progress = 0;\n    const interval = setInterval(() => {\n      progress += 15;\n      updateStepProgress('generate', progress);\n      if (progress >= 100) {\n        clearInterval(interval);\n        completeStep('generate');\n        activateStep('download');\n        setTimeout(() => completeStep('download'), 500);\n      }\n    }, 400);\n  }, [activateStep, updateStepProgress, completeStep]);\n\n  return {\n    processState,\n    activateStep,\n    updateStepProgress,\n    completeStep,\n    setStepError,\n    resetFlow,\n    // 联动方法\n    onFileUploaded,\n    onFileValidation,\n    onFileProcessing,\n    onReportGeneration\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAwB7C,MAAMC,YAA2B,GAAG,CAClC;EACEC,EAAE,EAAE,QAAQ;EACZC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,eAAe;EAC5BC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,UAAU;EACdC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,aAAa;EAC1BC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,WAAW;EACfC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,UAAU;EACvBC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,SAAS;EACbC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,eAAe;EAC5BC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,UAAU;EACdC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,cAAc;EAC3BC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,UAAU;EACdC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,aAAa;EAC1BC,MAAM,EAAE;AACV,CAAC,CACF;AAED,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAmB;IACjEW,WAAW,EAAE,EAAE;IACfC,KAAK,EAAEV,YAAY;IACnBW,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACA,MAAMC,YAAY,GAAGb,WAAW,CAAEc,MAAc,IAAK;IACnDL,eAAe,CAACM,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPL,WAAW,EAAEI,MAAM;MACnBH,KAAK,EAAEI,IAAI,CAACJ,KAAK,CAACK,GAAG,CAACC,IAAI,KAAK;QAC7B,GAAGA,IAAI;QACPZ,MAAM,EAAEY,IAAI,CAACf,EAAE,KAAKY,MAAM,GAAG,QAAQ,GAC7BG,IAAI,CAACZ,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAGY,IAAI,CAACZ,MAAM;QAC1Da,QAAQ,EAAED,IAAI,CAACf,EAAE,KAAKY,MAAM,GAAG,CAAC,GAAGG,IAAI,CAACC;MAC1C,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,kBAAkB,GAAGnB,WAAW,CAAC,CAACc,MAAc,EAAEI,QAAgB,KAAK;IAC3ET,eAAe,CAACM,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPJ,KAAK,EAAEI,IAAI,CAACJ,KAAK,CAACK,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACf,EAAE,KAAKY,MAAM,GAAG;QAAE,GAAGG,IAAI;QAAEC;MAAS,CAAC,GAAGD,IAC/C;IACF,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,YAAY,GAAGpB,WAAW,CAAEc,MAAc,IAAK;IACnDL,eAAe,CAACM,IAAI,IAAI;MACtB,MAAMM,YAAY,GAAGN,IAAI,CAACJ,KAAK,CAACK,GAAG,CAACC,IAAI,IACtCA,IAAI,CAACf,EAAE,KAAKY,MAAM,GAAG;QAAE,GAAGG,IAAI;QAAEZ,MAAM,EAAE,WAAoB;QAAEa,QAAQ,EAAE;MAAI,CAAC,GAAGD,IAClF,CAAC;MAED,MAAMK,cAAc,GAAGD,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnB,MAAM,KAAK,WAAW,CAAC,CAACoB,MAAM;MAChF,MAAMb,eAAe,GAAIU,cAAc,GAAGD,YAAY,CAACI,MAAM,GAAI,GAAG;MAEpE,OAAO;QACL,GAAGV,IAAI;QACPJ,KAAK,EAAEU,YAAY;QACnBT;MACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMc,YAAY,GAAG1B,WAAW,CAAC,CAACc,MAAc,EAAEa,YAAqB,KAAK;IAC1ElB,eAAe,CAACM,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPJ,KAAK,EAAEI,IAAI,CAACJ,KAAK,CAACK,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACf,EAAE,KAAKY,MAAM,GAAG;QACnB,GAAGG,IAAI;QACPZ,MAAM,EAAE,OAAgB;QACxBa,QAAQ,EAAE;MACZ,CAAC,GAAGD,IACN;IACF,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,SAAS,GAAG5B,WAAW,CAAC,MAAM;IAClCS,eAAe,CAAC;MACdC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAEV,YAAY;MACnBW,eAAe,EAAE;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiB,cAAc,GAAG7B,WAAW,CAAC,MAAM;IACvCa,YAAY,CAAC,QAAQ,CAAC;IACtBiB,UAAU,CAAC,MAAMV,YAAY,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;EAChD,CAAC,EAAE,CAACP,YAAY,EAAEO,YAAY,CAAC,CAAC;;EAEhC;EACA,MAAMW,gBAAgB,GAAG/B,WAAW,CAAC,MAAM;IACzCa,YAAY,CAAC,UAAU,CAAC;IACxB;IACA,IAAIK,QAAQ,GAAG,CAAC;IAChB,MAAMc,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCf,QAAQ,IAAI,EAAE;MACdC,kBAAkB,CAAC,UAAU,EAAED,QAAQ,CAAC;MACxC,IAAIA,QAAQ,IAAI,GAAG,EAAE;QACnBgB,aAAa,CAACF,QAAQ,CAAC;QACvBZ,YAAY,CAAC,UAAU,CAAC;MAC1B;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,CAACP,YAAY,EAAEM,kBAAkB,EAAEC,YAAY,CAAC,CAAC;;EAEpD;EACA,MAAMe,gBAAgB,GAAGnC,WAAW,CAAC,MAAM;IACzCa,YAAY,CAAC,WAAW,CAAC;IACzBiB,UAAU,CAAC,MAAM;MACfV,YAAY,CAAC,WAAW,CAAC;MACzBP,YAAY,CAAC,SAAS,CAAC;;MAEvB;MACA,IAAIK,QAAQ,GAAG,CAAC;MAChB,MAAMc,QAAQ,GAAGC,WAAW,CAAC,MAAM;QACjCf,QAAQ,IAAI,EAAE;QACdC,kBAAkB,CAAC,SAAS,EAAED,QAAQ,CAAC;QACvC,IAAIA,QAAQ,IAAI,GAAG,EAAE;UACnBgB,aAAa,CAACF,QAAQ,CAAC;UACvBZ,YAAY,CAAC,SAAS,CAAC;QACzB;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAACP,YAAY,EAAEO,YAAY,EAAED,kBAAkB,CAAC,CAAC;;EAEpD;EACA,MAAMiB,kBAAkB,GAAGpC,WAAW,CAAC,MAAM;IAC3Ca,YAAY,CAAC,UAAU,CAAC;IACxB;IACA,IAAIK,QAAQ,GAAG,CAAC;IAChB,MAAMc,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCf,QAAQ,IAAI,EAAE;MACdC,kBAAkB,CAAC,UAAU,EAAED,QAAQ,CAAC;MACxC,IAAIA,QAAQ,IAAI,GAAG,EAAE;QACnBgB,aAAa,CAACF,QAAQ,CAAC;QACvBZ,YAAY,CAAC,UAAU,CAAC;QACxBP,YAAY,CAAC,UAAU,CAAC;QACxBiB,UAAU,CAAC,MAAMV,YAAY,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC;MACjD;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,CAACP,YAAY,EAAEM,kBAAkB,EAAEC,YAAY,CAAC,CAAC;EAEpD,OAAO;IACLZ,YAAY;IACZK,YAAY;IACZM,kBAAkB;IAClBC,YAAY;IACZM,YAAY;IACZE,SAAS;IACT;IACAC,cAAc;IACdE,gBAAgB;IAChBI,gBAAgB;IAChBC;EACF,CAAC;AACH,CAAC;AAAC7B,EAAA,CA/IWD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}