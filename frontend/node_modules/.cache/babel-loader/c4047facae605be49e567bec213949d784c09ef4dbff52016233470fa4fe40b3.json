{"ast": null, "code": "export const configureStageOutput = [\"🔧 **开始解析配置阶段**\", \"\", \"正在初始化智能解析引擎...\", \"✅ 解析引擎初始化完成\", \"\", \"📋 **检测文件结构**\", \"- 正在分析入户数据文件格式...\", \"  ✓ 检测到Excel格式文件\", \"  ✓ 发现数据表：住户信息、联系方式、地址信息\", \"  ✓ 数据行数：1,247条记录\", \"\", \"- 正在分析投诉数据文件格式...\", \"  ✓ 检测到CSV格式文件\", \"  ✓ 发现数据列：投诉时间、投诉内容、处理状态、客户ID\", \"  ✓ 数据行数：856条记录\", \"\", \"🎯 **配置解析规则**\", \"正在设置数据字段映射关系...\", \"- 客户ID字段映射：入户数据.住户编号 ↔ 投诉数据.客户ID\", \"- 时间字段标准化：统一为YYYY-MM-DD HH:mm:ss格式\", \"- 文本字段编码：统一转换为UTF-8编码\", \"\", \"🔍 **数据质量检查**\", \"- 检查缺失值...\", \"  ⚠️  发现23条记录存在空值，已标记待处理\", \"- 检查重复记录...\", \"  ✓ 未发现重复记录\", \"- 检查数据格式一致性...\", \"  ✓ 数据格式检查通过\", \"\", \"📊 **生成解析配置文件**\", \"```json\", \"{\", \"  \\\"dataMapping\\\": {\", \"    \\\"customerIdField\\\": \\\"住户编号\\\",\", \"    \\\"timeFormat\\\": \\\"YYYY-MM-DD HH:mm:ss\\\",\", \"    \\\"encoding\\\": \\\"UTF-8\\\"\", \"  },\", \"  \\\"qualityRules\\\": {\", \"    \\\"allowNullValues\\\": false,\", \"    \\\"duplicateHandling\\\": \\\"merge\\\"\", \"  }\", \"}\", \"```\", \"\", \"✅ **解析配置阶段完成**\", \"- 配置文件已生成\", \"- 数据映射关系已建立\", \"- 质量检查规则已设定\", \"\", \"🚀 准备进入智能分析阶段...\"];", "map": {"version": 3, "names": ["configureStageOutput"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/data/streamOutputs/configureStage.ts"], "sourcesContent": ["export const configureStageOutput = [\n  \"🔧 **开始解析配置阶段**\",\n  \"\",\n  \"正在初始化智能解析引擎...\",\n  \"✅ 解析引擎初始化完成\",\n  \"\",\n  \"📋 **检测文件结构**\",\n  \"- 正在分析入户数据文件格式...\",\n  \"  ✓ 检测到Excel格式文件\",\n  \"  ✓ 发现数据表：住户信息、联系方式、地址信息\",\n  \"  ✓ 数据行数：1,247条记录\",\n  \"\",\n  \"- 正在分析投诉数据文件格式...\",\n  \"  ✓ 检测到CSV格式文件\", \n  \"  ✓ 发现数据列：投诉时间、投诉内容、处理状态、客户ID\",\n  \"  ✓ 数据行数：856条记录\",\n  \"\",\n  \"🎯 **配置解析规则**\",\n  \"正在设置数据字段映射关系...\",\n  \"- 客户ID字段映射：入户数据.住户编号 ↔ 投诉数据.客户ID\",\n  \"- 时间字段标准化：统一为YYYY-MM-DD HH:mm:ss格式\",\n  \"- 文本字段编码：统一转换为UTF-8编码\",\n  \"\",\n  \"🔍 **数据质量检查**\",\n  \"- 检查缺失值...\",\n  \"  ⚠️  发现23条记录存在空值，已标记待处理\",\n  \"- 检查重复记录...\",\n  \"  ✓ 未发现重复记录\",\n  \"- 检查数据格式一致性...\",\n  \"  ✓ 数据格式检查通过\",\n  \"\",\n  \"📊 **生成解析配置文件**\",\n  \"```json\",\n  \"{\",\n  \"  \\\"dataMapping\\\": {\",\n  \"    \\\"customerIdField\\\": \\\"住户编号\\\",\",\n  \"    \\\"timeFormat\\\": \\\"YYYY-MM-DD HH:mm:ss\\\",\",\n  \"    \\\"encoding\\\": \\\"UTF-8\\\"\",\n  \"  },\",\n  \"  \\\"qualityRules\\\": {\",\n  \"    \\\"allowNullValues\\\": false,\",\n  \"    \\\"duplicateHandling\\\": \\\"merge\\\"\",\n  \"  }\",\n  \"}\",\n  \"```\",\n  \"\",\n  \"✅ **解析配置阶段完成**\",\n  \"- 配置文件已生成\",\n  \"- 数据映射关系已建立\",\n  \"- 质量检查规则已设定\",\n  \"\",\n  \"🚀 准备进入智能分析阶段...\"\n];\n"], "mappings": "AAAA,OAAO,MAAMA,oBAAoB,GAAG,CAClC,iBAAiB,EACjB,EAAE,EACF,gBAAgB,EAChB,aAAa,EACb,EAAE,EACF,eAAe,EACf,mBAAmB,EACnB,kBAAkB,EAClB,0BAA0B,EAC1B,mBAAmB,EACnB,EAAE,EACF,mBAAmB,EACnB,gBAAgB,EAChB,+BAA+B,EAC/B,iBAAiB,EACjB,EAAE,EACF,eAAe,EACf,iBAAiB,EACjB,kCAAkC,EAClC,oCAAoC,EACpC,uBAAuB,EACvB,EAAE,EACF,eAAe,EACf,YAAY,EACZ,0BAA0B,EAC1B,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,cAAc,EACd,EAAE,EACF,iBAAiB,EACjB,SAAS,EACT,GAAG,EACH,sBAAsB,EACtB,oCAAoC,EACpC,8CAA8C,EAC9C,6BAA6B,EAC7B,MAAM,EACN,uBAAuB,EACvB,iCAAiC,EACjC,sCAAsC,EACtC,KAAK,EACL,GAAG,EACH,KAAK,EACL,EAAE,EACF,gBAAgB,EAChB,WAAW,EACX,aAAa,EACb,aAAa,EACb,EAAE,EACF,kBAAkB,CACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}