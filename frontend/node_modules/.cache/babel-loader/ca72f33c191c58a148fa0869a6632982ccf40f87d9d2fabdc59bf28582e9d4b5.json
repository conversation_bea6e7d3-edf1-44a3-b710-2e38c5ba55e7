{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\components\\\\FileUploadSimple.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\nimport { Upload, FileText, CheckCircle, AlertCircle, Settings, Play, Download, Trash2, Eye, Zap } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FileUploadSimple = () => {\n  _s();\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [selectedMonth, setSelectedMonth] = useState('2024-01');\n  const [selectedFileType, setSelectedFileType] = useState('household');\n  const {\n    onFileUploaded,\n    onFileValidation,\n    onFileProcessing,\n    onReportGeneration\n  } = useProcessFlowContext();\n  const handleFileUpload = event => {\n    const files = event.target.files;\n    if (files) {\n      Array.from(files).forEach(file => {\n        const newFile = {\n          id: Math.random().toString(36).substr(2, 9),\n          filename: file.name,\n          file_type: selectedFileType,\n          month: selectedMonth,\n          status: 'uploaded',\n          upload_time: new Date().toISOString(),\n          file_path: `/uploads/${file.name}`\n        };\n        setUploadedFiles(prev => [...prev, newFile]);\n\n        // 触发流程联动\n        onFileUploaded();\n      });\n    }\n  };\n  const validateFile = fileId => {\n    setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n      ...file,\n      status: 'validated'\n    } : file));\n\n    // 触发验证流程联动\n    onFileValidation();\n  };\n  const processFile = fileId => {\n    setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n      ...file,\n      status: 'processing'\n    } : file));\n    setTimeout(() => {\n      setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n        ...file,\n        status: 'processed'\n      } : file));\n    }, 3000);\n  };\n  const deleteFile = fileId => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  };\n  const processAllFiles = () => {\n    setIsProcessing(true);\n    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');\n    validatedFiles.forEach((file, index) => {\n      setTimeout(() => {\n        processFile(file.id);\n      }, index * 1000);\n    });\n    setTimeout(() => {\n      setIsProcessing(false);\n    }, validatedFiles.length * 1000 + 2000);\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'uploaded':\n        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/30';\n      case 'validated':\n        return 'text-tech-green bg-tech-green/10 border-tech-green/30';\n      case 'processing':\n        return 'text-tech-cyan bg-tech-cyan/10 border-tech-cyan/30';\n      case 'processed':\n        return 'text-tech-purple bg-tech-purple/10 border-tech-purple/30';\n      case 'error':\n        return 'text-red-400 bg-red-400/10 border-red-400/30';\n      default:\n        return 'text-gray-400 bg-gray-400/10 border-gray-400/30';\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'uploaded':\n        return /*#__PURE__*/_jsxDEV(Upload, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 31\n        }, this);\n      case 'validated':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 32\n        }, this);\n      case 'processing':\n        return /*#__PURE__*/_jsxDEV(Settings, {\n          className: \"w-4 h-4 animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 33\n        }, this);\n      case 'processed':\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 32\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 28\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6 h-full flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-6\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-white\",\n          children: \"\\u6570\\u636E\\u6587\\u4EF6\\u4E0A\\u4F20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Zap, {\n            className: \"w-5 h-5 text-tech-cyan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-tech-cyan text-sm\",\n            children: \"\\u667A\\u80FD\\u5904\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300 mb-2\",\n            children: \"\\u6587\\u4EF6\\u7C7B\\u578B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedFileType,\n            onChange: e => setSelectedFileType(e.target.value),\n            className: \"tech-input w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"household\",\n              children: \"\\u5165\\u6237\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"complaint\",\n              children: \"\\u6295\\u8BC9\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300 mb-2\",\n            children: \"\\u6708\\u4EFD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"month\",\n            value: selectedMonth,\n            onChange: e => setSelectedMonth(e.target.value),\n            className: \"tech-input w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-2 border-dashed border-gray-600 hover:border-tech-cyan/50 rounded-lg p-8 text-center transition-all duration-300\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 bg-tech-cyan/20 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(Upload, {\n              className: \"w-8 h-8 text-tech-cyan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-medium text-white\",\n              children: \"\\u70B9\\u51FB\\u4E0A\\u4F20\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mt-1\",\n              children: \"\\u652F\\u6301 .xlsx, .xls \\u683C\\u5F0F\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            multiple: true,\n            accept: \".xlsx,.xls\",\n            onChange: handleFileUpload,\n            className: \"tech-button\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-6 flex-1 flex flex-col min-h-0\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.2\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-white\",\n          children: [\"\\u5DF2\\u4E0A\\u4F20\\u6587\\u4EF6 (\", uploadedFiles.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: processAllFiles,\n            disabled: isProcessing || uploadedFiles.filter(f => f.status === 'validated').length === 0,\n            className: \"tech-button text-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: [/*#__PURE__*/_jsxDEV(Play, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), \"\\u4E00\\u952E\\u5904\\u7406\\u5168\\u90E8\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3 flex-1 overflow-y-auto min-h-0\",\n        children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: uploadedFiles.map(file => /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"tech-border p-4 rounded-lg\",\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            exit: {\n              opacity: 0,\n              x: 20\n            },\n            layout: true,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-2 rounded-lg border ${getStatusColor(file.status)}`,\n                  children: getStatusIcon(file.status)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-white truncate\",\n                    children: file.filename\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: file.file_type === 'household' ? '入户数据' : '投诉数据'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: file.month\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-2 py-1 rounded-full text-xs ${getStatusColor(file.status)}`,\n                      children: [file.status === 'uploaded' && '已上传', file.status === 'validated' && '已验证', file.status === 'processing' && '处理中', file.status === 'processed' && '已处理', file.status === 'error' && '错误']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [file.status === 'uploaded' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => validateFile(file.id),\n                  className: \"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\",\n                  title: \"\\u9A8C\\u8BC1\\u6587\\u4EF6\",\n                  children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 23\n                }, this), file.status === 'validated' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => processFile(file.id),\n                  className: \"p-2 text-tech-cyan hover:bg-tech-cyan/10 rounded-lg transition-colors\",\n                  title: \"\\u667A\\u80FD\\u5904\\u7406\",\n                  children: /*#__PURE__*/_jsxDEV(Play, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 23\n                }, this), file.status === 'processed' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 text-tech-purple hover:bg-tech-purple/10 rounded-lg transition-colors\",\n                    title: \"\\u9884\\u89C8\\u7ED3\\u679C\",\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 285,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\",\n                    title: \"\\u4E0B\\u8F7D\\u7ED3\\u679C\",\n                    children: /*#__PURE__*/_jsxDEV(Download, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 291,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => deleteFile(file.id),\n                  className: \"p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors\",\n                  title: \"\\u5220\\u9664\\u6587\\u4EF6\",\n                  children: /*#__PURE__*/_jsxDEV(Trash2, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this), file.status === 'processing' && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"mt-3\",\n              initial: {\n                opacity: 0,\n                height: 0\n              },\n              animate: {\n                opacity: 1,\n                height: 'auto'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-700 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"progress-bar h-2 rounded-full\",\n                  initial: {\n                    width: 0\n                  },\n                  animate: {\n                    width: '100%'\n                  },\n                  transition: {\n                    duration: 3,\n                    ease: \"easeInOut\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-tech-cyan mt-1\",\n                children: \"\\u6B63\\u5728\\u8FDB\\u884C\\u667A\\u80FD\\u5206\\u6790...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 19\n            }, this)]\n          }, file.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), uploadedFiles.length === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"text-center py-12 text-gray-400\",\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u6682\\u65E0\\u4E0A\\u4F20\\u6587\\u4EF6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm mt-1\",\n            children: \"\\u8BF7\\u5148\\u4E0A\\u4F20\\u6570\\u636E\\u6587\\u4EF6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this), uploadedFiles.some(f => f.status === 'processed') && /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-6\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.4\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-white mb-4\",\n          children: \"\\u751F\\u6210\\u5206\\u6790\\u62A5\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"tech-button text-lg px-8 py-3\",\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"w-5 h-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this), \"\\u751F\\u6210\\u667A\\u80FD\\u5206\\u6790\\u62A5\\u8868\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 5\n  }, this);\n};\n_s(FileUploadSimple, \"Sqf42XJJ/rLKAYFYTKN2S7y6DQ0=\", false, function () {\n  return [useProcessFlowContext];\n});\n_c = FileUploadSimple;\nexport default FileUploadSimple;\nvar _c;\n$RefreshReg$(_c, \"FileUploadSimple\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "useProcessFlowContext", "Upload", "FileText", "CheckCircle", "AlertCircle", "Settings", "Play", "Download", "Trash2", "Eye", "Zap", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FileUploadSimple", "_s", "uploadedFiles", "setUploadedFiles", "isProcessing", "setIsProcessing", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMonth", "selectedFileType", "setSelectedFileType", "onFileUploaded", "onFileValidation", "onFileProcessing", "onReportGeneration", "handleFileUpload", "event", "files", "target", "Array", "from", "for<PERSON>ach", "file", "newFile", "id", "Math", "random", "toString", "substr", "filename", "name", "file_type", "month", "status", "upload_time", "Date", "toISOString", "file_path", "prev", "validateFile", "fileId", "map", "processFile", "setTimeout", "deleteFile", "filter", "processAllFiles", "validatedFiles", "index", "length", "getStatusColor", "getStatusIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "div", "initial", "opacity", "y", "animate", "value", "onChange", "e", "type", "multiple", "accept", "transition", "delay", "onClick", "disabled", "f", "x", "exit", "layout", "title", "height", "width", "duration", "ease", "some", "_c", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/components/FileUploadSimple.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\nimport { \n  Upload, \n  FileText, \n  CheckCircle, \n  AlertCircle, \n  Settings, \n  Play, \n  Download,\n  Trash2,\n  Eye,\n  Zap\n} from 'lucide-react';\n\ninterface UploadedFile {\n  id: string;\n  filename: string;\n  file_type: 'household' | 'complaint';\n  month: string;\n  status: 'uploaded' | 'validated' | 'processing' | 'processed' | 'error';\n  upload_time: string;\n  file_path: string;\n  error_message?: string;\n}\n\nconst FileUploadSimple: React.FC = () => {\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [selectedMonth, setSelectedMonth] = useState('2024-01');\n  const [selectedFileType, setSelectedFileType] = useState<'household' | 'complaint'>('household');\n\n  const { onFileUploaded, onFileValidation, onFileProcessing, onReportGeneration } = useProcessFlowContext();\n\n  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files;\n    if (files) {\n      Array.from(files).forEach(file => {\n        const newFile: UploadedFile = {\n          id: Math.random().toString(36).substr(2, 9),\n          filename: file.name,\n          file_type: selectedFileType,\n          month: selectedMonth,\n          status: 'uploaded',\n          upload_time: new Date().toISOString(),\n          file_path: `/uploads/${file.name}`\n        };\n        \n        setUploadedFiles(prev => [...prev, newFile]);\n\n        // 触发流程联动\n        onFileUploaded();\n      });\n    }\n  };\n\n  const validateFile = (fileId: string) => {\n    setUploadedFiles(prev =>\n      prev.map(file =>\n        file.id === fileId\n          ? { ...file, status: 'validated' }\n          : file\n      )\n    );\n\n    // 触发验证流程联动\n    onFileValidation();\n  };\n\n  const processFile = (fileId: string) => {\n    setUploadedFiles(prev => \n      prev.map(file => \n        file.id === fileId \n          ? { ...file, status: 'processing' }\n          : file\n      )\n    );\n\n    setTimeout(() => {\n      setUploadedFiles(prev => \n        prev.map(file => \n          file.id === fileId \n            ? { ...file, status: 'processed' }\n            : file\n        )\n      );\n    }, 3000);\n  };\n\n  const deleteFile = (fileId: string) => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  };\n\n  const processAllFiles = () => {\n    setIsProcessing(true);\n    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');\n    \n    validatedFiles.forEach((file, index) => {\n      setTimeout(() => {\n        processFile(file.id);\n      }, index * 1000);\n    });\n\n    setTimeout(() => {\n      setIsProcessing(false);\n    }, validatedFiles.length * 1000 + 2000);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'uploaded': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/30';\n      case 'validated': return 'text-tech-green bg-tech-green/10 border-tech-green/30';\n      case 'processing': return 'text-tech-cyan bg-tech-cyan/10 border-tech-cyan/30';\n      case 'processed': return 'text-tech-purple bg-tech-purple/10 border-tech-purple/30';\n      case 'error': return 'text-red-400 bg-red-400/10 border-red-400/30';\n      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/30';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'uploaded': return <Upload className=\"w-4 h-4\" />;\n      case 'validated': return <CheckCircle className=\"w-4 h-4\" />;\n      case 'processing': return <Settings className=\"w-4 h-4 animate-spin\" />;\n      case 'processed': return <FileText className=\"w-4 h-4\" />;\n      case 'error': return <AlertCircle className=\"w-4 h-4\" />;\n      default: return <FileText className=\"w-4 h-4\" />;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6 h-full flex flex-col\">\n      {/* 文件上传区域 */}\n      <motion.div\n        className=\"tech-card p-6\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-xl font-bold text-white\">数据文件上传</h2>\n          <div className=\"flex items-center space-x-2\">\n            <Zap className=\"w-5 h-5 text-tech-cyan\" />\n            <span className=\"text-tech-cyan text-sm\">智能处理</span>\n          </div>\n        </div>\n\n        {/* 文件类型和月份选择 */}\n        <div className=\"grid grid-cols-2 gap-4 mb-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n              文件类型\n            </label>\n            <select\n              value={selectedFileType}\n              onChange={(e) => setSelectedFileType(e.target.value as 'household' | 'complaint')}\n              className=\"tech-input w-full\"\n            >\n              <option value=\"household\">入户数据</option>\n              <option value=\"complaint\">投诉数据</option>\n            </select>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n              月份\n            </label>\n            <input\n              type=\"month\"\n              value={selectedMonth}\n              onChange={(e) => setSelectedMonth(e.target.value)}\n              className=\"tech-input w-full\"\n            />\n          </div>\n        </div>\n\n        {/* 文件上传区域 */}\n        <div className=\"border-2 border-dashed border-gray-600 hover:border-tech-cyan/50 rounded-lg p-8 text-center transition-all duration-300\">\n          <div className=\"flex flex-col items-center space-y-4\">\n            <div className=\"p-4 bg-tech-cyan/20 rounded-full\">\n              <Upload className=\"w-8 h-8 text-tech-cyan\" />\n            </div>\n            <div>\n              <p className=\"text-lg font-medium text-white\">\n                点击上传文件\n              </p>\n              <p className=\"text-sm text-gray-400 mt-1\">\n                支持 .xlsx, .xls 格式文件\n              </p>\n            </div>\n            <input\n              type=\"file\"\n              multiple\n              accept=\".xlsx,.xls\"\n              onChange={handleFileUpload}\n              className=\"tech-button\"\n            />\n          </div>\n        </div>\n      </motion.div>\n\n      {/* 已上传文件列表 */}\n      <motion.div\n        className=\"tech-card p-6 flex-1 flex flex-col min-h-0\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.2 }}\n      >\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-white\">\n            已上传文件 ({uploadedFiles.length})\n          </h3>\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={processAllFiles}\n              disabled={isProcessing || uploadedFiles.filter(f => f.status === 'validated').length === 0}\n              className=\"tech-button text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <Play className=\"w-4 h-4 mr-2\" />\n              一键处理全部\n            </button>\n          </div>\n        </div>\n\n        <div className=\"space-y-3 flex-1 overflow-y-auto min-h-0\">\n          <AnimatePresence>\n            {uploadedFiles.map((file) => (\n              <motion.div\n                key={file.id}\n                className=\"tech-border p-4 rounded-lg\"\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: 20 }}\n                layout\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3 flex-1\">\n                    <div className={`p-2 rounded-lg border ${getStatusColor(file.status)}`}>\n                      {getStatusIcon(file.status)}\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <h4 className=\"font-medium text-white truncate\">\n                        {file.filename}\n                      </h4>\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-400\">\n                        <span>{file.file_type === 'household' ? '入户数据' : '投诉数据'}</span>\n                        <span>{file.month}</span>\n                        <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(file.status)}`}>\n                          {file.status === 'uploaded' && '已上传'}\n                          {file.status === 'validated' && '已验证'}\n                          {file.status === 'processing' && '处理中'}\n                          {file.status === 'processed' && '已处理'}\n                          {file.status === 'error' && '错误'}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2\">\n                    {file.status === 'uploaded' && (\n                      <button\n                        onClick={() => validateFile(file.id)}\n                        className=\"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\"\n                        title=\"验证文件\"\n                      >\n                        <CheckCircle className=\"w-4 h-4\" />\n                      </button>\n                    )}\n\n                    {file.status === 'validated' && (\n                      <button\n                        onClick={() => processFile(file.id)}\n                        className=\"p-2 text-tech-cyan hover:bg-tech-cyan/10 rounded-lg transition-colors\"\n                        title=\"智能处理\"\n                      >\n                        <Play className=\"w-4 h-4\" />\n                      </button>\n                    )}\n\n                    {file.status === 'processed' && (\n                      <>\n                        <button\n                          className=\"p-2 text-tech-purple hover:bg-tech-purple/10 rounded-lg transition-colors\"\n                          title=\"预览结果\"\n                        >\n                          <Eye className=\"w-4 h-4\" />\n                        </button>\n                        <button\n                          className=\"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\"\n                          title=\"下载结果\"\n                        >\n                          <Download className=\"w-4 h-4\" />\n                        </button>\n                      </>\n                    )}\n\n                    <button\n                      onClick={() => deleteFile(file.id)}\n                      className=\"p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors\"\n                      title=\"删除文件\"\n                    >\n                      <Trash2 className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                </div>\n\n                {file.status === 'processing' && (\n                  <motion.div\n                    className=\"mt-3\"\n                    initial={{ opacity: 0, height: 0 }}\n                    animate={{ opacity: 1, height: 'auto' }}\n                  >\n                    <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                      <motion.div\n                        className=\"progress-bar h-2 rounded-full\"\n                        initial={{ width: 0 }}\n                        animate={{ width: '100%' }}\n                        transition={{ duration: 3, ease: \"easeInOut\" }}\n                      />\n                    </div>\n                    <p className=\"text-xs text-tech-cyan mt-1\">正在进行智能分析...</p>\n                  </motion.div>\n                )}\n              </motion.div>\n            ))}\n          </AnimatePresence>\n\n          {uploadedFiles.length === 0 && (\n            <motion.div\n              className=\"text-center py-12 text-gray-400\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n            >\n              <FileText className=\"w-12 h-12 mx-auto mb-4 opacity-50\" />\n              <p>暂无上传文件</p>\n              <p className=\"text-sm mt-1\">请先上传数据文件</p>\n            </motion.div>\n          )}\n        </div>\n      </motion.div>\n\n      {/* 生成报表按钮 */}\n      {uploadedFiles.some(f => f.status === 'processed') && (\n        <motion.div\n          className=\"tech-card p-6\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n        >\n          <div className=\"text-center\">\n            <h3 className=\"text-lg font-semibold text-white mb-4\">\n              生成分析报表\n            </h3>\n            <button className=\"tech-button text-lg px-8 py-3\">\n              <FileText className=\"w-5 h-5 mr-2\" />\n              生成智能分析报表\n            </button>\n          </div>\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\nexport default FileUploadSimple;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SACEC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,WAAW,EACXC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,GAAG,EACHC,GAAG,QACE,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAatB,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAA4B,WAAW,CAAC;EAEhG,MAAM;IAAE4B,cAAc;IAAEC,gBAAgB;IAAEC,gBAAgB;IAAEC;EAAmB,CAAC,GAAG5B,qBAAqB,CAAC,CAAC;EAE1G,MAAM6B,gBAAgB,GAAIC,KAA0C,IAAK;IACvE,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,EAAE;MACTE,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,OAAO,CAACC,IAAI,IAAI;QAChC,MAAMC,OAAqB,GAAG;UAC5BC,EAAE,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;UAC3CC,QAAQ,EAAEP,IAAI,CAACQ,IAAI;UACnBC,SAAS,EAAEtB,gBAAgB;UAC3BuB,KAAK,EAAEzB,aAAa;UACpB0B,MAAM,EAAE,UAAU;UAClBC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACrCC,SAAS,EAAE,YAAYf,IAAI,CAACQ,IAAI;QAClC,CAAC;QAED1B,gBAAgB,CAACkC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEf,OAAO,CAAC,CAAC;;QAE5C;QACAZ,cAAc,CAAC,CAAC;MAClB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM4B,YAAY,GAAIC,MAAc,IAAK;IACvCpC,gBAAgB,CAACkC,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACnB,IAAI,IACXA,IAAI,CAACE,EAAE,KAAKgB,MAAM,GACd;MAAE,GAAGlB,IAAI;MAAEW,MAAM,EAAE;IAAY,CAAC,GAChCX,IACN,CACF,CAAC;;IAED;IACAV,gBAAgB,CAAC,CAAC;EACpB,CAAC;EAED,MAAM8B,WAAW,GAAIF,MAAc,IAAK;IACtCpC,gBAAgB,CAACkC,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACnB,IAAI,IACXA,IAAI,CAACE,EAAE,KAAKgB,MAAM,GACd;MAAE,GAAGlB,IAAI;MAAEW,MAAM,EAAE;IAAa,CAAC,GACjCX,IACN,CACF,CAAC;IAEDqB,UAAU,CAAC,MAAM;MACfvC,gBAAgB,CAACkC,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACnB,IAAI,IACXA,IAAI,CAACE,EAAE,KAAKgB,MAAM,GACd;QAAE,GAAGlB,IAAI;QAAEW,MAAM,EAAE;MAAY,CAAC,GAChCX,IACN,CACF,CAAC;IACH,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMsB,UAAU,GAAIJ,MAAc,IAAK;IACrCpC,gBAAgB,CAACkC,IAAI,IAAIA,IAAI,CAACO,MAAM,CAACvB,IAAI,IAAIA,IAAI,CAACE,EAAE,KAAKgB,MAAM,CAAC,CAAC;EACnE,CAAC;EAED,MAAMM,eAAe,GAAGA,CAAA,KAAM;IAC5BxC,eAAe,CAAC,IAAI,CAAC;IACrB,MAAMyC,cAAc,GAAG5C,aAAa,CAAC0C,MAAM,CAACvB,IAAI,IAAIA,IAAI,CAACW,MAAM,KAAK,WAAW,CAAC;IAEhFc,cAAc,CAAC1B,OAAO,CAAC,CAACC,IAAI,EAAE0B,KAAK,KAAK;MACtCL,UAAU,CAAC,MAAM;QACfD,WAAW,CAACpB,IAAI,CAACE,EAAE,CAAC;MACtB,CAAC,EAAEwB,KAAK,GAAG,IAAI,CAAC;IAClB,CAAC,CAAC;IAEFL,UAAU,CAAC,MAAM;MACfrC,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,EAAEyC,cAAc,CAACE,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;EACzC,CAAC;EAED,MAAMC,cAAc,GAAIjB,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,UAAU;QAAE,OAAO,uDAAuD;MAC/E,KAAK,WAAW;QAAE,OAAO,uDAAuD;MAChF,KAAK,YAAY;QAAE,OAAO,oDAAoD;MAC9E,KAAK,WAAW;QAAE,OAAO,0DAA0D;MACnF,KAAK,OAAO;QAAE,OAAO,8CAA8C;MACnE;QAAS,OAAO,iDAAiD;IACnE;EACF,CAAC;EAED,MAAMkB,aAAa,GAAIlB,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,UAAU;QAAE,oBAAOnC,OAAA,CAACX,MAAM;UAACiE,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,WAAW;QAAE,oBAAO1D,OAAA,CAACT,WAAW;UAAC+D,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5D,KAAK,YAAY;QAAE,oBAAO1D,OAAA,CAACP,QAAQ;UAAC6D,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvE,KAAK,WAAW;QAAE,oBAAO1D,OAAA,CAACV,QAAQ;UAACgE,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD,KAAK,OAAO;QAAE,oBAAO1D,OAAA,CAACR,WAAW;UAAC8D,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxD;QAAS,oBAAO1D,OAAA,CAACV,QAAQ;UAACgE,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClD;EACF,CAAC;EAED,oBACE1D,OAAA;IAAKsD,SAAS,EAAC,gCAAgC;IAAAK,QAAA,gBAE7C3D,OAAA,CAACd,MAAM,CAAC0E,GAAG;MACTN,SAAS,EAAC,eAAe;MACzBO,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBAE9B3D,OAAA;QAAKsD,SAAS,EAAC,wCAAwC;QAAAK,QAAA,gBACrD3D,OAAA;UAAIsD,SAAS,EAAC,8BAA8B;UAAAK,QAAA,EAAC;QAAM;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxD1D,OAAA;UAAKsD,SAAS,EAAC,6BAA6B;UAAAK,QAAA,gBAC1C3D,OAAA,CAACF,GAAG;YAACwD,SAAS,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1C1D,OAAA;YAAMsD,SAAS,EAAC,wBAAwB;YAAAK,QAAA,EAAC;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1D,OAAA;QAAKsD,SAAS,EAAC,6BAA6B;QAAAK,QAAA,gBAC1C3D,OAAA;UAAA2D,QAAA,gBACE3D,OAAA;YAAOsD,SAAS,EAAC,8CAA8C;YAAAK,QAAA,EAAC;UAEhE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR1D,OAAA;YACEiE,KAAK,EAAEtD,gBAAiB;YACxBuD,QAAQ,EAAGC,CAAC,IAAKvD,mBAAmB,CAACuD,CAAC,CAAC/C,MAAM,CAAC6C,KAAkC,CAAE;YAClFX,SAAS,EAAC,mBAAmB;YAAAK,QAAA,gBAE7B3D,OAAA;cAAQiE,KAAK,EAAC,WAAW;cAAAN,QAAA,EAAC;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvC1D,OAAA;cAAQiE,KAAK,EAAC,WAAW;cAAAN,QAAA,EAAC;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN1D,OAAA;UAAA2D,QAAA,gBACE3D,OAAA;YAAOsD,SAAS,EAAC,8CAA8C;YAAAK,QAAA,EAAC;UAEhE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR1D,OAAA;YACEoE,IAAI,EAAC,OAAO;YACZH,KAAK,EAAExD,aAAc;YACrByD,QAAQ,EAAGC,CAAC,IAAKzD,gBAAgB,CAACyD,CAAC,CAAC/C,MAAM,CAAC6C,KAAK,CAAE;YAClDX,SAAS,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1D,OAAA;QAAKsD,SAAS,EAAC,yHAAyH;QAAAK,QAAA,eACtI3D,OAAA;UAAKsD,SAAS,EAAC,sCAAsC;UAAAK,QAAA,gBACnD3D,OAAA;YAAKsD,SAAS,EAAC,kCAAkC;YAAAK,QAAA,eAC/C3D,OAAA,CAACX,MAAM;cAACiE,SAAS,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACN1D,OAAA;YAAA2D,QAAA,gBACE3D,OAAA;cAAGsD,SAAS,EAAC,gCAAgC;cAAAK,QAAA,EAAC;YAE9C;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ1D,OAAA;cAAGsD,SAAS,EAAC,4BAA4B;cAAAK,QAAA,EAAC;YAE1C;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN1D,OAAA;YACEoE,IAAI,EAAC,MAAM;YACXC,QAAQ;YACRC,MAAM,EAAC,YAAY;YACnBJ,QAAQ,EAAEjD,gBAAiB;YAC3BqC,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGb1D,OAAA,CAACd,MAAM,CAAC0E,GAAG;MACTN,SAAS,EAAC,4CAA4C;MACtDO,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BQ,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAb,QAAA,gBAE3B3D,OAAA;QAAKsD,SAAS,EAAC,wCAAwC;QAAAK,QAAA,gBACrD3D,OAAA;UAAIsD,SAAS,EAAC,kCAAkC;UAAAK,QAAA,GAAC,kCACxC,EAACtD,aAAa,CAAC8C,MAAM,EAAC,GAC/B;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1D,OAAA;UAAKsD,SAAS,EAAC,gBAAgB;UAAAK,QAAA,eAC7B3D,OAAA;YACEyE,OAAO,EAAEzB,eAAgB;YACzB0B,QAAQ,EAAEnE,YAAY,IAAIF,aAAa,CAAC0C,MAAM,CAAC4B,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,WAAW,CAAC,CAACgB,MAAM,KAAK,CAAE;YAC3FG,SAAS,EAAC,qEAAqE;YAAAK,QAAA,gBAE/E3D,OAAA,CAACN,IAAI;cAAC4D,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wCAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1D,OAAA;QAAKsD,SAAS,EAAC,0CAA0C;QAAAK,QAAA,gBACvD3D,OAAA,CAACb,eAAe;UAAAwE,QAAA,EACbtD,aAAa,CAACsC,GAAG,CAAEnB,IAAI,iBACtBxB,OAAA,CAACd,MAAM,CAAC0E,GAAG;YAETN,SAAS,EAAC,4BAA4B;YACtCO,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEc,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCZ,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEc,CAAC,EAAE;YAAE,CAAE;YAC9BC,IAAI,EAAE;cAAEf,OAAO,EAAE,CAAC;cAAEc,CAAC,EAAE;YAAG,CAAE;YAC5BE,MAAM;YAAAnB,QAAA,gBAEN3D,OAAA;cAAKsD,SAAS,EAAC,mCAAmC;cAAAK,QAAA,gBAChD3D,OAAA;gBAAKsD,SAAS,EAAC,oCAAoC;gBAAAK,QAAA,gBACjD3D,OAAA;kBAAKsD,SAAS,EAAE,yBAAyBF,cAAc,CAAC5B,IAAI,CAACW,MAAM,CAAC,EAAG;kBAAAwB,QAAA,EACpEN,aAAa,CAAC7B,IAAI,CAACW,MAAM;gBAAC;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACN1D,OAAA;kBAAKsD,SAAS,EAAC,gBAAgB;kBAAAK,QAAA,gBAC7B3D,OAAA;oBAAIsD,SAAS,EAAC,iCAAiC;oBAAAK,QAAA,EAC5CnC,IAAI,CAACO;kBAAQ;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACL1D,OAAA;oBAAKsD,SAAS,EAAC,mDAAmD;oBAAAK,QAAA,gBAChE3D,OAAA;sBAAA2D,QAAA,EAAOnC,IAAI,CAACS,SAAS,KAAK,WAAW,GAAG,MAAM,GAAG;oBAAM;sBAAAsB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC/D1D,OAAA;sBAAA2D,QAAA,EAAOnC,IAAI,CAACU;oBAAK;sBAAAqB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzB1D,OAAA;sBAAMsD,SAAS,EAAE,kCAAkCF,cAAc,CAAC5B,IAAI,CAACW,MAAM,CAAC,EAAG;sBAAAwB,QAAA,GAC9EnC,IAAI,CAACW,MAAM,KAAK,UAAU,IAAI,KAAK,EACnCX,IAAI,CAACW,MAAM,KAAK,WAAW,IAAI,KAAK,EACpCX,IAAI,CAACW,MAAM,KAAK,YAAY,IAAI,KAAK,EACrCX,IAAI,CAACW,MAAM,KAAK,WAAW,IAAI,KAAK,EACpCX,IAAI,CAACW,MAAM,KAAK,OAAO,IAAI,IAAI;oBAAA;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1D,OAAA;gBAAKsD,SAAS,EAAC,6BAA6B;gBAAAK,QAAA,GACzCnC,IAAI,CAACW,MAAM,KAAK,UAAU,iBACzBnC,OAAA;kBACEyE,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAACjB,IAAI,CAACE,EAAE,CAAE;kBACrC4B,SAAS,EAAC,yEAAyE;kBACnFyB,KAAK,EAAC,0BAAM;kBAAApB,QAAA,eAEZ3D,OAAA,CAACT,WAAW;oBAAC+D,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CACT,EAEAlC,IAAI,CAACW,MAAM,KAAK,WAAW,iBAC1BnC,OAAA;kBACEyE,OAAO,EAAEA,CAAA,KAAM7B,WAAW,CAACpB,IAAI,CAACE,EAAE,CAAE;kBACpC4B,SAAS,EAAC,uEAAuE;kBACjFyB,KAAK,EAAC,0BAAM;kBAAApB,QAAA,eAEZ3D,OAAA,CAACN,IAAI;oBAAC4D,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CACT,EAEAlC,IAAI,CAACW,MAAM,KAAK,WAAW,iBAC1BnC,OAAA,CAAAE,SAAA;kBAAAyD,QAAA,gBACE3D,OAAA;oBACEsD,SAAS,EAAC,2EAA2E;oBACrFyB,KAAK,EAAC,0BAAM;oBAAApB,QAAA,eAEZ3D,OAAA,CAACH,GAAG;sBAACyD,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACT1D,OAAA;oBACEsD,SAAS,EAAC,yEAAyE;oBACnFyB,KAAK,EAAC,0BAAM;oBAAApB,QAAA,eAEZ3D,OAAA,CAACL,QAAQ;sBAAC2D,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA,eACT,CACH,eAED1D,OAAA;kBACEyE,OAAO,EAAEA,CAAA,KAAM3B,UAAU,CAACtB,IAAI,CAACE,EAAE,CAAE;kBACnC4B,SAAS,EAAC,mEAAmE;kBAC7EyB,KAAK,EAAC,0BAAM;kBAAApB,QAAA,eAEZ3D,OAAA,CAACJ,MAAM;oBAAC0D,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELlC,IAAI,CAACW,MAAM,KAAK,YAAY,iBAC3BnC,OAAA,CAACd,MAAM,CAAC0E,GAAG;cACTN,SAAS,EAAC,MAAM;cAChBO,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEkB,MAAM,EAAE;cAAE,CAAE;cACnChB,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEkB,MAAM,EAAE;cAAO,CAAE;cAAArB,QAAA,gBAExC3D,OAAA;gBAAKsD,SAAS,EAAC,qCAAqC;gBAAAK,QAAA,eAClD3D,OAAA,CAACd,MAAM,CAAC0E,GAAG;kBACTN,SAAS,EAAC,+BAA+B;kBACzCO,OAAO,EAAE;oBAAEoB,KAAK,EAAE;kBAAE,CAAE;kBACtBjB,OAAO,EAAE;oBAAEiB,KAAK,EAAE;kBAAO,CAAE;kBAC3BV,UAAU,EAAE;oBAAEW,QAAQ,EAAE,CAAC;oBAAEC,IAAI,EAAE;kBAAY;gBAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1D,OAAA;gBAAGsD,SAAS,EAAC,6BAA6B;gBAAAK,QAAA,EAAC;cAAW;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CACb;UAAA,GA9FIlC,IAAI,CAACE,EAAE;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+FF,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa,CAAC,EAEjBrD,aAAa,CAAC8C,MAAM,KAAK,CAAC,iBACzBnD,OAAA,CAACd,MAAM,CAAC0E,GAAG;UACTN,SAAS,EAAC,iCAAiC;UAC3CO,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UAAAH,QAAA,gBAExB3D,OAAA,CAACV,QAAQ;YAACgE,SAAS,EAAC;UAAmC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1D1D,OAAA;YAAA2D,QAAA,EAAG;UAAM;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACb1D,OAAA;YAAGsD,SAAS,EAAC,cAAc;YAAAK,QAAA,EAAC;UAAQ;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGZrD,aAAa,CAAC+E,IAAI,CAACT,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,WAAW,CAAC,iBAChDnC,OAAA,CAACd,MAAM,CAAC0E,GAAG;MACTN,SAAS,EAAC,eAAe;MACzBO,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BQ,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAb,QAAA,eAE3B3D,OAAA;QAAKsD,SAAS,EAAC,aAAa;QAAAK,QAAA,gBAC1B3D,OAAA;UAAIsD,SAAS,EAAC,uCAAuC;UAAAK,QAAA,EAAC;QAEtD;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1D,OAAA;UAAQsD,SAAS,EAAC,+BAA+B;UAAAK,QAAA,gBAC/C3D,OAAA,CAACV,QAAQ;YAACgE,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oDAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtD,EAAA,CA9UID,gBAA0B;EAAA,QAMqDf,qBAAqB;AAAA;AAAAiG,EAAA,GANpGlF,gBAA0B;AAgVhC,eAAeA,gBAAgB;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}