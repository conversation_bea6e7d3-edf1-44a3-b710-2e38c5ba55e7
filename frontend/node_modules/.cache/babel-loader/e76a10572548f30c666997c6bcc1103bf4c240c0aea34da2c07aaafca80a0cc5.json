{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\components\\\\FileUploadSimple.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\nimport * as XLSX from 'xlsx';\nimport { Upload, FileText, CheckCircle, Settings, Play, Trash2, Terminal, Eye, X } from 'lucide-react';\nimport { StreamOutput } from './StreamOutput';\nimport { configureStageOutput } from '../data/streamOutputs/configureStage';\nimport { analyzeStageOutput } from '../data/streamOutputs/analyzeStage';\nimport { generateStageOutput } from '../data/streamOutputs/generateStage';\nimport { eventEmitter, EVENTS } from '../utils/eventEmitter';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FileUploadSimple = () => {\n  _s();\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  // 使用Context中的智能分析状态\n  // const [currentStreamStage, setCurrentStreamStage] = useState<'configure' | 'analyze' | 'generate' | null>(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [showPreview, setShowPreview] = useState(false);\n  const fileInputRef = useRef(null);\n  const {\n    onFileUploaded,\n    onFileValidation,\n    onFileProcessing,\n    onReportGeneration,\n    resetFlow,\n    processState,\n    analysisStage,\n    setAnalysisStage\n  } = useProcessFlowContext();\n\n  // 监听全局重置事件\n  useEffect(() => {\n    const handleResetAll = () => {\n      setUploadedFiles([]);\n      setIsProcessing(false);\n      setAnalysisStage(null);\n    };\n    eventEmitter.on(EVENTS.RESET_ALL, handleResetAll);\n    return () => {\n      eventEmitter.off(EVENTS.RESET_ALL, handleResetAll);\n    };\n  }, [setAnalysisStage]);\n  const handleFileUpload = async event => {\n    const files = event.target.files;\n    if (files) {\n      for (const file of Array.from(files)) {\n        const newFile = {\n          id: Math.random().toString(36).substr(2, 9),\n          filename: file.name,\n          file_type: file.type || 'text/plain',\n          size: file.size,\n          status: 'uploaded',\n          upload_time: new Date().toISOString(),\n          file_path: `/uploads/${file.name}`\n        };\n\n        // 根据文件类型读取内容用于预览\n        try {\n          const isExcelFile = file.name.toLowerCase().endsWith('.xlsx') || file.name.toLowerCase().endsWith('.xls') || file.name.toLowerCase().endsWith('.csv');\n          if (isExcelFile) {\n            const excelData = await readExcelFile(file);\n            newFile.excel_data = excelData.data;\n            newFile.excel_headers = excelData.headers;\n          } else {\n            const content = await readFileContent(file);\n            newFile.preview_content = content.substring(0, 1000); // 只保存前1000字符用于预览\n          }\n        } catch (error) {\n          console.error('读取文件内容失败:', error);\n        }\n        setUploadedFiles(prev => [...prev, newFile]);\n        onFileUploaded(newFile.id, newFile.filename);\n      }\n    }\n  };\n\n  // 读取文件内容的辅助函数\n  const readFileContent = file => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = e => {\n        var _e$target;\n        resolve((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n      };\n      reader.onerror = reject;\n      reader.readAsText(file, 'UTF-8');\n    });\n  };\n\n  // 读取Excel文件的辅助函数\n  const readExcelFile = file => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = e => {\n        try {\n          var _e$target2;\n          const data = new Uint8Array((_e$target2 = e.target) === null || _e$target2 === void 0 ? void 0 : _e$target2.result);\n          const workbook = XLSX.read(data, {\n            type: 'array'\n          });\n          const sheetName = workbook.SheetNames[0];\n          const worksheet = workbook.Sheets[sheetName];\n          const jsonData = XLSX.utils.sheet_to_json(worksheet, {\n            header: 1\n          });\n          if (jsonData.length > 0) {\n            const headers = jsonData[0];\n            const data = jsonData.slice(1);\n            resolve({\n              data: data.slice(0, 50),\n              headers\n            }); // 只取前50行用于预览\n          } else {\n            resolve({\n              data: [],\n              headers: []\n            });\n          }\n        } catch (error) {\n          reject(error);\n        }\n      };\n      reader.onerror = reject;\n      reader.readAsArrayBuffer(file);\n    });\n  };\n  const validateFile = fileId => {\n    setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n      ...file,\n      status: 'validated'\n    } : file));\n    onFileValidation(fileId);\n  };\n  const processFile = fileId => {\n    setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n      ...file,\n      status: 'processing'\n    } : file));\n    onFileProcessing(fileId);\n\n    // 模拟处理完成\n    setTimeout(() => {\n      setUploadedFiles(prev => prev.map(file => file.id === fileId ? {\n        ...file,\n        status: 'processed'\n      } : file));\n    }, 5000);\n  };\n  const deleteFile = fileId => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  };\n\n  // 预览文件\n  const previewFile = file => {\n    setSelectedFile(file);\n    setShowPreview(true);\n  };\n  const processAllFiles = () => {\n    setIsProcessing(true);\n    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');\n\n    // 开始流式输出 - 解析配置阶段\n    setAnalysisStage('configure');\n\n    // 模拟处理流程\n    setTimeout(() => {\n      // 解析配置完成，开始智能分析\n      setAnalysisStage('analyze');\n\n      // 同时开始处理文件\n      validatedFiles.forEach((file, index) => {\n        setTimeout(() => {\n          processFile(file.id);\n        }, index * 1000);\n      });\n      setTimeout(() => {\n        // 智能分析完成，直接开始报表生成（不需要用户点击）\n        setAnalysisStage('generate');\n        onReportGeneration(); // 自动触发报表生成\n\n        setTimeout(() => {\n          // 所有处理完成\n          setIsProcessing(false);\n          setAnalysisStage(null);\n        }, 8000); // 报表生成阶段时间\n      }, 6000); // 智能分析阶段时间\n    }, 4000); // 解析配置阶段时间\n  };\n\n  // 下载功能已移至ProcessFlow组件\n\n  // 获取文件进度\n  const getFileProgress = fileId => {\n    return processState.fileProgresses.find(fp => fp.fileId === fileId);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6 h-full flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"tech-card p-4\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-white\",\n          children: \"\\u6570\\u636E\\u6587\\u4EF6\\u4E0A\\u4F20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 max-w-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              ref: fileInputRef,\n              type: \"file\",\n              multiple: true,\n              accept: \".txt,.doc,.docx,.pdf,.xlsx,.xls,.csv\",\n              onChange: handleFileUpload,\n              className: \"hidden\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var _fileInputRef$current;\n                return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n              },\n              className: \"tech-button w-full px-6 py-3 text-base font-medium\",\n              children: [/*#__PURE__*/_jsxDEV(Upload, {\n                className: \"w-5 h-5 mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), \"\\u9009\\u62E9\\u6587\\u672C\\u6587\\u4EF6\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"tech-card flex flex-col\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        style: {\n          height: '400px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-4 border-b border-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold text-white\",\n            children: [\"\\u5DF2\\u4E0A\\u4F20\\u6587\\u4EF6 (\", uploadedFiles.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), uploadedFiles.filter(f => f.status === 'validated').length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: processAllFiles,\n            disabled: isProcessing,\n            className: \"tech-button text-sm px-4 py-2 disabled:opacity-50\",\n            children: [/*#__PURE__*/_jsxDEV(Play, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this), isProcessing ? '处理中...' : '一键处理全部']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 overflow-y-auto p-4\",\n          children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n            children: uploadedFiles.map(file => /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"bg-gray-800/50 rounded-lg p-4 mb-3 border border-gray-700\",\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              exit: {\n                opacity: 0,\n                y: -20\n              },\n              layout: true,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(FileText, {\n                      className: \"w-5 h-5 text-tech-cyan\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 283,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-medium text-white\",\n                        children: file.filename\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 285,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-400\",\n                        children: [(file.size / 1024).toFixed(1), \" KB \\u2022 \", new Date(file.upload_time).toLocaleString()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 286,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 284,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `px-2 py-1 rounded text-xs ${file.status === 'uploaded' ? 'bg-blue-900/50 text-blue-300' : file.status === 'validated' ? 'bg-green-900/50 text-green-300' : file.status === 'processing' ? 'bg-yellow-900/50 text-yellow-300' : file.status === 'processed' ? 'bg-purple-900/50 text-purple-300' : 'bg-red-900/50 text-red-300'}`,\n                    children: file.status === 'uploaded' ? '已上传' : file.status === 'validated' ? '已验证' : file.status === 'processing' ? '处理中' : file.status === 'processed' ? '已完成' : '错误'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => previewFile(file),\n                    className: \"p-2 text-tech-purple hover:bg-tech-purple/10 rounded-lg transition-colors\",\n                    title: \"\\u5728\\u7EBF\\u9884\\u89C8\",\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 23\n                  }, this), file.status === 'uploaded' && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => validateFile(file.id),\n                    className: \"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\",\n                    title: \"\\u9A8C\\u8BC1\\u6587\\u4EF6\",\n                    children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 25\n                  }, this), file.status === 'validated' && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => processFile(file.id),\n                    className: \"p-2 text-tech-cyan hover:bg-tech-cyan/10 rounded-lg transition-colors\",\n                    title: \"\\u667A\\u80FD\\u5904\\u7406\",\n                    children: /*#__PURE__*/_jsxDEV(Settings, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => deleteFile(file.id),\n                    className: \"p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors\",\n                    title: \"\\u5220\\u9664\\u6587\\u4EF6\",\n                    children: /*#__PURE__*/_jsxDEV(Trash2, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this), (() => {\n                const fileProgress = getFileProgress(file.id);\n                if (fileProgress && fileProgress.stepProgress > 0) {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between text-xs text-gray-400 mb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [fileProgress.currentStep === 'configure' ? '配置中' : fileProgress.currentStep === 'analyze' ? '分析中' : fileProgress.currentStep === 'generate' ? '生成中' : '处理中', \"...\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 354,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [fileProgress.stepProgress, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 357,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-full bg-gray-700 rounded-full h-1.5\",\n                      children: /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"progress-bar h-1.5 rounded-full\",\n                        initial: {\n                          width: 0\n                        },\n                        animate: {\n                          width: `${fileProgress.stepProgress}%`\n                        },\n                        transition: {\n                          duration: 0.3\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 360,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 359,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 25\n                  }, this);\n                }\n                return null;\n              })()]\n            }, file.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), uploadedFiles.length === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"text-center py-12 text-gray-400\",\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(FileText, {\n              className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u6682\\u65E0\\u4E0A\\u4F20\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm mt-1\",\n              children: \"\\u8BF7\\u5148\\u4E0A\\u4F20\\u6570\\u636E\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"tech-card flex flex-col\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        style: {\n          height: '400px'\n        },\n        children: analysisStage ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [analysisStage === 'configure' && /*#__PURE__*/_jsxDEV(StreamOutput, {\n            content: configureStageOutput,\n            isActive: true,\n            title: \"\\u89E3\\u6790\\u914D\\u7F6E\\u9636\\u6BB5\",\n            onComplete: () => {},\n            speed: 100\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 17\n          }, this), analysisStage === 'analyze' && /*#__PURE__*/_jsxDEV(StreamOutput, {\n            content: analyzeStageOutput,\n            isActive: true,\n            title: \"\\u667A\\u80FD\\u5206\\u6790\\u9636\\u6BB5\",\n            onComplete: () => {},\n            speed: 120\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 17\n          }, this), analysisStage === 'generate' && /*#__PURE__*/_jsxDEV(StreamOutput, {\n            content: generateStageOutput,\n            isActive: true,\n            title: \"\\u62A5\\u8868\\u751F\\u6210\\u9636\\u6BB5\",\n            onComplete: () => {},\n            speed: 80\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 flex items-center justify-center text-gray-400\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(Terminal, {\n              className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"AI\\u5904\\u7406\\u8F93\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm\",\n              children: \"\\u70B9\\u51FB\\\"\\u4E00\\u952E\\u5904\\u7406\\u5168\\u90E8\\\"\\u5F00\\u59CB\\u667A\\u80FD\\u5206\\u6790\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: showPreview && selectedFile && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        exit: {\n          opacity: 0\n        },\n        onClick: () => setShowPreview(false),\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"tech-card max-w-4xl w-full max-h-[80vh] flex flex-col\",\n          initial: {\n            scale: 0.9,\n            opacity: 0\n          },\n          animate: {\n            scale: 1,\n            opacity: 1\n          },\n          exit: {\n            scale: 0.9,\n            opacity: 0\n          },\n          onClick: e => e.stopPropagation(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-4 border-b border-gray-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-white\",\n                children: selectedFile.filename\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-400\",\n                children: [(selectedFile.size / 1024).toFixed(1), \" KB \\u2022 \", new Date(selectedFile.upload_time).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowPreview(false),\n              className: \"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(X, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 overflow-auto p-4\",\n            children: selectedFile.excel_data && selectedFile.excel_headers ?\n            /*#__PURE__*/\n            // Excel表格预览\n            _jsxDEV(\"div\", {\n              className: \"bg-gray-900/50 rounded-lg overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"px-4 py-3 bg-gray-800/50 border-b border-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-300 font-medium\",\n                    children: \"\\uD83D\\uDCCA Excel\\u8868\\u683C\\u9884\\u89C8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-400\",\n                    children: [\"\\u663E\\u793A\\u524D50\\u884C \\xB7 \", selectedFile.excel_headers.length, \"\\u5217\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"overflow-auto max-h-96\",\n                style: {\n                  maxWidth: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"w-full text-sm border-collapse\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    className: \"sticky top-0 bg-gray-800 z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: selectedFile.excel_headers.map((header, index) => /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"px-4 py-3 text-left text-gray-200 font-medium border-r border-gray-600 last:border-r-0 whitespace-nowrap min-w-[120px] bg-gray-800\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center space-x-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\"#\", index + 1]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 497,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: header || `列${index + 1}`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 498,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 496,\n                          columnNumber: 33\n                        }, this)\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 492,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: selectedFile.excel_data.map((row, rowIndex) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: `hover:bg-gray-700/30 transition-colors ${rowIndex % 2 === 0 ? 'bg-gray-800/20' : 'bg-gray-800/10'}`,\n                      children: selectedFile.excel_headers.map((_, colIndex) => {\n                        var _row$colIndex;\n                        return /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"px-4 py-2 text-gray-300 border-r border-gray-700/50 last:border-r-0 whitespace-nowrap min-w-[120px]\",\n                          title: ((_row$colIndex = row[colIndex]) === null || _row$colIndex === void 0 ? void 0 : _row$colIndex.toString()) || '',\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"max-w-[200px] truncate\",\n                            children: row[colIndex] || '-'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 518,\n                            columnNumber: 35\n                          }, this)\n                        }, colIndex, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 513,\n                          columnNumber: 33\n                        }, this);\n                      })\n                    }, rowIndex, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"px-4 py-2 bg-gray-800/30 border-t border-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-400 flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\uD83D\\uDCA1 \\u63D0\\u793A\\uFF1A\\u8868\\u683C\\u652F\\u6301\\u6A2A\\u5411\\u548C\\u7EB5\\u5411\\u6EDA\\u52A8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"\\u6570\\u636E\\u884C\\u6570\\uFF1A\", selectedFile.excel_data.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 19\n            }, this) :\n            /*#__PURE__*/\n            // 文本文件预览\n            _jsxDEV(\"div\", {\n              className: \"bg-gray-900/50 rounded-lg p-4 font-mono text-sm text-gray-300 whitespace-pre-wrap\",\n              children: selectedFile.preview_content || '无法预览此文件内容'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 212,\n    columnNumber: 5\n  }, this);\n};\n_s(FileUploadSimple, \"xxKwq7n5QPqbPbmsgXSYVZcQyQc=\", false, function () {\n  return [useProcessFlowContext];\n});\n_c = FileUploadSimple;\nexport default FileUploadSimple;\nvar _c;\n$RefreshReg$(_c, \"FileUploadSimple\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "AnimatePresence", "useProcessFlowContext", "XLSX", "Upload", "FileText", "CheckCircle", "Settings", "Play", "Trash2", "Terminal", "Eye", "X", "StreamOutput", "configureStageOutput", "analyzeStageOutput", "generateStageOutput", "eventEmitter", "EVENTS", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FileUploadSimple", "_s", "uploadedFiles", "setUploadedFiles", "isProcessing", "setIsProcessing", "selectedFile", "setSelectedFile", "showPreview", "setShowPreview", "fileInputRef", "onFileUploaded", "onFileValidation", "onFileProcessing", "onReportGeneration", "resetFlow", "processState", "analysisStage", "setAnalysisStage", "handleResetAll", "on", "RESET_ALL", "off", "handleFileUpload", "event", "files", "target", "file", "Array", "from", "newFile", "id", "Math", "random", "toString", "substr", "filename", "name", "file_type", "type", "size", "status", "upload_time", "Date", "toISOString", "file_path", "isExcelFile", "toLowerCase", "endsWith", "excelData", "readExcelFile", "excel_data", "data", "excel_headers", "headers", "content", "readFileContent", "preview_content", "substring", "error", "console", "prev", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "e", "_e$target", "result", "onerror", "readAsText", "_e$target2", "Uint8Array", "workbook", "read", "sheetName", "SheetNames", "worksheet", "Sheets", "jsonData", "utils", "sheet_to_json", "header", "length", "slice", "readAsA<PERSON>y<PERSON><PERSON>er", "validateFile", "fileId", "map", "processFile", "setTimeout", "deleteFile", "filter", "previewFile", "processAllFiles", "validatedFiles", "for<PERSON>ach", "index", "getFileProgress", "fileProgresses", "find", "fp", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "multiple", "accept", "onChange", "onClick", "_fileInputRef$current", "current", "click", "style", "height", "f", "disabled", "exit", "layout", "toFixed", "toLocaleString", "title", "fileProgress", "stepProgress", "currentStep", "width", "transition", "duration", "isActive", "onComplete", "speed", "scale", "stopPropagation", "max<PERSON><PERSON><PERSON>", "row", "rowIndex", "_", "colIndex", "_row$colIndex", "_c", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/components/FileUploadSimple.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\nimport * as XLSX from 'xlsx';\nimport {\n  Upload,\n  FileText,\n  CheckCircle,\n  Settings,\n  Play,\n  Trash2,\n  Terminal,\n  Eye,\n  X\n} from 'lucide-react';\nimport { StreamOutput } from './StreamOutput';\nimport { configureStageOutput } from '../data/streamOutputs/configureStage';\nimport { analyzeStageOutput } from '../data/streamOutputs/analyzeStage';\nimport { generateStageOutput } from '../data/streamOutputs/generateStage';\nimport { eventEmitter, EVENTS } from '../utils/eventEmitter';\n\ninterface UploadedFile {\n  id: string;\n  filename: string;\n  file_type: string;\n  size: number;\n  status: 'uploaded' | 'validated' | 'processing' | 'processed' | 'error';\n  upload_time: string;\n  file_path: string;\n  error_message?: string;\n  preview_content?: string;\n  excel_data?: any[][]; // Excel表格数据\n  excel_headers?: string[]; // Excel表头\n}\n\nconst FileUploadSimple: React.FC = () => {\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  // 使用Context中的智能分析状态\n  // const [currentStreamStage, setCurrentStreamStage] = useState<'configure' | 'analyze' | 'generate' | null>(null);\n  const [selectedFile, setSelectedFile] = useState<UploadedFile | null>(null);\n  const [showPreview, setShowPreview] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const { onFileUploaded, onFileValidation, onFileProcessing, onReportGeneration, resetFlow, processState, analysisStage, setAnalysisStage } = useProcessFlowContext();\n\n  // 监听全局重置事件\n  useEffect(() => {\n    const handleResetAll = () => {\n      setUploadedFiles([]);\n      setIsProcessing(false);\n      setAnalysisStage(null);\n    };\n\n    eventEmitter.on(EVENTS.RESET_ALL, handleResetAll);\n\n    return () => {\n      eventEmitter.off(EVENTS.RESET_ALL, handleResetAll);\n    };\n  }, [setAnalysisStage]);\n\n  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files;\n    if (files) {\n      for (const file of Array.from(files)) {\n        const newFile: UploadedFile = {\n          id: Math.random().toString(36).substr(2, 9),\n          filename: file.name,\n          file_type: file.type || 'text/plain',\n          size: file.size,\n          status: 'uploaded',\n          upload_time: new Date().toISOString(),\n          file_path: `/uploads/${file.name}`\n        };\n\n        // 根据文件类型读取内容用于预览\n        try {\n          const isExcelFile = file.name.toLowerCase().endsWith('.xlsx') ||\n                             file.name.toLowerCase().endsWith('.xls') ||\n                             file.name.toLowerCase().endsWith('.csv');\n\n          if (isExcelFile) {\n            const excelData = await readExcelFile(file);\n            newFile.excel_data = excelData.data;\n            newFile.excel_headers = excelData.headers;\n          } else {\n            const content = await readFileContent(file);\n            newFile.preview_content = content.substring(0, 1000); // 只保存前1000字符用于预览\n          }\n        } catch (error) {\n          console.error('读取文件内容失败:', error);\n        }\n\n        setUploadedFiles(prev => [...prev, newFile]);\n        onFileUploaded(newFile.id, newFile.filename);\n      }\n    }\n  };\n\n  // 读取文件内容的辅助函数\n  const readFileContent = (file: File): Promise<string> => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        resolve(e.target?.result as string);\n      };\n      reader.onerror = reject;\n      reader.readAsText(file, 'UTF-8');\n    });\n  };\n\n  // 读取Excel文件的辅助函数\n  const readExcelFile = (file: File): Promise<{ data: any[][], headers: string[] }> => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        try {\n          const data = new Uint8Array(e.target?.result as ArrayBuffer);\n          const workbook = XLSX.read(data, { type: 'array' });\n          const sheetName = workbook.SheetNames[0];\n          const worksheet = workbook.Sheets[sheetName];\n          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });\n\n          if (jsonData.length > 0) {\n            const headers = jsonData[0] as string[];\n            const data = jsonData.slice(1) as any[][];\n            resolve({ data: data.slice(0, 50), headers }); // 只取前50行用于预览\n          } else {\n            resolve({ data: [], headers: [] });\n          }\n        } catch (error) {\n          reject(error);\n        }\n      };\n      reader.onerror = reject;\n      reader.readAsArrayBuffer(file);\n    });\n  };\n\n  const validateFile = (fileId: string) => {\n    setUploadedFiles(prev => prev.map(file => \n      file.id === fileId ? { ...file, status: 'validated' } : file\n    ));\n    onFileValidation(fileId);\n  };\n\n  const processFile = (fileId: string) => {\n    setUploadedFiles(prev => prev.map(file => \n      file.id === fileId ? { ...file, status: 'processing' } : file\n    ));\n    onFileProcessing(fileId);\n    \n    // 模拟处理完成\n    setTimeout(() => {\n      setUploadedFiles(prev => prev.map(file => \n        file.id === fileId ? { ...file, status: 'processed' } : file\n      ));\n    }, 5000);\n  };\n\n  const deleteFile = (fileId: string) => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  };\n\n  // 预览文件\n  const previewFile = (file: UploadedFile) => {\n    setSelectedFile(file);\n    setShowPreview(true);\n  };\n\n  const processAllFiles = () => {\n    setIsProcessing(true);\n    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');\n    \n    // 开始流式输出 - 解析配置阶段\n    setAnalysisStage('configure');\n    \n    // 模拟处理流程\n    setTimeout(() => {\n      // 解析配置完成，开始智能分析\n      setAnalysisStage('analyze');\n      \n      // 同时开始处理文件\n      validatedFiles.forEach((file, index) => {\n        setTimeout(() => {\n          processFile(file.id);\n        }, index * 1000);\n      });\n      \n      setTimeout(() => {\n        // 智能分析完成，直接开始报表生成（不需要用户点击）\n        setAnalysisStage('generate');\n        onReportGeneration(); // 自动触发报表生成\n        \n        setTimeout(() => {\n          // 所有处理完成\n          setIsProcessing(false);\n          setAnalysisStage(null);\n        }, 8000); // 报表生成阶段时间\n      }, 6000); // 智能分析阶段时间\n    }, 4000); // 解析配置阶段时间\n  };\n\n  // 下载功能已移至ProcessFlow组件\n\n  // 获取文件进度\n  const getFileProgress = (fileId: string) => {\n    return processState.fileProgresses.find(fp => fp.fileId === fileId);\n  };\n\n  return (\n    <div className=\"space-y-6 h-full flex flex-col\">\n      {/* 顶部控制区域 */}\n      <motion.div\n        className=\"tech-card p-4\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        <div className=\"flex items-center justify-between\">\n          <h2 className=\"text-xl font-bold text-white\">数据文件上传</h2>\n          \n          {/* 文件上传控制 */}\n          <div className=\"flex items-center space-x-4\">\n            {/* 文件选择器 - 扩展长度 */}\n            <div className=\"flex-1 max-w-md\">\n              <input\n                ref={fileInputRef}\n                type=\"file\"\n                multiple\n                accept=\".txt,.doc,.docx,.pdf,.xlsx,.xls,.csv\"\n                onChange={handleFileUpload}\n                className=\"hidden\"\n              />\n              <button\n                onClick={() => fileInputRef.current?.click()}\n                className=\"tech-button w-full px-6 py-3 text-base font-medium\"\n              >\n                <Upload className=\"w-5 h-5 mr-3\" />\n                选择文本文件\n              </button>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* 主要内容区域 */}\n      <div className=\"flex-1 space-y-6\">\n        {/* 已上传文件列表 - 横向扩充 */}\n        <motion.div\n          className=\"tech-card flex flex-col\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          style={{ height: '400px' }}\n        >\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-700\">\n            <h3 className=\"font-semibold text-white\">已上传文件 ({uploadedFiles.length})</h3>\n            {uploadedFiles.filter(f => f.status === 'validated').length > 0 && (\n              <button\n                onClick={processAllFiles}\n                disabled={isProcessing}\n                className=\"tech-button text-sm px-4 py-2 disabled:opacity-50\"\n              >\n                <Play className=\"w-4 h-4 mr-2\" />\n                {isProcessing ? '处理中...' : '一键处理全部'}\n              </button>\n            )}\n          </div>\n          \n          <div className=\"flex-1 overflow-y-auto p-4\">\n            <AnimatePresence>\n              {uploadedFiles.map((file) => (\n                <motion.div\n                  key={file.id}\n                  className=\"bg-gray-800/50 rounded-lg p-4 mb-3 border border-gray-700\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: -20 }}\n                  layout\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-3\">\n                        <FileText className=\"w-5 h-5 text-tech-cyan\" />\n                        <div>\n                          <h4 className=\"font-medium text-white\">{file.filename}</h4>\n                          <p className=\"text-sm text-gray-400\">\n                            {(file.size / 1024).toFixed(1)} KB • {new Date(file.upload_time).toLocaleString()}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-2\">\n                      {/* 状态指示器 */}\n                      <div className={`px-2 py-1 rounded text-xs ${\n                        file.status === 'uploaded' ? 'bg-blue-900/50 text-blue-300' :\n                        file.status === 'validated' ? 'bg-green-900/50 text-green-300' :\n                        file.status === 'processing' ? 'bg-yellow-900/50 text-yellow-300' :\n                        file.status === 'processed' ? 'bg-purple-900/50 text-purple-300' :\n                        'bg-red-900/50 text-red-300'\n                      }`}>\n                        {file.status === 'uploaded' ? '已上传' :\n                         file.status === 'validated' ? '已验证' :\n                         file.status === 'processing' ? '处理中' :\n                         file.status === 'processed' ? '已完成' : '错误'}\n                      </div>\n                      \n                      {/* 操作按钮 */}\n                      <button\n                        onClick={() => previewFile(file)}\n                        className=\"p-2 text-tech-purple hover:bg-tech-purple/10 rounded-lg transition-colors\"\n                        title=\"在线预览\"\n                      >\n                        <Eye className=\"w-4 h-4\" />\n                      </button>\n\n                      {file.status === 'uploaded' && (\n                        <button\n                          onClick={() => validateFile(file.id)}\n                          className=\"p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors\"\n                          title=\"验证文件\"\n                        >\n                          <CheckCircle className=\"w-4 h-4\" />\n                        </button>\n                      )}\n\n                      {file.status === 'validated' && (\n                        <button\n                          onClick={() => processFile(file.id)}\n                          className=\"p-2 text-tech-cyan hover:bg-tech-cyan/10 rounded-lg transition-colors\"\n                          title=\"智能处理\"\n                        >\n                          <Settings className=\"w-4 h-4\" />\n                        </button>\n                      )}\n\n                      <button\n                        onClick={() => deleteFile(file.id)}\n                        className=\"p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors\"\n                        title=\"删除文件\"\n                      >\n                        <Trash2 className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </div>\n                  \n                  {/* 文件级进度显示 */}\n                  {(() => {\n                    const fileProgress = getFileProgress(file.id);\n                    if (fileProgress && fileProgress.stepProgress > 0) {\n                      return (\n                        <div className=\"mt-2\">\n                          <div className=\"flex items-center justify-between text-xs text-gray-400 mb-1\">\n                            <span>{fileProgress.currentStep === 'configure' ? '配置中' : \n                                   fileProgress.currentStep === 'analyze' ? '分析中' : \n                                   fileProgress.currentStep === 'generate' ? '生成中' : '处理中'}...</span>\n                            <span>{fileProgress.stepProgress}%</span>\n                          </div>\n                          <div className=\"w-full bg-gray-700 rounded-full h-1.5\">\n                            <motion.div\n                              className=\"progress-bar h-1.5 rounded-full\"\n                              initial={{ width: 0 }}\n                              animate={{ width: `${fileProgress.stepProgress}%` }}\n                              transition={{ duration: 0.3 }}\n                            />\n                          </div>\n                        </div>\n                      );\n                    }\n                    return null;\n                  })()}\n                </motion.div>\n              ))}\n            </AnimatePresence>\n\n            {uploadedFiles.length === 0 && (\n              <motion.div\n                className=\"text-center py-12 text-gray-400\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n              >\n                <FileText className=\"w-12 h-12 mx-auto mb-4 opacity-50\" />\n                <p>暂无上传文件</p>\n                <p className=\"text-sm mt-1\">请先上传数据文件</p>\n              </motion.div>\n            )}\n          </div>\n        </motion.div>\n\n        {/* AI处理输出区域 - 移至原生成智能分析报表位置 */}\n        <motion.div\n          className=\"tech-card flex flex-col\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          style={{ height: '400px' }}\n        >\n          {analysisStage ? (\n            <>\n              {analysisStage === 'configure' && (\n                <StreamOutput\n                  content={configureStageOutput}\n                  isActive={true}\n                  title=\"解析配置阶段\"\n                  onComplete={() => {}}\n                  speed={100}\n                />\n              )}\n              {analysisStage === 'analyze' && (\n                <StreamOutput\n                  content={analyzeStageOutput}\n                  isActive={true}\n                  title=\"智能分析阶段\"\n                  onComplete={() => {}}\n                  speed={120}\n                />\n              )}\n              {analysisStage === 'generate' && (\n                <StreamOutput\n                  content={generateStageOutput}\n                  isActive={true}\n                  title=\"报表生成阶段\"\n                  onComplete={() => {}}\n                  speed={80}\n                />\n              )}\n            </>\n          ) : (\n            <div className=\"flex-1 flex items-center justify-center text-gray-400\">\n              <div className=\"text-center\">\n                <Terminal className=\"w-12 h-12 mx-auto mb-2 opacity-50\" />\n                <p>AI处理输出</p>\n                <p className=\"text-sm\">点击\"一键处理全部\"开始智能分析</p>\n              </div>\n            </div>\n          )}\n        </motion.div>\n      </div>\n\n      {/* 文件预览模态框 */}\n      <AnimatePresence>\n        {showPreview && selectedFile && (\n          <motion.div\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            onClick={() => setShowPreview(false)}\n          >\n            <motion.div\n              className=\"tech-card max-w-4xl w-full max-h-[80vh] flex flex-col\"\n              initial={{ scale: 0.9, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              exit={{ scale: 0.9, opacity: 0 }}\n              onClick={(e) => e.stopPropagation()}\n            >\n              {/* 预览头部 */}\n              <div className=\"flex items-center justify-between p-4 border-b border-gray-700\">\n                <div>\n                  <h3 className=\"text-lg font-semibold text-white\">{selectedFile.filename}</h3>\n                  <p className=\"text-sm text-gray-400\">\n                    {(selectedFile.size / 1024).toFixed(1)} KB • {new Date(selectedFile.upload_time).toLocaleString()}\n                  </p>\n                </div>\n                <button\n                  onClick={() => setShowPreview(false)}\n                  className=\"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors\"\n                >\n                  <X className=\"w-5 h-5\" />\n                </button>\n              </div>\n\n              {/* 预览内容 */}\n              <div className=\"flex-1 overflow-auto p-4\">\n                {selectedFile.excel_data && selectedFile.excel_headers ? (\n                  // Excel表格预览\n                  <div className=\"bg-gray-900/50 rounded-lg overflow-hidden\">\n                    <div className=\"px-4 py-3 bg-gray-800/50 border-b border-gray-700\">\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"text-sm text-gray-300 font-medium\">\n                          📊 Excel表格预览\n                        </div>\n                        <div className=\"text-xs text-gray-400\">\n                          显示前50行 · {selectedFile.excel_headers.length}列\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"overflow-auto max-h-96\" style={{ maxWidth: '100%' }}>\n                      <table className=\"w-full text-sm border-collapse\">\n                        <thead className=\"sticky top-0 bg-gray-800 z-10\">\n                          <tr>\n                            {selectedFile.excel_headers.map((header, index) => (\n                              <th\n                                key={index}\n                                className=\"px-4 py-3 text-left text-gray-200 font-medium border-r border-gray-600 last:border-r-0 whitespace-nowrap min-w-[120px] bg-gray-800\"\n                              >\n                                <div className=\"flex items-center space-x-1\">\n                                  <span className=\"text-xs text-gray-400\">#{index + 1}</span>\n                                  <span>{header || `列${index + 1}`}</span>\n                                </div>\n                              </th>\n                            ))}\n                          </tr>\n                        </thead>\n                        <tbody>\n                          {selectedFile.excel_data.map((row, rowIndex) => (\n                            <tr\n                              key={rowIndex}\n                              className={`hover:bg-gray-700/30 transition-colors ${\n                                rowIndex % 2 === 0 ? 'bg-gray-800/20' : 'bg-gray-800/10'\n                              }`}\n                            >\n                              {selectedFile.excel_headers!.map((_, colIndex) => (\n                                <td\n                                  key={colIndex}\n                                  className=\"px-4 py-2 text-gray-300 border-r border-gray-700/50 last:border-r-0 whitespace-nowrap min-w-[120px]\"\n                                  title={row[colIndex]?.toString() || ''}\n                                >\n                                  <div className=\"max-w-[200px] truncate\">\n                                    {row[colIndex] || '-'}\n                                  </div>\n                                </td>\n                              ))}\n                            </tr>\n                          ))}\n                        </tbody>\n                      </table>\n                    </div>\n                    <div className=\"px-4 py-2 bg-gray-800/30 border-t border-gray-700\">\n                      <div className=\"text-xs text-gray-400 flex items-center justify-between\">\n                        <span>💡 提示：表格支持横向和纵向滚动</span>\n                        <span>数据行数：{selectedFile.excel_data.length}</span>\n                      </div>\n                    </div>\n                  </div>\n                ) : (\n                  // 文本文件预览\n                  <div className=\"bg-gray-900/50 rounded-lg p-4 font-mono text-sm text-gray-300 whitespace-pre-wrap\">\n                    {selectedFile.preview_content || '无法预览此文件内容'}\n                  </div>\n                )}\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n};\n\nexport default FileUploadSimple;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SACEC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,QAAQ,EACRC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,GAAG,EACHC,CAAC,QACI,cAAc;AACrB,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,oBAAoB,QAAQ,sCAAsC;AAC3E,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,YAAY,EAAEC,MAAM,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAgB7D,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACvD;EACA;EACA,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMoC,YAAY,GAAGnC,MAAM,CAAmB,IAAI,CAAC;EAEnD,MAAM;IAAEoC,cAAc;IAAEC,gBAAgB;IAAEC,gBAAgB;IAAEC,kBAAkB;IAAEC,SAAS;IAAEC,YAAY;IAAEC,aAAa;IAAEC;EAAiB,CAAC,GAAGvC,qBAAqB,CAAC,CAAC;;EAEpK;EACAH,SAAS,CAAC,MAAM;IACd,MAAM2C,cAAc,GAAGA,CAAA,KAAM;MAC3BhB,gBAAgB,CAAC,EAAE,CAAC;MACpBE,eAAe,CAAC,KAAK,CAAC;MACtBa,gBAAgB,CAAC,IAAI,CAAC;IACxB,CAAC;IAEDxB,YAAY,CAAC0B,EAAE,CAACzB,MAAM,CAAC0B,SAAS,EAAEF,cAAc,CAAC;IAEjD,OAAO,MAAM;MACXzB,YAAY,CAAC4B,GAAG,CAAC3B,MAAM,CAAC0B,SAAS,EAAEF,cAAc,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,CAACD,gBAAgB,CAAC,CAAC;EAEtB,MAAMK,gBAAgB,GAAG,MAAOC,KAA0C,IAAK;IAC7E,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,EAAE;MACT,KAAK,MAAME,IAAI,IAAIC,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC,EAAE;QACpC,MAAMK,OAAqB,GAAG;UAC5BC,EAAE,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;UAC3CC,QAAQ,EAAET,IAAI,CAACU,IAAI;UACnBC,SAAS,EAAEX,IAAI,CAACY,IAAI,IAAI,YAAY;UACpCC,IAAI,EAAEb,IAAI,CAACa,IAAI;UACfC,MAAM,EAAE,UAAU;UAClBC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACrCC,SAAS,EAAE,YAAYlB,IAAI,CAACU,IAAI;QAClC,CAAC;;QAED;QACA,IAAI;UACF,MAAMS,WAAW,GAAGnB,IAAI,CAACU,IAAI,CAACU,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,IAC1CrB,IAAI,CAACU,IAAI,CAACU,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,IACxCrB,IAAI,CAACU,IAAI,CAACU,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC;UAE3D,IAAIF,WAAW,EAAE;YACf,MAAMG,SAAS,GAAG,MAAMC,aAAa,CAACvB,IAAI,CAAC;YAC3CG,OAAO,CAACqB,UAAU,GAAGF,SAAS,CAACG,IAAI;YACnCtB,OAAO,CAACuB,aAAa,GAAGJ,SAAS,CAACK,OAAO;UAC3C,CAAC,MAAM;YACL,MAAMC,OAAO,GAAG,MAAMC,eAAe,CAAC7B,IAAI,CAAC;YAC3CG,OAAO,CAAC2B,eAAe,GAAGF,OAAO,CAACG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;UACxD;QACF,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACnC;QAEAxD,gBAAgB,CAAC0D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE/B,OAAO,CAAC,CAAC;QAC5CnB,cAAc,CAACmB,OAAO,CAACC,EAAE,EAAED,OAAO,CAACM,QAAQ,CAAC;MAC9C;IACF;EACF,CAAC;;EAED;EACA,MAAMoB,eAAe,GAAI7B,IAAU,IAAsB;IACvD,OAAO,IAAImC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;QAAA,IAAAC,SAAA;QACrBN,OAAO,EAAAM,SAAA,GAACD,CAAC,CAAC1C,MAAM,cAAA2C,SAAA,uBAARA,SAAA,CAAUC,MAAgB,CAAC;MACrC,CAAC;MACDL,MAAM,CAACM,OAAO,GAAGP,MAAM;MACvBC,MAAM,CAACO,UAAU,CAAC7C,IAAI,EAAE,OAAO,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMuB,aAAa,GAAIvB,IAAU,IAAoD;IACnF,OAAO,IAAImC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;QACrB,IAAI;UAAA,IAAAK,UAAA;UACF,MAAMrB,IAAI,GAAG,IAAIsB,UAAU,EAAAD,UAAA,GAACL,CAAC,CAAC1C,MAAM,cAAA+C,UAAA,uBAARA,UAAA,CAAUH,MAAqB,CAAC;UAC5D,MAAMK,QAAQ,GAAG/F,IAAI,CAACgG,IAAI,CAACxB,IAAI,EAAE;YAAEb,IAAI,EAAE;UAAQ,CAAC,CAAC;UACnD,MAAMsC,SAAS,GAAGF,QAAQ,CAACG,UAAU,CAAC,CAAC,CAAC;UACxC,MAAMC,SAAS,GAAGJ,QAAQ,CAACK,MAAM,CAACH,SAAS,CAAC;UAC5C,MAAMI,QAAQ,GAAGrG,IAAI,CAACsG,KAAK,CAACC,aAAa,CAACJ,SAAS,EAAE;YAAEK,MAAM,EAAE;UAAE,CAAC,CAAC;UAEnE,IAAIH,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM/B,OAAO,GAAG2B,QAAQ,CAAC,CAAC,CAAa;YACvC,MAAM7B,IAAI,GAAG6B,QAAQ,CAACK,KAAK,CAAC,CAAC,CAAY;YACzCvB,OAAO,CAAC;cAAEX,IAAI,EAAEA,IAAI,CAACkC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;cAAEhC;YAAQ,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,MAAM;YACLS,OAAO,CAAC;cAAEX,IAAI,EAAE,EAAE;cAAEE,OAAO,EAAE;YAAG,CAAC,CAAC;UACpC;QACF,CAAC,CAAC,OAAOK,KAAK,EAAE;UACdK,MAAM,CAACL,KAAK,CAAC;QACf;MACF,CAAC;MACDM,MAAM,CAACM,OAAO,GAAGP,MAAM;MACvBC,MAAM,CAACsB,iBAAiB,CAAC5D,IAAI,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6D,YAAY,GAAIC,MAAc,IAAK;IACvCtF,gBAAgB,CAAC0D,IAAI,IAAIA,IAAI,CAAC6B,GAAG,CAAC/D,IAAI,IACpCA,IAAI,CAACI,EAAE,KAAK0D,MAAM,GAAG;MAAE,GAAG9D,IAAI;MAAEc,MAAM,EAAE;IAAY,CAAC,GAAGd,IAC1D,CAAC,CAAC;IACFf,gBAAgB,CAAC6E,MAAM,CAAC;EAC1B,CAAC;EAED,MAAME,WAAW,GAAIF,MAAc,IAAK;IACtCtF,gBAAgB,CAAC0D,IAAI,IAAIA,IAAI,CAAC6B,GAAG,CAAC/D,IAAI,IACpCA,IAAI,CAACI,EAAE,KAAK0D,MAAM,GAAG;MAAE,GAAG9D,IAAI;MAAEc,MAAM,EAAE;IAAa,CAAC,GAAGd,IAC3D,CAAC,CAAC;IACFd,gBAAgB,CAAC4E,MAAM,CAAC;;IAExB;IACAG,UAAU,CAAC,MAAM;MACfzF,gBAAgB,CAAC0D,IAAI,IAAIA,IAAI,CAAC6B,GAAG,CAAC/D,IAAI,IACpCA,IAAI,CAACI,EAAE,KAAK0D,MAAM,GAAG;QAAE,GAAG9D,IAAI;QAAEc,MAAM,EAAE;MAAY,CAAC,GAAGd,IAC1D,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMkE,UAAU,GAAIJ,MAAc,IAAK;IACrCtF,gBAAgB,CAAC0D,IAAI,IAAIA,IAAI,CAACiC,MAAM,CAACnE,IAAI,IAAIA,IAAI,CAACI,EAAE,KAAK0D,MAAM,CAAC,CAAC;EACnE,CAAC;;EAED;EACA,MAAMM,WAAW,GAAIpE,IAAkB,IAAK;IAC1CpB,eAAe,CAACoB,IAAI,CAAC;IACrBlB,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMuF,eAAe,GAAGA,CAAA,KAAM;IAC5B3F,eAAe,CAAC,IAAI,CAAC;IACrB,MAAM4F,cAAc,GAAG/F,aAAa,CAAC4F,MAAM,CAACnE,IAAI,IAAIA,IAAI,CAACc,MAAM,KAAK,WAAW,CAAC;;IAEhF;IACAvB,gBAAgB,CAAC,WAAW,CAAC;;IAE7B;IACA0E,UAAU,CAAC,MAAM;MACf;MACA1E,gBAAgB,CAAC,SAAS,CAAC;;MAE3B;MACA+E,cAAc,CAACC,OAAO,CAAC,CAACvE,IAAI,EAAEwE,KAAK,KAAK;QACtCP,UAAU,CAAC,MAAM;UACfD,WAAW,CAAChE,IAAI,CAACI,EAAE,CAAC;QACtB,CAAC,EAAEoE,KAAK,GAAG,IAAI,CAAC;MAClB,CAAC,CAAC;MAEFP,UAAU,CAAC,MAAM;QACf;QACA1E,gBAAgB,CAAC,UAAU,CAAC;QAC5BJ,kBAAkB,CAAC,CAAC,CAAC,CAAC;;QAEtB8E,UAAU,CAAC,MAAM;UACf;UACAvF,eAAe,CAAC,KAAK,CAAC;UACtBa,gBAAgB,CAAC,IAAI,CAAC;QACxB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC;;EAED;;EAEA;EACA,MAAMkF,eAAe,GAAIX,MAAc,IAAK;IAC1C,OAAOzE,YAAY,CAACqF,cAAc,CAACC,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACd,MAAM,KAAKA,MAAM,CAAC;EACrE,CAAC;EAED,oBACE5F,OAAA;IAAK2G,SAAS,EAAC,gCAAgC;IAAAC,QAAA,gBAE7C5G,OAAA,CAACpB,MAAM,CAACiI,GAAG;MACTF,SAAS,EAAC,eAAe;MACzBG,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAE9B5G,OAAA;QAAK2G,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD5G,OAAA;UAAI2G,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAC;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGxDrH,OAAA;UAAK2G,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAE1C5G,OAAA;YAAK2G,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B5G,OAAA;cACEsH,GAAG,EAAEzG,YAAa;cAClB6B,IAAI,EAAC,MAAM;cACX6E,QAAQ;cACRC,MAAM,EAAC,sCAAsC;cAC7CC,QAAQ,EAAE/F,gBAAiB;cAC3BiF,SAAS,EAAC;YAAQ;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACFrH,OAAA;cACE0H,OAAO,EAAEA,CAAA;gBAAA,IAAAC,qBAAA;gBAAA,QAAAA,qBAAA,GAAM9G,YAAY,CAAC+G,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,KAAK,CAAC,CAAC;cAAA,CAAC;cAC7ClB,SAAS,EAAC,oDAAoD;cAAAC,QAAA,gBAE9D5G,OAAA,CAAChB,MAAM;gBAAC2H,SAAS,EAAC;cAAc;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wCAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbrH,OAAA;MAAK2G,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/B5G,OAAA,CAACpB,MAAM,CAACiI,GAAG;QACTF,SAAS,EAAC,yBAAyB;QACnCG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9Bc,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAnB,QAAA,gBAE3B5G,OAAA;UAAK2G,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAC7E5G,OAAA;YAAI2G,SAAS,EAAC,0BAA0B;YAAAC,QAAA,GAAC,kCAAO,EAACvG,aAAa,CAACmF,MAAM,EAAC,GAAC;UAAA;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC3EhH,aAAa,CAAC4F,MAAM,CAAC+B,CAAC,IAAIA,CAAC,CAACpF,MAAM,KAAK,WAAW,CAAC,CAAC4C,MAAM,GAAG,CAAC,iBAC7DxF,OAAA;YACE0H,OAAO,EAAEvB,eAAgB;YACzB8B,QAAQ,EAAE1H,YAAa;YACvBoG,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAE7D5G,OAAA,CAACZ,IAAI;cAACuH,SAAS,EAAC;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAChC9G,YAAY,GAAG,QAAQ,GAAG,QAAQ;UAAA;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENrH,OAAA;UAAK2G,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC5G,OAAA,CAACnB,eAAe;YAAA+H,QAAA,EACbvG,aAAa,CAACwF,GAAG,CAAE/D,IAAI,iBACtB9B,OAAA,CAACpB,MAAM,CAACiI,GAAG;cAETF,SAAS,EAAC,2DAA2D;cACrEG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BkB,IAAI,EAAE;gBAAEnB,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAC7BmB,MAAM;cAAAvB,QAAA,gBAEN5G,OAAA;gBAAK2G,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD5G,OAAA;kBAAK2G,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eACrB5G,OAAA;oBAAK2G,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C5G,OAAA,CAACf,QAAQ;sBAAC0H,SAAS,EAAC;oBAAwB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/CrH,OAAA;sBAAA4G,QAAA,gBACE5G,OAAA;wBAAI2G,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,EAAE9E,IAAI,CAACS;sBAAQ;wBAAA2E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3DrH,OAAA;wBAAG2G,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GACjC,CAAC9E,IAAI,CAACa,IAAI,GAAG,IAAI,EAAEyF,OAAO,CAAC,CAAC,CAAC,EAAC,aAAM,EAAC,IAAItF,IAAI,CAAChB,IAAI,CAACe,WAAW,CAAC,CAACwF,cAAc,CAAC,CAAC;sBAAA;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrH,OAAA;kBAAK2G,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAE1C5G,OAAA;oBAAK2G,SAAS,EAAE,6BACd7E,IAAI,CAACc,MAAM,KAAK,UAAU,GAAG,8BAA8B,GAC3Dd,IAAI,CAACc,MAAM,KAAK,WAAW,GAAG,gCAAgC,GAC9Dd,IAAI,CAACc,MAAM,KAAK,YAAY,GAAG,kCAAkC,GACjEd,IAAI,CAACc,MAAM,KAAK,WAAW,GAAG,kCAAkC,GAChE,4BAA4B,EAC3B;oBAAAgE,QAAA,EACA9E,IAAI,CAACc,MAAM,KAAK,UAAU,GAAG,KAAK,GAClCd,IAAI,CAACc,MAAM,KAAK,WAAW,GAAG,KAAK,GACnCd,IAAI,CAACc,MAAM,KAAK,YAAY,GAAG,KAAK,GACpCd,IAAI,CAACc,MAAM,KAAK,WAAW,GAAG,KAAK,GAAG;kBAAI;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eAGNrH,OAAA;oBACE0H,OAAO,EAAEA,CAAA,KAAMxB,WAAW,CAACpE,IAAI,CAAE;oBACjC6E,SAAS,EAAC,2EAA2E;oBACrF2B,KAAK,EAAC,0BAAM;oBAAA1B,QAAA,eAEZ5G,OAAA,CAACT,GAAG;sBAACoH,SAAS,EAAC;oBAAS;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,EAERvF,IAAI,CAACc,MAAM,KAAK,UAAU,iBACzB5C,OAAA;oBACE0H,OAAO,EAAEA,CAAA,KAAM/B,YAAY,CAAC7D,IAAI,CAACI,EAAE,CAAE;oBACrCyE,SAAS,EAAC,yEAAyE;oBACnF2B,KAAK,EAAC,0BAAM;oBAAA1B,QAAA,eAEZ5G,OAAA,CAACd,WAAW;sBAACyH,SAAS,EAAC;oBAAS;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CACT,EAEAvF,IAAI,CAACc,MAAM,KAAK,WAAW,iBAC1B5C,OAAA;oBACE0H,OAAO,EAAEA,CAAA,KAAM5B,WAAW,CAAChE,IAAI,CAACI,EAAE,CAAE;oBACpCyE,SAAS,EAAC,uEAAuE;oBACjF2B,KAAK,EAAC,0BAAM;oBAAA1B,QAAA,eAEZ5G,OAAA,CAACb,QAAQ;sBAACwH,SAAS,EAAC;oBAAS;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CACT,eAEDrH,OAAA;oBACE0H,OAAO,EAAEA,CAAA,KAAM1B,UAAU,CAAClE,IAAI,CAACI,EAAE,CAAE;oBACnCyE,SAAS,EAAC,mEAAmE;oBAC7E2B,KAAK,EAAC,0BAAM;oBAAA1B,QAAA,eAEZ5G,OAAA,CAACX,MAAM;sBAACsH,SAAS,EAAC;oBAAS;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL,CAAC,MAAM;gBACN,MAAMkB,YAAY,GAAGhC,eAAe,CAACzE,IAAI,CAACI,EAAE,CAAC;gBAC7C,IAAIqG,YAAY,IAAIA,YAAY,CAACC,YAAY,GAAG,CAAC,EAAE;kBACjD,oBACExI,OAAA;oBAAK2G,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB5G,OAAA;sBAAK2G,SAAS,EAAC,8DAA8D;sBAAAC,QAAA,gBAC3E5G,OAAA;wBAAA4G,QAAA,GAAO2B,YAAY,CAACE,WAAW,KAAK,WAAW,GAAG,KAAK,GAChDF,YAAY,CAACE,WAAW,KAAK,SAAS,GAAG,KAAK,GAC9CF,YAAY,CAACE,WAAW,KAAK,UAAU,GAAG,KAAK,GAAG,KAAK,EAAC,KAAG;sBAAA;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzErH,OAAA;wBAAA4G,QAAA,GAAO2B,YAAY,CAACC,YAAY,EAAC,GAAC;sBAAA;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC,eACNrH,OAAA;sBAAK2G,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,eACpD5G,OAAA,CAACpB,MAAM,CAACiI,GAAG;wBACTF,SAAS,EAAC,iCAAiC;wBAC3CG,OAAO,EAAE;0BAAE4B,KAAK,EAAE;wBAAE,CAAE;wBACtBzB,OAAO,EAAE;0BAAEyB,KAAK,EAAE,GAAGH,YAAY,CAACC,YAAY;wBAAI,CAAE;wBACpDG,UAAU,EAAE;0BAAEC,QAAQ,EAAE;wBAAI;sBAAE;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAEV;gBACA,OAAO,IAAI;cACb,CAAC,EAAE,CAAC;YAAA,GAlGCvF,IAAI,CAACI,EAAE;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmGF,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACa,CAAC,EAEjBhH,aAAa,CAACmF,MAAM,KAAK,CAAC,iBACzBxF,OAAA,CAACpB,MAAM,CAACiI,GAAG;YACTF,SAAS,EAAC,iCAAiC;YAC3CG,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YAAAH,QAAA,gBAExB5G,OAAA,CAACf,QAAQ;cAAC0H,SAAS,EAAC;YAAmC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DrH,OAAA;cAAA4G,QAAA,EAAG;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACbrH,OAAA;cAAG2G,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbrH,OAAA,CAACpB,MAAM,CAACiI,GAAG;QACTF,SAAS,EAAC,yBAAyB;QACnCG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9Bc,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAnB,QAAA,EAE1BxF,aAAa,gBACZpB,OAAA,CAAAE,SAAA;UAAA0G,QAAA,GACGxF,aAAa,KAAK,WAAW,iBAC5BpB,OAAA,CAACP,YAAY;YACXiE,OAAO,EAAEhE,oBAAqB;YAC9BmJ,QAAQ,EAAE,IAAK;YACfP,KAAK,EAAC,sCAAQ;YACdQ,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;YACrBC,KAAK,EAAE;UAAI;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACF,EACAjG,aAAa,KAAK,SAAS,iBAC1BpB,OAAA,CAACP,YAAY;YACXiE,OAAO,EAAE/D,kBAAmB;YAC5BkJ,QAAQ,EAAE,IAAK;YACfP,KAAK,EAAC,sCAAQ;YACdQ,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;YACrBC,KAAK,EAAE;UAAI;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACF,EACAjG,aAAa,KAAK,UAAU,iBAC3BpB,OAAA,CAACP,YAAY;YACXiE,OAAO,EAAE9D,mBAAoB;YAC7BiJ,QAAQ,EAAE,IAAK;YACfP,KAAK,EAAC,sCAAQ;YACdQ,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;YACrBC,KAAK,EAAE;UAAG;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CACF;QAAA,eACD,CAAC,gBAEHrH,OAAA;UAAK2G,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eACpE5G,OAAA;YAAK2G,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B5G,OAAA,CAACV,QAAQ;cAACqH,SAAS,EAAC;YAAmC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DrH,OAAA;cAAA4G,QAAA,EAAG;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACbrH,OAAA;cAAG2G,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAgB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNrH,OAAA,CAACnB,eAAe;MAAA+H,QAAA,EACbjG,WAAW,IAAIF,YAAY,iBAC1BT,OAAA,CAACpB,MAAM,CAACiI,GAAG;QACTF,SAAS,EAAC,sFAAsF;QAChGG,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBE,OAAO,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACxBmB,IAAI,EAAE;UAAEnB,OAAO,EAAE;QAAE,CAAE;QACrBW,OAAO,EAAEA,CAAA,KAAM9G,cAAc,CAAC,KAAK,CAAE;QAAAgG,QAAA,eAErC5G,OAAA,CAACpB,MAAM,CAACiI,GAAG;UACTF,SAAS,EAAC,uDAAuD;UACjEG,OAAO,EAAE;YAAEkC,KAAK,EAAE,GAAG;YAAEjC,OAAO,EAAE;UAAE,CAAE;UACpCE,OAAO,EAAE;YAAE+B,KAAK,EAAE,CAAC;YAAEjC,OAAO,EAAE;UAAE,CAAE;UAClCmB,IAAI,EAAE;YAAEc,KAAK,EAAE,GAAG;YAAEjC,OAAO,EAAE;UAAE,CAAE;UACjCW,OAAO,EAAGnD,CAAC,IAAKA,CAAC,CAAC0E,eAAe,CAAC,CAAE;UAAArC,QAAA,gBAGpC5G,OAAA;YAAK2G,SAAS,EAAC,gEAAgE;YAAAC,QAAA,gBAC7E5G,OAAA;cAAA4G,QAAA,gBACE5G,OAAA;gBAAI2G,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAEnG,YAAY,CAAC8B;cAAQ;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7ErH,OAAA;gBAAG2G,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACjC,CAACnG,YAAY,CAACkC,IAAI,GAAG,IAAI,EAAEyF,OAAO,CAAC,CAAC,CAAC,EAAC,aAAM,EAAC,IAAItF,IAAI,CAACrC,YAAY,CAACoC,WAAW,CAAC,CAACwF,cAAc,CAAC,CAAC;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNrH,OAAA;cACE0H,OAAO,EAAEA,CAAA,KAAM9G,cAAc,CAAC,KAAK,CAAE;cACrC+F,SAAS,EAAC,mFAAmF;cAAAC,QAAA,eAE7F5G,OAAA,CAACR,CAAC;gBAACmH,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNrH,OAAA;YAAK2G,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EACtCnG,YAAY,CAAC6C,UAAU,IAAI7C,YAAY,CAAC+C,aAAa;YAAA;YACpD;YACAxD,OAAA;cAAK2G,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxD5G,OAAA;gBAAK2G,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eAChE5G,OAAA;kBAAK2G,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD5G,OAAA;oBAAK2G,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAEnD;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNrH,OAAA;oBAAK2G,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,kCAC5B,EAACnG,YAAY,CAAC+C,aAAa,CAACgC,MAAM,EAAC,QAC9C;kBAAA;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrH,OAAA;gBAAK2G,SAAS,EAAC,wBAAwB;gBAACmB,KAAK,EAAE;kBAAEoB,QAAQ,EAAE;gBAAO,CAAE;gBAAAtC,QAAA,eAClE5G,OAAA;kBAAO2G,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC/C5G,OAAA;oBAAO2G,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,eAC9C5G,OAAA;sBAAA4G,QAAA,EACGnG,YAAY,CAAC+C,aAAa,CAACqC,GAAG,CAAC,CAACN,MAAM,EAAEe,KAAK,kBAC5CtG,OAAA;wBAEE2G,SAAS,EAAC,oIAAoI;wBAAAC,QAAA,eAE9I5G,OAAA;0BAAK2G,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,gBAC1C5G,OAAA;4BAAM2G,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,GAAC,GAAC,EAACN,KAAK,GAAG,CAAC;0BAAA;4BAAAY,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eAC3DrH,OAAA;4BAAA4G,QAAA,EAAOrB,MAAM,IAAI,IAAIe,KAAK,GAAG,CAAC;0BAAE;4BAAAY,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC;sBAAC,GANDf,KAAK;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAOR,CACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACRrH,OAAA;oBAAA4G,QAAA,EACGnG,YAAY,CAAC6C,UAAU,CAACuC,GAAG,CAAC,CAACsD,GAAG,EAAEC,QAAQ,kBACzCpJ,OAAA;sBAEE2G,SAAS,EAAE,0CACTyC,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAG,gBAAgB,GAAG,gBAAgB,EACvD;sBAAAxC,QAAA,EAEFnG,YAAY,CAAC+C,aAAa,CAAEqC,GAAG,CAAC,CAACwD,CAAC,EAAEC,QAAQ;wBAAA,IAAAC,aAAA;wBAAA,oBAC3CvJ,OAAA;0BAEE2G,SAAS,EAAC,qGAAqG;0BAC/G2B,KAAK,EAAE,EAAAiB,aAAA,GAAAJ,GAAG,CAACG,QAAQ,CAAC,cAAAC,aAAA,uBAAbA,aAAA,CAAelH,QAAQ,CAAC,CAAC,KAAI,EAAG;0BAAAuE,QAAA,eAEvC5G,OAAA;4BAAK2G,SAAS,EAAC,wBAAwB;4BAAAC,QAAA,EACpCuC,GAAG,CAACG,QAAQ,CAAC,IAAI;0BAAG;4BAAApC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClB;wBAAC,GANDiC,QAAQ;0BAAApC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAOX,CAAC;sBAAA,CACN;oBAAC,GAfG+B,QAAQ;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAgBX,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNrH,OAAA;gBAAK2G,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eAChE5G,OAAA;kBAAK2G,SAAS,EAAC,yDAAyD;kBAAAC,QAAA,gBACtE5G,OAAA;oBAAA4G,QAAA,EAAM;kBAAiB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9BrH,OAAA;oBAAA4G,QAAA,GAAM,gCAAK,EAACnG,YAAY,CAAC6C,UAAU,CAACkC,MAAM;kBAAA;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;YAAA;YAEN;YACArH,OAAA;cAAK2G,SAAS,EAAC,mFAAmF;cAAAC,QAAA,EAC/FnG,YAAY,CAACmD,eAAe,IAAI;YAAW;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEV,CAAC;AAACjH,EAAA,CAhgBID,gBAA0B;EAAA,QAS+GrB,qBAAqB;AAAA;AAAA0K,EAAA,GAT9JrJ,gBAA0B;AAkgBhC,eAAeA,gBAAgB;AAAC,IAAAqJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}