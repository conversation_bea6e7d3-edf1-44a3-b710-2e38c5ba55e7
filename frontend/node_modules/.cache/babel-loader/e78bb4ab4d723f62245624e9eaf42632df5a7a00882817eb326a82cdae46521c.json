{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport ProcessFlow from './components/ProcessFlow';\nimport FileUploadSimple from './components/FileUploadSimple';\nimport Header from './components/Header';\nimport { ProcessFlowProvider } from './contexts/ProcessFlowContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ProcessFlowProvider, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-tech-dark\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-tech-grid bg-tech-grid opacity-20 pointer-events-none\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"container mx-auto px-4 py-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 min-h-[calc(100vh-120px)]\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"lg:col-span-1 flex flex-col\",\n              initial: {\n                opacity: 0,\n                x: -50\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              transition: {\n                duration: 0.6\n              },\n              children: /*#__PURE__*/_jsxDEV(ProcessFlow, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"lg:col-span-2 flex flex-col\",\n              initial: {\n                opacity: 0,\n                x: 50\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              children: /*#__PURE__*/_jsxDEV(FileUploadSimple, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "motion", "ProcessFlow", "FileUploadSimple", "Header", "ProcessFlowProvider", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "x", "animate", "transition", "duration", "delay", "_c", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport ProcessFlow from './components/ProcessFlow';\nimport FileUploadSimple from './components/FileUploadSimple';\nimport Header from './components/Header';\nimport { ProcessFlowProvider } from './contexts/ProcessFlowContext';\n\nfunction App() {\n  return (\n    <ProcessFlowProvider>\n      <div className=\"min-h-screen bg-tech-dark\">\n        {/* 背景网格效果 */}\n        <div className=\"fixed inset-0 bg-tech-grid bg-tech-grid opacity-20 pointer-events-none\" />\n\n        {/* 主要内容 */}\n        <div className=\"relative z-10\">\n          <Header />\n\n          <main className=\"container mx-auto px-4 py-8\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 min-h-[calc(100vh-120px)]\">\n              {/* 左侧流程展示区域 */}\n              <motion.div\n                className=\"lg:col-span-1 flex flex-col\"\n                initial={{ opacity: 0, x: -50 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.6 }}\n              >\n                <ProcessFlow />\n              </motion.div>\n\n              {/* 右侧文件上传和操作区域 */}\n              <motion.div\n                className=\"lg:col-span-2 flex flex-col\"\n                initial={{ opacity: 0, x: 50 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n              >\n                <FileUploadSimple />\n              </motion.div>\n            </div>\n          </main>\n        </div>\n      </div>\n    </ProcessFlowProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,mBAAmB,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACF,mBAAmB;IAAAI,QAAA,eAClBF,OAAA;MAAKG,SAAS,EAAC,2BAA2B;MAAAD,QAAA,gBAExCF,OAAA;QAAKG,SAAS,EAAC;MAAwE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG1FP,OAAA;QAAKG,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAC5BF,OAAA,CAACH,MAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEVP,OAAA;UAAMG,SAAS,EAAC,6BAA6B;UAAAD,QAAA,eAC3CF,OAAA;YAAKG,SAAS,EAAC,iEAAiE;YAAAD,QAAA,gBAE9EF,OAAA,CAACN,MAAM,CAACc,GAAG;cACTL,SAAS,EAAC,6BAA6B;cACvCM,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAAZ,QAAA,eAE9BF,OAAA,CAACL,WAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGbP,OAAA,CAACN,MAAM,CAACc,GAAG;cACTL,SAAS,EAAC,6BAA6B;cACvCM,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAAAb,QAAA,eAE1CF,OAAA,CAACJ,gBAAgB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAE1B;AAACS,EAAA,GAtCQf,GAAG;AAwCZ,eAAeA,GAAG;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}