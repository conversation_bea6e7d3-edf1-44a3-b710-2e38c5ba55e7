{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useCallback } from 'react';\nconst initialSteps = [{\n  id: 'upload',\n  title: '文件上传',\n  description: '上传入户数据和投诉数据文件',\n  status: 'pending'\n}, {\n  id: 'validate',\n  title: '内容检测',\n  description: '验证文件格式和必要字段',\n  status: 'pending'\n}, {\n  id: 'configure',\n  title: '解析配置',\n  description: '配置智能解析规则',\n  status: 'pending'\n}, {\n  id: 'analyze',\n  title: '智能分析',\n  description: '提取转写文本并进行智能解析',\n  status: 'pending'\n}, {\n  id: 'generate',\n  title: '报表生成',\n  description: '生成分析报表和可视化图表',\n  status: 'pending'\n}, {\n  id: 'download',\n  title: '结果下载',\n  description: '下载分析结果和报表文件',\n  status: 'pending'\n}];\nexport const useProcessFlow = () => {\n  _s();\n  const [processState, setProcessState] = useState({\n    currentStep: '',\n    steps: initialSteps,\n    overallProgress: 0,\n    fileProgresses: []\n  });\n\n  // 激活某个步骤\n  const activateStep = useCallback(stepId => {\n    setProcessState(prev => ({\n      ...prev,\n      currentStep: stepId,\n      steps: prev.steps.map(step => ({\n        ...step,\n        status: step.id === stepId ? 'active' : step.status === 'active' ? 'pending' : step.status,\n        progress: step.id === stepId ? 0 : step.progress\n      }))\n    }));\n  }, []);\n\n  // 更新步骤进度\n  const updateStepProgress = useCallback((stepId, progress) => {\n    setProcessState(prev => ({\n      ...prev,\n      steps: prev.steps.map(step => step.id === stepId ? {\n        ...step,\n        progress\n      } : step)\n    }));\n  }, []);\n\n  // 完成某个步骤\n  const completeStep = useCallback(stepId => {\n    setProcessState(prev => {\n      const updatedSteps = prev.steps.map(step => step.id === stepId ? {\n        ...step,\n        status: 'completed',\n        progress: 100\n      } : step);\n      const completedCount = updatedSteps.filter(s => s.status === 'completed').length;\n      const overallProgress = completedCount / updatedSteps.length * 100;\n      return {\n        ...prev,\n        steps: updatedSteps,\n        overallProgress\n      };\n    });\n  }, []);\n\n  // 设置步骤错误\n  const setStepError = useCallback((stepId, errorMessage) => {\n    setProcessState(prev => ({\n      ...prev,\n      steps: prev.steps.map(step => step.id === stepId ? {\n        ...step,\n        status: 'error',\n        progress: 0\n      } : step)\n    }));\n  }, []);\n\n  // 添加文件进度跟踪\n  const addFileProgress = useCallback((fileId, fileName) => {\n    setProcessState(prev => ({\n      ...prev,\n      fileProgresses: [...prev.fileProgresses.filter(fp => fp.fileId !== fileId), {\n        fileId,\n        fileName,\n        currentStep: 'upload',\n        stepProgress: 0\n      }]\n    }));\n  }, []);\n\n  // 更新文件进度\n  const updateFileProgress = useCallback((fileId, stepId, progress) => {\n    setProcessState(prev => {\n      const updatedFileProgresses = prev.fileProgresses.map(fp => fp.fileId === fileId ? {\n        ...fp,\n        currentStep: stepId,\n        stepProgress: progress\n      } : fp);\n\n      // 计算整体进度\n      const totalFiles = updatedFileProgresses.length;\n      if (totalFiles === 0) return prev;\n      const stepWeights = {\n        'upload': 1,\n        'validate': 2,\n        'configure': 3,\n        'analyze': 4,\n        'generate': 5,\n        'download': 6\n      };\n      const totalProgress = updatedFileProgresses.reduce((sum, fp) => {\n        const stepWeight = stepWeights[fp.currentStep] || 1;\n        const fileProgress = ((stepWeight - 1) * 100 + fp.stepProgress) / 6;\n        return sum + fileProgress;\n      }, 0);\n      const overallProgress = totalProgress / totalFiles;\n\n      // 更新步骤状态\n      const updatedSteps = prev.steps.map(step => {\n        const filesInThisStep = updatedFileProgresses.filter(fp => fp.currentStep === step.id);\n        const filesCompletedThisStep = updatedFileProgresses.filter(fp => {\n          const stepWeight = stepWeights[fp.currentStep] || 1;\n          const currentStepWeight = stepWeights[step.id] || 1;\n          return stepWeight > currentStepWeight || stepWeight === currentStepWeight && fp.stepProgress === 100;\n        });\n        if (filesInThisStep.length > 0) {\n          const avgProgress = filesInThisStep.reduce((sum, fp) => sum + fp.stepProgress, 0) / filesInThisStep.length;\n          return {\n            ...step,\n            status: 'active',\n            progress: Math.round(avgProgress)\n          };\n        } else if (filesCompletedThisStep.length === totalFiles && totalFiles > 0) {\n          return {\n            ...step,\n            status: 'completed',\n            progress: 100\n          };\n        } else {\n          return {\n            ...step,\n            status: 'pending',\n            progress: 0\n          };\n        }\n      });\n      return {\n        ...prev,\n        fileProgresses: updatedFileProgresses,\n        steps: updatedSteps,\n        overallProgress: Math.round(overallProgress)\n      };\n    });\n  }, []);\n\n  // 重置所有步骤\n  const resetFlow = useCallback(() => {\n    setProcessState({\n      currentStep: '',\n      steps: initialSteps,\n      overallProgress: 0,\n      fileProgresses: []\n    });\n  }, []);\n\n  // 文件上传时的联动\n  const onFileUploaded = useCallback(() => {\n    activateStep('upload');\n    setTimeout(() => completeStep('upload'), 1000);\n  }, [activateStep, completeStep]);\n\n  // 文件验证时的联动\n  const onFileValidation = useCallback(() => {\n    activateStep('validate');\n    // 模拟验证过程\n    let progress = 0;\n    const interval = setInterval(() => {\n      progress += 20;\n      updateStepProgress('validate', progress);\n      if (progress >= 100) {\n        clearInterval(interval);\n        completeStep('validate');\n      }\n    }, 200);\n  }, [activateStep, updateStepProgress, completeStep]);\n\n  // 文件处理时的联动\n  const onFileProcessing = useCallback(() => {\n    activateStep('configure');\n    setTimeout(() => {\n      completeStep('configure');\n      activateStep('analyze');\n\n      // 模拟分析过程\n      let progress = 0;\n      const interval = setInterval(() => {\n        progress += 10;\n        updateStepProgress('analyze', progress);\n        if (progress >= 100) {\n          clearInterval(interval);\n          completeStep('analyze');\n        }\n      }, 300);\n    }, 1000);\n  }, [activateStep, completeStep, updateStepProgress]);\n\n  // 报表生成时的联动\n  const onReportGeneration = useCallback(() => {\n    activateStep('generate');\n    // 模拟报表生成过程\n    let progress = 0;\n    const interval = setInterval(() => {\n      progress += 15;\n      updateStepProgress('generate', progress);\n      if (progress >= 100) {\n        clearInterval(interval);\n        completeStep('generate');\n        activateStep('download');\n        setTimeout(() => completeStep('download'), 500);\n      }\n    }, 400);\n  }, [activateStep, updateStepProgress, completeStep]);\n  return {\n    processState,\n    activateStep,\n    updateStepProgress,\n    completeStep,\n    setStepError,\n    resetFlow,\n    // 联动方法\n    onFileUploaded,\n    onFileValidation,\n    onFileProcessing,\n    onReportGeneration\n  };\n};\n_s(useProcessFlow, \"GFICsjWRkSS9fGfUUc1O/fhpWmU=\");", "map": {"version": 3, "names": ["useState", "useCallback", "initialSteps", "id", "title", "description", "status", "useProcessFlow", "_s", "processState", "setProcessState", "currentStep", "steps", "overallProgress", "fileProgresses", "activateStep", "stepId", "prev", "map", "step", "progress", "updateStepProgress", "completeStep", "updatedSteps", "completedCount", "filter", "s", "length", "setStepError", "errorMessage", "addFileProgress", "fileId", "fileName", "fp", "stepProgress", "updateFileProgress", "updatedFileProgresses", "totalFiles", "stepWeights", "totalProgress", "reduce", "sum", "step<PERSON><PERSON>ght", "fileProgress", "filesInThisStep", "filesCompletedThisStep", "currentStepWeight", "avgProgress", "Math", "round", "resetFlow", "onFileUploaded", "setTimeout", "onFileValidation", "interval", "setInterval", "clearInterval", "onFileProcessing", "onReportGeneration"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/hooks/useProcessFlow.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\n\nexport interface ProcessStep {\n  id: string;\n  title: string;\n  description: string;\n  status: 'pending' | 'active' | 'completed' | 'error';\n  progress?: number;\n}\n\nexport interface FileProgress {\n  fileId: string;\n  fileName: string;\n  currentStep: string;\n  stepProgress: number;\n}\n\nexport interface ProcessFlowState {\n  currentStep: string;\n  steps: ProcessStep[];\n  overallProgress: number;\n  fileProgresses: FileProgress[];\n}\n\nconst initialSteps: ProcessStep[] = [\n  {\n    id: 'upload',\n    title: '文件上传',\n    description: '上传入户数据和投诉数据文件',\n    status: 'pending'\n  },\n  {\n    id: 'validate',\n    title: '内容检测',\n    description: '验证文件格式和必要字段',\n    status: 'pending'\n  },\n  {\n    id: 'configure',\n    title: '解析配置',\n    description: '配置智能解析规则',\n    status: 'pending'\n  },\n  {\n    id: 'analyze',\n    title: '智能分析',\n    description: '提取转写文本并进行智能解析',\n    status: 'pending'\n  },\n  {\n    id: 'generate',\n    title: '报表生成',\n    description: '生成分析报表和可视化图表',\n    status: 'pending'\n  },\n  {\n    id: 'download',\n    title: '结果下载',\n    description: '下载分析结果和报表文件',\n    status: 'pending'\n  }\n];\n\nexport const useProcessFlow = () => {\n  const [processState, setProcessState] = useState<ProcessFlowState>({\n    currentStep: '',\n    steps: initialSteps,\n    overallProgress: 0,\n    fileProgresses: []\n  });\n\n  // 激活某个步骤\n  const activateStep = useCallback((stepId: string) => {\n    setProcessState(prev => ({\n      ...prev,\n      currentStep: stepId,\n      steps: prev.steps.map(step => ({\n        ...step,\n        status: step.id === stepId ? 'active' : \n                step.status === 'active' ? 'pending' : step.status,\n        progress: step.id === stepId ? 0 : step.progress\n      }))\n    }));\n  }, []);\n\n  // 更新步骤进度\n  const updateStepProgress = useCallback((stepId: string, progress: number) => {\n    setProcessState(prev => ({\n      ...prev,\n      steps: prev.steps.map(step => \n        step.id === stepId ? { ...step, progress } : step\n      )\n    }));\n  }, []);\n\n  // 完成某个步骤\n  const completeStep = useCallback((stepId: string) => {\n    setProcessState(prev => {\n      const updatedSteps = prev.steps.map(step => \n        step.id === stepId ? { ...step, status: 'completed' as const, progress: 100 } : step\n      );\n      \n      const completedCount = updatedSteps.filter(s => s.status === 'completed').length;\n      const overallProgress = (completedCount / updatedSteps.length) * 100;\n      \n      return {\n        ...prev,\n        steps: updatedSteps,\n        overallProgress\n      };\n    });\n  }, []);\n\n  // 设置步骤错误\n  const setStepError = useCallback((stepId: string, errorMessage?: string) => {\n    setProcessState(prev => ({\n      ...prev,\n      steps: prev.steps.map(step => \n        step.id === stepId ? { \n          ...step, \n          status: 'error' as const, \n          progress: 0 \n        } : step\n      )\n    }));\n  }, []);\n\n  // 添加文件进度跟踪\n  const addFileProgress = useCallback((fileId: string, fileName: string) => {\n    setProcessState(prev => ({\n      ...prev,\n      fileProgresses: [\n        ...prev.fileProgresses.filter(fp => fp.fileId !== fileId),\n        {\n          fileId,\n          fileName,\n          currentStep: 'upload',\n          stepProgress: 0\n        }\n      ]\n    }));\n  }, []);\n\n  // 更新文件进度\n  const updateFileProgress = useCallback((fileId: string, stepId: string, progress: number) => {\n    setProcessState(prev => {\n      const updatedFileProgresses = prev.fileProgresses.map(fp =>\n        fp.fileId === fileId ? { ...fp, currentStep: stepId, stepProgress: progress } : fp\n      );\n\n      // 计算整体进度\n      const totalFiles = updatedFileProgresses.length;\n      if (totalFiles === 0) return prev;\n\n      const stepWeights = {\n        'upload': 1,\n        'validate': 2,\n        'configure': 3,\n        'analyze': 4,\n        'generate': 5,\n        'download': 6\n      };\n\n      const totalProgress = updatedFileProgresses.reduce((sum, fp) => {\n        const stepWeight = stepWeights[fp.currentStep as keyof typeof stepWeights] || 1;\n        const fileProgress = ((stepWeight - 1) * 100 + fp.stepProgress) / 6;\n        return sum + fileProgress;\n      }, 0);\n\n      const overallProgress = totalProgress / totalFiles;\n\n      // 更新步骤状态\n      const updatedSteps = prev.steps.map(step => {\n        const filesInThisStep = updatedFileProgresses.filter(fp => fp.currentStep === step.id);\n        const filesCompletedThisStep = updatedFileProgresses.filter(fp => {\n          const stepWeight = stepWeights[fp.currentStep as keyof typeof stepWeights] || 1;\n          const currentStepWeight = stepWeights[step.id as keyof typeof stepWeights] || 1;\n          return stepWeight > currentStepWeight || (stepWeight === currentStepWeight && fp.stepProgress === 100);\n        });\n\n        if (filesInThisStep.length > 0) {\n          const avgProgress = filesInThisStep.reduce((sum, fp) => sum + fp.stepProgress, 0) / filesInThisStep.length;\n          return { ...step, status: 'active' as const, progress: Math.round(avgProgress) };\n        } else if (filesCompletedThisStep.length === totalFiles && totalFiles > 0) {\n          return { ...step, status: 'completed' as const, progress: 100 };\n        } else {\n          return { ...step, status: 'pending' as const, progress: 0 };\n        }\n      });\n\n      return {\n        ...prev,\n        fileProgresses: updatedFileProgresses,\n        steps: updatedSteps,\n        overallProgress: Math.round(overallProgress)\n      };\n    });\n  }, []);\n\n  // 重置所有步骤\n  const resetFlow = useCallback(() => {\n    setProcessState({\n      currentStep: '',\n      steps: initialSteps,\n      overallProgress: 0,\n      fileProgresses: []\n    });\n  }, []);\n\n  // 文件上传时的联动\n  const onFileUploaded = useCallback(() => {\n    activateStep('upload');\n    setTimeout(() => completeStep('upload'), 1000);\n  }, [activateStep, completeStep]);\n\n  // 文件验证时的联动\n  const onFileValidation = useCallback(() => {\n    activateStep('validate');\n    // 模拟验证过程\n    let progress = 0;\n    const interval = setInterval(() => {\n      progress += 20;\n      updateStepProgress('validate', progress);\n      if (progress >= 100) {\n        clearInterval(interval);\n        completeStep('validate');\n      }\n    }, 200);\n  }, [activateStep, updateStepProgress, completeStep]);\n\n  // 文件处理时的联动\n  const onFileProcessing = useCallback(() => {\n    activateStep('configure');\n    setTimeout(() => {\n      completeStep('configure');\n      activateStep('analyze');\n      \n      // 模拟分析过程\n      let progress = 0;\n      const interval = setInterval(() => {\n        progress += 10;\n        updateStepProgress('analyze', progress);\n        if (progress >= 100) {\n          clearInterval(interval);\n          completeStep('analyze');\n        }\n      }, 300);\n    }, 1000);\n  }, [activateStep, completeStep, updateStepProgress]);\n\n  // 报表生成时的联动\n  const onReportGeneration = useCallback(() => {\n    activateStep('generate');\n    // 模拟报表生成过程\n    let progress = 0;\n    const interval = setInterval(() => {\n      progress += 15;\n      updateStepProgress('generate', progress);\n      if (progress >= 100) {\n        clearInterval(interval);\n        completeStep('generate');\n        activateStep('download');\n        setTimeout(() => completeStep('download'), 500);\n      }\n    }, 400);\n  }, [activateStep, updateStepProgress, completeStep]);\n\n  return {\n    processState,\n    activateStep,\n    updateStepProgress,\n    completeStep,\n    setStepError,\n    resetFlow,\n    // 联动方法\n    onFileUploaded,\n    onFileValidation,\n    onFileProcessing,\n    onReportGeneration\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAwB7C,MAAMC,YAA2B,GAAG,CAClC;EACEC,EAAE,EAAE,QAAQ;EACZC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,eAAe;EAC5BC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,UAAU;EACdC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,aAAa;EAC1BC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,WAAW;EACfC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,UAAU;EACvBC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,SAAS;EACbC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,eAAe;EAC5BC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,UAAU;EACdC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,cAAc;EAC3BC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,EAAE,EAAE,UAAU;EACdC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,aAAa;EAC1BC,MAAM,EAAE;AACV,CAAC,CACF;AAED,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAmB;IACjEW,WAAW,EAAE,EAAE;IACfC,KAAK,EAAEV,YAAY;IACnBW,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAMC,YAAY,GAAGd,WAAW,CAAEe,MAAc,IAAK;IACnDN,eAAe,CAACO,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPN,WAAW,EAAEK,MAAM;MACnBJ,KAAK,EAAEK,IAAI,CAACL,KAAK,CAACM,GAAG,CAACC,IAAI,KAAK;QAC7B,GAAGA,IAAI;QACPb,MAAM,EAAEa,IAAI,CAAChB,EAAE,KAAKa,MAAM,GAAG,QAAQ,GAC7BG,IAAI,CAACb,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAGa,IAAI,CAACb,MAAM;QAC1Dc,QAAQ,EAAED,IAAI,CAAChB,EAAE,KAAKa,MAAM,GAAG,CAAC,GAAGG,IAAI,CAACC;MAC1C,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,kBAAkB,GAAGpB,WAAW,CAAC,CAACe,MAAc,EAAEI,QAAgB,KAAK;IAC3EV,eAAe,CAACO,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPL,KAAK,EAAEK,IAAI,CAACL,KAAK,CAACM,GAAG,CAACC,IAAI,IACxBA,IAAI,CAAChB,EAAE,KAAKa,MAAM,GAAG;QAAE,GAAGG,IAAI;QAAEC;MAAS,CAAC,GAAGD,IAC/C;IACF,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,YAAY,GAAGrB,WAAW,CAAEe,MAAc,IAAK;IACnDN,eAAe,CAACO,IAAI,IAAI;MACtB,MAAMM,YAAY,GAAGN,IAAI,CAACL,KAAK,CAACM,GAAG,CAACC,IAAI,IACtCA,IAAI,CAAChB,EAAE,KAAKa,MAAM,GAAG;QAAE,GAAGG,IAAI;QAAEb,MAAM,EAAE,WAAoB;QAAEc,QAAQ,EAAE;MAAI,CAAC,GAAGD,IAClF,CAAC;MAED,MAAMK,cAAc,GAAGD,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpB,MAAM,KAAK,WAAW,CAAC,CAACqB,MAAM;MAChF,MAAMd,eAAe,GAAIW,cAAc,GAAGD,YAAY,CAACI,MAAM,GAAI,GAAG;MAEpE,OAAO;QACL,GAAGV,IAAI;QACPL,KAAK,EAAEW,YAAY;QACnBV;MACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMe,YAAY,GAAG3B,WAAW,CAAC,CAACe,MAAc,EAAEa,YAAqB,KAAK;IAC1EnB,eAAe,CAACO,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPL,KAAK,EAAEK,IAAI,CAACL,KAAK,CAACM,GAAG,CAACC,IAAI,IACxBA,IAAI,CAAChB,EAAE,KAAKa,MAAM,GAAG;QACnB,GAAGG,IAAI;QACPb,MAAM,EAAE,OAAgB;QACxBc,QAAQ,EAAE;MACZ,CAAC,GAAGD,IACN;IACF,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,eAAe,GAAG7B,WAAW,CAAC,CAAC8B,MAAc,EAAEC,QAAgB,KAAK;IACxEtB,eAAe,CAACO,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPH,cAAc,EAAE,CACd,GAAGG,IAAI,CAACH,cAAc,CAACW,MAAM,CAACQ,EAAE,IAAIA,EAAE,CAACF,MAAM,KAAKA,MAAM,CAAC,EACzD;QACEA,MAAM;QACNC,QAAQ;QACRrB,WAAW,EAAE,QAAQ;QACrBuB,YAAY,EAAE;MAChB,CAAC;IAEL,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,kBAAkB,GAAGlC,WAAW,CAAC,CAAC8B,MAAc,EAAEf,MAAc,EAAEI,QAAgB,KAAK;IAC3FV,eAAe,CAACO,IAAI,IAAI;MACtB,MAAMmB,qBAAqB,GAAGnB,IAAI,CAACH,cAAc,CAACI,GAAG,CAACe,EAAE,IACtDA,EAAE,CAACF,MAAM,KAAKA,MAAM,GAAG;QAAE,GAAGE,EAAE;QAAEtB,WAAW,EAAEK,MAAM;QAAEkB,YAAY,EAAEd;MAAS,CAAC,GAAGa,EAClF,CAAC;;MAED;MACA,MAAMI,UAAU,GAAGD,qBAAqB,CAACT,MAAM;MAC/C,IAAIU,UAAU,KAAK,CAAC,EAAE,OAAOpB,IAAI;MAEjC,MAAMqB,WAAW,GAAG;QAClB,QAAQ,EAAE,CAAC;QACX,UAAU,EAAE,CAAC;QACb,WAAW,EAAE,CAAC;QACd,SAAS,EAAE,CAAC;QACZ,UAAU,EAAE,CAAC;QACb,UAAU,EAAE;MACd,CAAC;MAED,MAAMC,aAAa,GAAGH,qBAAqB,CAACI,MAAM,CAAC,CAACC,GAAG,EAAER,EAAE,KAAK;QAC9D,MAAMS,UAAU,GAAGJ,WAAW,CAACL,EAAE,CAACtB,WAAW,CAA6B,IAAI,CAAC;QAC/E,MAAMgC,YAAY,GAAG,CAAC,CAACD,UAAU,GAAG,CAAC,IAAI,GAAG,GAAGT,EAAE,CAACC,YAAY,IAAI,CAAC;QACnE,OAAOO,GAAG,GAAGE,YAAY;MAC3B,CAAC,EAAE,CAAC,CAAC;MAEL,MAAM9B,eAAe,GAAG0B,aAAa,GAAGF,UAAU;;MAElD;MACA,MAAMd,YAAY,GAAGN,IAAI,CAACL,KAAK,CAACM,GAAG,CAACC,IAAI,IAAI;QAC1C,MAAMyB,eAAe,GAAGR,qBAAqB,CAACX,MAAM,CAACQ,EAAE,IAAIA,EAAE,CAACtB,WAAW,KAAKQ,IAAI,CAAChB,EAAE,CAAC;QACtF,MAAM0C,sBAAsB,GAAGT,qBAAqB,CAACX,MAAM,CAACQ,EAAE,IAAI;UAChE,MAAMS,UAAU,GAAGJ,WAAW,CAACL,EAAE,CAACtB,WAAW,CAA6B,IAAI,CAAC;UAC/E,MAAMmC,iBAAiB,GAAGR,WAAW,CAACnB,IAAI,CAAChB,EAAE,CAA6B,IAAI,CAAC;UAC/E,OAAOuC,UAAU,GAAGI,iBAAiB,IAAKJ,UAAU,KAAKI,iBAAiB,IAAIb,EAAE,CAACC,YAAY,KAAK,GAAI;QACxG,CAAC,CAAC;QAEF,IAAIU,eAAe,CAACjB,MAAM,GAAG,CAAC,EAAE;UAC9B,MAAMoB,WAAW,GAAGH,eAAe,CAACJ,MAAM,CAAC,CAACC,GAAG,EAAER,EAAE,KAAKQ,GAAG,GAAGR,EAAE,CAACC,YAAY,EAAE,CAAC,CAAC,GAAGU,eAAe,CAACjB,MAAM;UAC1G,OAAO;YAAE,GAAGR,IAAI;YAAEb,MAAM,EAAE,QAAiB;YAAEc,QAAQ,EAAE4B,IAAI,CAACC,KAAK,CAACF,WAAW;UAAE,CAAC;QAClF,CAAC,MAAM,IAAIF,sBAAsB,CAAClB,MAAM,KAAKU,UAAU,IAAIA,UAAU,GAAG,CAAC,EAAE;UACzE,OAAO;YAAE,GAAGlB,IAAI;YAAEb,MAAM,EAAE,WAAoB;YAAEc,QAAQ,EAAE;UAAI,CAAC;QACjE,CAAC,MAAM;UACL,OAAO;YAAE,GAAGD,IAAI;YAAEb,MAAM,EAAE,SAAkB;YAAEc,QAAQ,EAAE;UAAE,CAAC;QAC7D;MACF,CAAC,CAAC;MAEF,OAAO;QACL,GAAGH,IAAI;QACPH,cAAc,EAAEsB,qBAAqB;QACrCxB,KAAK,EAAEW,YAAY;QACnBV,eAAe,EAAEmC,IAAI,CAACC,KAAK,CAACpC,eAAe;MAC7C,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqC,SAAS,GAAGjD,WAAW,CAAC,MAAM;IAClCS,eAAe,CAAC;MACdC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAEV,YAAY;MACnBW,eAAe,EAAE,CAAC;MAClBC,cAAc,EAAE;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqC,cAAc,GAAGlD,WAAW,CAAC,MAAM;IACvCc,YAAY,CAAC,QAAQ,CAAC;IACtBqC,UAAU,CAAC,MAAM9B,YAAY,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;EAChD,CAAC,EAAE,CAACP,YAAY,EAAEO,YAAY,CAAC,CAAC;;EAEhC;EACA,MAAM+B,gBAAgB,GAAGpD,WAAW,CAAC,MAAM;IACzCc,YAAY,CAAC,UAAU,CAAC;IACxB;IACA,IAAIK,QAAQ,GAAG,CAAC;IAChB,MAAMkC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCnC,QAAQ,IAAI,EAAE;MACdC,kBAAkB,CAAC,UAAU,EAAED,QAAQ,CAAC;MACxC,IAAIA,QAAQ,IAAI,GAAG,EAAE;QACnBoC,aAAa,CAACF,QAAQ,CAAC;QACvBhC,YAAY,CAAC,UAAU,CAAC;MAC1B;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,CAACP,YAAY,EAAEM,kBAAkB,EAAEC,YAAY,CAAC,CAAC;;EAEpD;EACA,MAAMmC,gBAAgB,GAAGxD,WAAW,CAAC,MAAM;IACzCc,YAAY,CAAC,WAAW,CAAC;IACzBqC,UAAU,CAAC,MAAM;MACf9B,YAAY,CAAC,WAAW,CAAC;MACzBP,YAAY,CAAC,SAAS,CAAC;;MAEvB;MACA,IAAIK,QAAQ,GAAG,CAAC;MAChB,MAAMkC,QAAQ,GAAGC,WAAW,CAAC,MAAM;QACjCnC,QAAQ,IAAI,EAAE;QACdC,kBAAkB,CAAC,SAAS,EAAED,QAAQ,CAAC;QACvC,IAAIA,QAAQ,IAAI,GAAG,EAAE;UACnBoC,aAAa,CAACF,QAAQ,CAAC;UACvBhC,YAAY,CAAC,SAAS,CAAC;QACzB;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAACP,YAAY,EAAEO,YAAY,EAAED,kBAAkB,CAAC,CAAC;;EAEpD;EACA,MAAMqC,kBAAkB,GAAGzD,WAAW,CAAC,MAAM;IAC3Cc,YAAY,CAAC,UAAU,CAAC;IACxB;IACA,IAAIK,QAAQ,GAAG,CAAC;IAChB,MAAMkC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCnC,QAAQ,IAAI,EAAE;MACdC,kBAAkB,CAAC,UAAU,EAAED,QAAQ,CAAC;MACxC,IAAIA,QAAQ,IAAI,GAAG,EAAE;QACnBoC,aAAa,CAACF,QAAQ,CAAC;QACvBhC,YAAY,CAAC,UAAU,CAAC;QACxBP,YAAY,CAAC,UAAU,CAAC;QACxBqC,UAAU,CAAC,MAAM9B,YAAY,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC;MACjD;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,CAACP,YAAY,EAAEM,kBAAkB,EAAEC,YAAY,CAAC,CAAC;EAEpD,OAAO;IACLb,YAAY;IACZM,YAAY;IACZM,kBAAkB;IAClBC,YAAY;IACZM,YAAY;IACZsB,SAAS;IACT;IACAC,cAAc;IACdE,gBAAgB;IAChBI,gBAAgB;IAChBC;EACF,CAAC;AACH,CAAC;AAAClD,EAAA,CAzNWD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}