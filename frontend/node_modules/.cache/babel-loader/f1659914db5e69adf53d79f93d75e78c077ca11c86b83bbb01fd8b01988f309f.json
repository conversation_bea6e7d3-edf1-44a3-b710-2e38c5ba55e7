{"ast": null, "code": "var _jsxFileName = \"D:\\\\pyworkspace\\\\analysisFlow\\\\frontend\\\\src\\\\components\\\\ProcessFlow.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Upload, CheckCircle, Settings, Brain, FileText, Download, Clock, AlertCircle, Zap } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProcessFlow = () => {\n  _s();\n  const [steps, setSteps] = useState([{\n    id: 'upload',\n    title: '文件上传',\n    description: '上传入户数据和投诉数据文件',\n    icon: /*#__PURE__*/_jsxDEV(Upload, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this),\n    status: 'pending'\n  }, {\n    id: 'validate',\n    title: '内容检测',\n    description: '验证文件格式和必要字段',\n    icon: /*#__PURE__*/_jsxDEV(CheckCircle, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 13\n    }, this),\n    status: 'pending'\n  }, {\n    id: 'configure',\n    title: '解析配置',\n    description: '配置智能解析规则',\n    icon: /*#__PURE__*/_jsxDEV(Settings, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this),\n    status: 'pending'\n  }, {\n    id: 'analyze',\n    title: '智能分析',\n    description: '提取转写文本并进行智能解析',\n    icon: /*#__PURE__*/_jsxDEV(Brain, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 13\n    }, this),\n    status: 'pending'\n  }, {\n    id: 'generate',\n    title: '报表生成',\n    description: '生成分析报表和可视化图表',\n    icon: /*#__PURE__*/_jsxDEV(FileText, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 13\n    }, this),\n    status: 'pending'\n  }, {\n    id: 'download',\n    title: '结果下载',\n    description: '下载分析结果和报表文件',\n    icon: /*#__PURE__*/_jsxDEV(Download, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this),\n    status: 'pending'\n  }]);\n  const [currentProgress, setCurrentProgress] = useState('');\n\n  // 模拟流程进度更新\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setSteps(prevSteps => {\n        const newSteps = [...prevSteps];\n        const activeIndex = newSteps.findIndex(step => step.status === 'active');\n        if (activeIndex !== -1) {\n          // 更新当前活动步骤的进度\n          if (newSteps[activeIndex].progress === undefined) {\n            newSteps[activeIndex].progress = 0;\n          }\n          newSteps[activeIndex].progress += 10;\n          if (newSteps[activeIndex].progress >= 100) {\n            newSteps[activeIndex].status = 'completed';\n            newSteps[activeIndex].progress = 100;\n\n            // 激活下一个步骤\n            if (activeIndex + 1 < newSteps.length) {\n              newSteps[activeIndex + 1].status = 'active';\n              newSteps[activeIndex + 1].progress = 0;\n            }\n          }\n        }\n        return newSteps;\n      });\n    }, 500);\n    return () => clearInterval(interval);\n  }, []);\n  const getStepColor = status => {\n    switch (status) {\n      case 'completed':\n        return 'text-tech-green border-tech-green bg-tech-green/10';\n      case 'active':\n        return 'text-tech-cyan border-tech-cyan bg-tech-cyan/10 animate-glow';\n      case 'error':\n        return 'text-red-400 border-red-400 bg-red-400/10';\n      default:\n        return 'text-gray-400 border-gray-600 bg-gray-600/10';\n    }\n  };\n  const getConnectorColor = (currentStatus, nextStatus) => {\n    if (currentStatus === 'completed') {\n      return 'bg-tech-green';\n    } else if (currentStatus === 'active') {\n      return 'bg-gradient-to-b from-tech-cyan to-gray-600';\n    }\n    return 'bg-gray-600';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"tech-card p-6 h-full flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-3 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"p-2 bg-gradient-to-r from-tech-cyan to-tech-purple rounded-lg\",\n        animate: {\n          rotate: [0, 360]\n        },\n        transition: {\n          duration: 20,\n          repeat: Infinity,\n          ease: \"linear\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Zap, {\n          className: \"w-6 h-6 text-white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-white\",\n          children: \"\\u5904\\u7406\\u6D41\\u7A0B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 text-sm\",\n          children: \"\\u5B9E\\u65F6\\u76D1\\u63A7\\u5206\\u6790\\u8FDB\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4 flex-1\",\n      children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"relative\",\n        initial: {\n          opacity: 0,\n          x: -20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        children: [index < steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute left-6 top-12 w-0.5 h-8 z-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-full h-full ${getConnectorColor(step.status, steps[index + 1].status)} transition-all duration-500`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: `relative z-10 flex items-start space-x-4 p-4 rounded-lg border transition-all duration-300 ${getStepColor(step.status)}`,\n          whileHover: {\n            scale: 1.02\n          },\n          layout: true,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex-shrink-0 p-2 rounded-lg border ${getStepColor(step.status)}`,\n            children: step.status === 'active' ? /*#__PURE__*/_jsxDEV(motion.div, {\n              animate: {\n                rotate: 360\n              },\n              transition: {\n                duration: 2,\n                repeat: Infinity,\n                ease: \"linear\"\n              },\n              children: step.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 19\n            }, this) : step.status === 'completed' ? /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0\n              },\n              animate: {\n                scale: 1\n              },\n              transition: {\n                type: \"spring\",\n                stiffness: 500,\n                damping: 30\n              },\n              children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 19\n            }, this) : step.status === 'error' ? /*#__PURE__*/_jsxDEV(AlertCircle, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 19\n            }, this) : step.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-white\",\n              children: step.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mt-1\",\n              children: step.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: step.status === 'active' && step.progress !== undefined && /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"mt-3\",\n                initial: {\n                  opacity: 0,\n                  height: 0\n                },\n                animate: {\n                  opacity: 1,\n                  height: 'auto'\n                },\n                exit: {\n                  opacity: 0,\n                  height: 0\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-xs text-gray-400 mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u5904\\u7406\\u4E2D...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [step.progress, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full bg-gray-700 rounded-full h-2\",\n                  children: /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"progress-bar h-2 rounded-full\",\n                    initial: {\n                      width: 0\n                    },\n                    animate: {\n                      width: `${step.progress}%`\n                    },\n                    transition: {\n                      duration: 0.3\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), step.status === 'completed' && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"flex items-center space-x-1 mt-2 text-xs text-tech-green\",\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Clock, {\n                className: \"w-3 h-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"\\u5B8C\\u6210\\u4E8E \", new Date().toLocaleTimeString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this)]\n      }, step.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"mt-6 p-4 bg-tech-blue/20 rounded-lg border border-tech-cyan/20\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.5\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between text-sm text-gray-400 mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u603B\\u4F53\\u8FDB\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [Math.round(steps.filter(s => s.status === 'completed').length / steps.length * 100), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full bg-gray-700 rounded-full h-2\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"progress-bar h-2 rounded-full\",\n          initial: {\n            width: 0\n          },\n          animate: {\n            width: `${steps.filter(s => s.status === 'completed').length / steps.length * 100}%`\n          },\n          transition: {\n            duration: 0.5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n};\n_s(ProcessFlow, \"aZbNzRNHWUyv/IUtncQhkUzXpDo=\");\n_c = ProcessFlow;\nexport default ProcessFlow;\nvar _c;\n$RefreshReg$(_c, \"ProcessFlow\");", "map": {"version": 3, "names": ["React", "motion", "AnimatePresence", "Upload", "CheckCircle", "Settings", "Brain", "FileText", "Download", "Clock", "AlertCircle", "Zap", "jsxDEV", "_jsxDEV", "ProcessFlow", "_s", "steps", "setSteps", "useState", "id", "title", "description", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "status", "currentProgress", "setCurrentProgress", "useEffect", "interval", "setInterval", "prevSteps", "newSteps", "activeIndex", "findIndex", "step", "progress", "undefined", "length", "clearInterval", "getStepColor", "getConnectorColor", "currentStatus", "nextStatus", "children", "div", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "map", "index", "initial", "opacity", "x", "delay", "whileHover", "scale", "layout", "type", "stiffness", "damping", "height", "exit", "width", "Date", "toLocaleTimeString", "y", "Math", "round", "filter", "s", "_c", "$RefreshReg$"], "sources": ["D:/pyworkspace/analysisFlow/frontend/src/components/ProcessFlow.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Upload,\n  CheckCircle,\n  Settings,\n  Brain,\n  FileText,\n  Download,\n  Clock,\n  AlertCircle,\n  Zap\n} from 'lucide-react';\nimport { useProcessFlowContext } from '../contexts/ProcessFlowContext';\n\nconst ProcessFlow: React.FC = () => {\n  const [steps, setSteps] = useState<ProcessStep[]>([\n    {\n      id: 'upload',\n      title: '文件上传',\n      description: '上传入户数据和投诉数据文件',\n      icon: <Upload className=\"w-6 h-6\" />,\n      status: 'pending'\n    },\n    {\n      id: 'validate',\n      title: '内容检测',\n      description: '验证文件格式和必要字段',\n      icon: <CheckCircle className=\"w-6 h-6\" />,\n      status: 'pending'\n    },\n    {\n      id: 'configure',\n      title: '解析配置',\n      description: '配置智能解析规则',\n      icon: <Settings className=\"w-6 h-6\" />,\n      status: 'pending'\n    },\n    {\n      id: 'analyze',\n      title: '智能分析',\n      description: '提取转写文本并进行智能解析',\n      icon: <Brain className=\"w-6 h-6\" />,\n      status: 'pending'\n    },\n    {\n      id: 'generate',\n      title: '报表生成',\n      description: '生成分析报表和可视化图表',\n      icon: <FileText className=\"w-6 h-6\" />,\n      status: 'pending'\n    },\n    {\n      id: 'download',\n      title: '结果下载',\n      description: '下载分析结果和报表文件',\n      icon: <Download className=\"w-6 h-6\" />,\n      status: 'pending'\n    }\n  ]);\n\n  const [currentProgress, setCurrentProgress] = useState<string>('');\n\n  // 模拟流程进度更新\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setSteps(prevSteps => {\n        const newSteps = [...prevSteps];\n        const activeIndex = newSteps.findIndex(step => step.status === 'active');\n        \n        if (activeIndex !== -1) {\n          // 更新当前活动步骤的进度\n          if (newSteps[activeIndex].progress === undefined) {\n            newSteps[activeIndex].progress = 0;\n          }\n          newSteps[activeIndex].progress! += 10;\n          \n          if (newSteps[activeIndex].progress! >= 100) {\n            newSteps[activeIndex].status = 'completed';\n            newSteps[activeIndex].progress = 100;\n            \n            // 激活下一个步骤\n            if (activeIndex + 1 < newSteps.length) {\n              newSteps[activeIndex + 1].status = 'active';\n              newSteps[activeIndex + 1].progress = 0;\n            }\n          }\n        }\n        \n        return newSteps;\n      });\n    }, 500);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const getStepColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'text-tech-green border-tech-green bg-tech-green/10';\n      case 'active':\n        return 'text-tech-cyan border-tech-cyan bg-tech-cyan/10 animate-glow';\n      case 'error':\n        return 'text-red-400 border-red-400 bg-red-400/10';\n      default:\n        return 'text-gray-400 border-gray-600 bg-gray-600/10';\n    }\n  };\n\n  const getConnectorColor = (currentStatus: string, nextStatus: string) => {\n    if (currentStatus === 'completed') {\n      return 'bg-tech-green';\n    } else if (currentStatus === 'active') {\n      return 'bg-gradient-to-b from-tech-cyan to-gray-600';\n    }\n    return 'bg-gray-600';\n  };\n\n  return (\n    <div className=\"tech-card p-6 h-full flex flex-col\">\n      <div className=\"flex items-center space-x-3 mb-6\">\n        <motion.div\n          className=\"p-2 bg-gradient-to-r from-tech-cyan to-tech-purple rounded-lg\"\n          animate={{ rotate: [0, 360] }}\n          transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n        >\n          <Zap className=\"w-6 h-6 text-white\" />\n        </motion.div>\n        <div>\n          <h2 className=\"text-xl font-bold text-white\">处理流程</h2>\n          <p className=\"text-gray-400 text-sm\">实时监控分析进度</p>\n        </div>\n      </div>\n\n      <div className=\"space-y-4 flex-1\">\n        {steps.map((step, index) => (\n          <motion.div\n            key={step.id}\n            className=\"relative\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: index * 0.1 }}\n          >\n            {/* 连接线 */}\n            {index < steps.length - 1 && (\n              <div className=\"absolute left-6 top-12 w-0.5 h-8 z-0\">\n                <div \n                  className={`w-full h-full ${getConnectorColor(step.status, steps[index + 1].status)} transition-all duration-500`}\n                />\n              </div>\n            )}\n\n            {/* 步骤卡片 */}\n            <motion.div\n              className={`relative z-10 flex items-start space-x-4 p-4 rounded-lg border transition-all duration-300 ${getStepColor(step.status)}`}\n              whileHover={{ scale: 1.02 }}\n              layout\n            >\n              {/* 图标 */}\n              <div className={`flex-shrink-0 p-2 rounded-lg border ${getStepColor(step.status)}`}>\n                {step.status === 'active' ? (\n                  <motion.div\n                    animate={{ rotate: 360 }}\n                    transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n                  >\n                    {step.icon}\n                  </motion.div>\n                ) : step.status === 'completed' ? (\n                  <motion.div\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                  >\n                    <CheckCircle className=\"w-6 h-6\" />\n                  </motion.div>\n                ) : step.status === 'error' ? (\n                  <AlertCircle className=\"w-6 h-6\" />\n                ) : (\n                  step.icon\n                )}\n              </div>\n\n              {/* 内容 */}\n              <div className=\"flex-1 min-w-0\">\n                <h3 className=\"font-medium text-white\">{step.title}</h3>\n                <p className=\"text-sm text-gray-400 mt-1\">{step.description}</p>\n                \n                {/* 进度条 */}\n                <AnimatePresence>\n                  {step.status === 'active' && step.progress !== undefined && (\n                    <motion.div\n                      className=\"mt-3\"\n                      initial={{ opacity: 0, height: 0 }}\n                      animate={{ opacity: 1, height: 'auto' }}\n                      exit={{ opacity: 0, height: 0 }}\n                    >\n                      <div className=\"flex items-center justify-between text-xs text-gray-400 mb-1\">\n                        <span>处理中...</span>\n                        <span>{step.progress}%</span>\n                      </div>\n                      <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                        <motion.div\n                          className=\"progress-bar h-2 rounded-full\"\n                          initial={{ width: 0 }}\n                          animate={{ width: `${step.progress}%` }}\n                          transition={{ duration: 0.3 }}\n                        />\n                      </div>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n\n                {/* 时间戳 */}\n                {step.status === 'completed' && (\n                  <motion.div\n                    className=\"flex items-center space-x-1 mt-2 text-xs text-tech-green\"\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                  >\n                    <Clock className=\"w-3 h-3\" />\n                    <span>完成于 {new Date().toLocaleTimeString()}</span>\n                  </motion.div>\n                )}\n              </div>\n            </motion.div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* 总体进度 */}\n      <motion.div\n        className=\"mt-6 p-4 bg-tech-blue/20 rounded-lg border border-tech-cyan/20\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.5 }}\n      >\n        <div className=\"flex items-center justify-between text-sm text-gray-400 mb-2\">\n          <span>总体进度</span>\n          <span>{Math.round((steps.filter(s => s.status === 'completed').length / steps.length) * 100)}%</span>\n        </div>\n        <div className=\"w-full bg-gray-700 rounded-full h-2\">\n          <motion.div\n            className=\"progress-bar h-2 rounded-full\"\n            initial={{ width: 0 }}\n            animate={{ width: `${(steps.filter(s => s.status === 'completed').length / steps.length) * 100}%` }}\n            transition={{ duration: 0.5 }}\n          />\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default ProcessFlow;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,MAAM,EACNC,WAAW,EACXC,QAAQ,EACRC,KAAK,EACLC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,GAAG,QACE,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGtB,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGC,QAAQ,CAAgB,CAChD;IACEC,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,eAAe;IAC5BC,IAAI,eAAET,OAAA,CAACV,MAAM;MAACoB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpCC,MAAM,EAAE;EACV,CAAC,EACD;IACET,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,aAAa;IAC1BC,IAAI,eAAET,OAAA,CAACT,WAAW;MAACmB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzCC,MAAM,EAAE;EACV,CAAC,EACD;IACET,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,UAAU;IACvBC,IAAI,eAAET,OAAA,CAACR,QAAQ;MAACkB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtCC,MAAM,EAAE;EACV,CAAC,EACD;IACET,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,eAAe;IAC5BC,IAAI,eAAET,OAAA,CAACP,KAAK;MAACiB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnCC,MAAM,EAAE;EACV,CAAC,EACD;IACET,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,cAAc;IAC3BC,IAAI,eAAET,OAAA,CAACN,QAAQ;MAACgB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtCC,MAAM,EAAE;EACV,CAAC,EACD;IACET,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,aAAa;IAC1BC,IAAI,eAAET,OAAA,CAACL,QAAQ;MAACe,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtCC,MAAM,EAAE;EACV,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGZ,QAAQ,CAAS,EAAE,CAAC;;EAElE;EACAa,SAAS,CAAC,MAAM;IACd,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjChB,QAAQ,CAACiB,SAAS,IAAI;QACpB,MAAMC,QAAQ,GAAG,CAAC,GAAGD,SAAS,CAAC;QAC/B,MAAME,WAAW,GAAGD,QAAQ,CAACE,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACV,MAAM,KAAK,QAAQ,CAAC;QAExE,IAAIQ,WAAW,KAAK,CAAC,CAAC,EAAE;UACtB;UACA,IAAID,QAAQ,CAACC,WAAW,CAAC,CAACG,QAAQ,KAAKC,SAAS,EAAE;YAChDL,QAAQ,CAACC,WAAW,CAAC,CAACG,QAAQ,GAAG,CAAC;UACpC;UACAJ,QAAQ,CAACC,WAAW,CAAC,CAACG,QAAQ,IAAK,EAAE;UAErC,IAAIJ,QAAQ,CAACC,WAAW,CAAC,CAACG,QAAQ,IAAK,GAAG,EAAE;YAC1CJ,QAAQ,CAACC,WAAW,CAAC,CAACR,MAAM,GAAG,WAAW;YAC1CO,QAAQ,CAACC,WAAW,CAAC,CAACG,QAAQ,GAAG,GAAG;;YAEpC;YACA,IAAIH,WAAW,GAAG,CAAC,GAAGD,QAAQ,CAACM,MAAM,EAAE;cACrCN,QAAQ,CAACC,WAAW,GAAG,CAAC,CAAC,CAACR,MAAM,GAAG,QAAQ;cAC3CO,QAAQ,CAACC,WAAW,GAAG,CAAC,CAAC,CAACG,QAAQ,GAAG,CAAC;YACxC;UACF;QACF;QAEA,OAAOJ,QAAQ;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMO,aAAa,CAACV,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,YAAY,GAAIf,MAAc,IAAK;IACvC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,oDAAoD;MAC7D,KAAK,QAAQ;QACX,OAAO,8DAA8D;MACvE,KAAK,OAAO;QACV,OAAO,2CAA2C;MACpD;QACE,OAAO,8CAA8C;IACzD;EACF,CAAC;EAED,MAAMgB,iBAAiB,GAAGA,CAACC,aAAqB,EAAEC,UAAkB,KAAK;IACvE,IAAID,aAAa,KAAK,WAAW,EAAE;MACjC,OAAO,eAAe;IACxB,CAAC,MAAM,IAAIA,aAAa,KAAK,QAAQ,EAAE;MACrC,OAAO,6CAA6C;IACtD;IACA,OAAO,aAAa;EACtB,CAAC;EAED,oBACEhC,OAAA;IAAKU,SAAS,EAAC,oCAAoC;IAAAwB,QAAA,gBACjDlC,OAAA;MAAKU,SAAS,EAAC,kCAAkC;MAAAwB,QAAA,gBAC/ClC,OAAA,CAACZ,MAAM,CAAC+C,GAAG;QACTzB,SAAS,EAAC,+DAA+D;QACzE0B,OAAO,EAAE;UAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG;QAAE,CAAE;QAC9BC,UAAU,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEC,MAAM,EAAEC,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAE;QAAAR,QAAA,eAE/DlC,OAAA,CAACF,GAAG;UAACY,SAAS,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACbd,OAAA;QAAAkC,QAAA,gBACElC,OAAA;UAAIU,SAAS,EAAC,8BAA8B;UAAAwB,QAAA,EAAC;QAAI;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDd,OAAA;UAAGU,SAAS,EAAC,uBAAuB;UAAAwB,QAAA,EAAC;QAAQ;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENd,OAAA;MAAKU,SAAS,EAAC,kBAAkB;MAAAwB,QAAA,EAC9B/B,KAAK,CAACwC,GAAG,CAAC,CAAClB,IAAI,EAAEmB,KAAK,kBACrB5C,OAAA,CAACZ,MAAM,CAAC+C,GAAG;QAETzB,SAAS,EAAC,UAAU;QACpBmC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCX,OAAO,EAAE;UAAEU,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BT,UAAU,EAAE;UAAEU,KAAK,EAAEJ,KAAK,GAAG;QAAI,CAAE;QAAAV,QAAA,GAGlCU,KAAK,GAAGzC,KAAK,CAACyB,MAAM,GAAG,CAAC,iBACvB5B,OAAA;UAAKU,SAAS,EAAC,sCAAsC;UAAAwB,QAAA,eACnDlC,OAAA;YACEU,SAAS,EAAE,iBAAiBqB,iBAAiB,CAACN,IAAI,CAACV,MAAM,EAAEZ,KAAK,CAACyC,KAAK,GAAG,CAAC,CAAC,CAAC7B,MAAM,CAAC;UAA+B;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGDd,OAAA,CAACZ,MAAM,CAAC+C,GAAG;UACTzB,SAAS,EAAE,8FAA8FoB,YAAY,CAACL,IAAI,CAACV,MAAM,CAAC,EAAG;UACrIkC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,MAAM;UAAAjB,QAAA,gBAGNlC,OAAA;YAAKU,SAAS,EAAE,uCAAuCoB,YAAY,CAACL,IAAI,CAACV,MAAM,CAAC,EAAG;YAAAmB,QAAA,EAChFT,IAAI,CAACV,MAAM,KAAK,QAAQ,gBACvBf,OAAA,CAACZ,MAAM,CAAC+C,GAAG;cACTC,OAAO,EAAE;gBAAEC,MAAM,EAAE;cAAI,CAAE;cACzBC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,CAAC;gBAAEC,MAAM,EAAEC,QAAQ;gBAAEC,IAAI,EAAE;cAAS,CAAE;cAAAR,QAAA,EAE7DT,IAAI,CAAChB;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,GACXW,IAAI,CAACV,MAAM,KAAK,WAAW,gBAC7Bf,OAAA,CAACZ,MAAM,CAAC+C,GAAG;cACTU,OAAO,EAAE;gBAAEK,KAAK,EAAE;cAAE,CAAE;cACtBd,OAAO,EAAE;gBAAEc,KAAK,EAAE;cAAE,CAAE;cACtBZ,UAAU,EAAE;gBAAEc,IAAI,EAAE,QAAQ;gBAAEC,SAAS,EAAE,GAAG;gBAAEC,OAAO,EAAE;cAAG,CAAE;cAAApB,QAAA,eAE5DlC,OAAA,CAACT,WAAW;gBAACmB,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,GACXW,IAAI,CAACV,MAAM,KAAK,OAAO,gBACzBf,OAAA,CAACH,WAAW;cAACa,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAEnCW,IAAI,CAAChB;UACN;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNd,OAAA;YAAKU,SAAS,EAAC,gBAAgB;YAAAwB,QAAA,gBAC7BlC,OAAA;cAAIU,SAAS,EAAC,wBAAwB;cAAAwB,QAAA,EAAET,IAAI,CAAClB;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxDd,OAAA;cAAGU,SAAS,EAAC,4BAA4B;cAAAwB,QAAA,EAAET,IAAI,CAACjB;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAGhEd,OAAA,CAACX,eAAe;cAAA6C,QAAA,EACbT,IAAI,CAACV,MAAM,KAAK,QAAQ,IAAIU,IAAI,CAACC,QAAQ,KAAKC,SAAS,iBACtD3B,OAAA,CAACZ,MAAM,CAAC+C,GAAG;gBACTzB,SAAS,EAAC,MAAM;gBAChBmC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAES,MAAM,EAAE;gBAAE,CAAE;gBACnCnB,OAAO,EAAE;kBAAEU,OAAO,EAAE,CAAC;kBAAES,MAAM,EAAE;gBAAO,CAAE;gBACxCC,IAAI,EAAE;kBAAEV,OAAO,EAAE,CAAC;kBAAES,MAAM,EAAE;gBAAE,CAAE;gBAAArB,QAAA,gBAEhClC,OAAA;kBAAKU,SAAS,EAAC,8DAA8D;kBAAAwB,QAAA,gBAC3ElC,OAAA;oBAAAkC,QAAA,EAAM;kBAAM;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnBd,OAAA;oBAAAkC,QAAA,GAAOT,IAAI,CAACC,QAAQ,EAAC,GAAC;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACNd,OAAA;kBAAKU,SAAS,EAAC,qCAAqC;kBAAAwB,QAAA,eAClDlC,OAAA,CAACZ,MAAM,CAAC+C,GAAG;oBACTzB,SAAS,EAAC,+BAA+B;oBACzCmC,OAAO,EAAE;sBAAEY,KAAK,EAAE;oBAAE,CAAE;oBACtBrB,OAAO,EAAE;sBAAEqB,KAAK,EAAE,GAAGhC,IAAI,CAACC,QAAQ;oBAAI,CAAE;oBACxCY,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC,EAGjBW,IAAI,CAACV,MAAM,KAAK,WAAW,iBAC1Bf,OAAA,CAACZ,MAAM,CAAC+C,GAAG;cACTzB,SAAS,EAAC,0DAA0D;cACpEmC,OAAO,EAAE;gBAAEC,OAAO,EAAE;cAAE,CAAE;cACxBV,OAAO,EAAE;gBAAEU,OAAO,EAAE;cAAE,CAAE;cAAAZ,QAAA,gBAExBlC,OAAA,CAACJ,KAAK;gBAACc,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7Bd,OAAA;gBAAAkC,QAAA,GAAM,qBAAI,EAAC,IAAIwB,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA,GAvFRW,IAAI,CAACnB,EAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAwFF,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNd,OAAA,CAACZ,MAAM,CAAC+C,GAAG;MACTzB,SAAS,EAAC,gEAAgE;MAC1EmC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEc,CAAC,EAAE;MAAG,CAAE;MAC/BxB,OAAO,EAAE;QAAEU,OAAO,EAAE,CAAC;QAAEc,CAAC,EAAE;MAAE,CAAE;MAC9BtB,UAAU,EAAE;QAAEU,KAAK,EAAE;MAAI,CAAE;MAAAd,QAAA,gBAE3BlC,OAAA;QAAKU,SAAS,EAAC,8DAA8D;QAAAwB,QAAA,gBAC3ElC,OAAA;UAAAkC,QAAA,EAAM;QAAI;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjBd,OAAA;UAAAkC,QAAA,GAAO2B,IAAI,CAACC,KAAK,CAAE3D,KAAK,CAAC4D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjD,MAAM,KAAK,WAAW,CAAC,CAACa,MAAM,GAAGzB,KAAK,CAACyB,MAAM,GAAI,GAAG,CAAC,EAAC,GAAC;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClG,CAAC,eACNd,OAAA;QAAKU,SAAS,EAAC,qCAAqC;QAAAwB,QAAA,eAClDlC,OAAA,CAACZ,MAAM,CAAC+C,GAAG;UACTzB,SAAS,EAAC,+BAA+B;UACzCmC,OAAO,EAAE;YAAEY,KAAK,EAAE;UAAE,CAAE;UACtBrB,OAAO,EAAE;YAAEqB,KAAK,EAAE,GAAItD,KAAK,CAAC4D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjD,MAAM,KAAK,WAAW,CAAC,CAACa,MAAM,GAAGzB,KAAK,CAACyB,MAAM,GAAI,GAAG;UAAI,CAAE;UACpGU,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI;QAAE;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACZ,EAAA,CA5OID,WAAqB;AAAAgE,EAAA,GAArBhE,WAAqB;AA8O3B,eAAeA,WAAW;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}