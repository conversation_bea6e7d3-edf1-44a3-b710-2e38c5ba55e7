{"name": "analysis-flow-frontend", "version": "1.0.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.0", "@types/node": "^16.18.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/xlsx": "^0.0.35", "axios": "^1.6.0", "clsx": "^2.0.0", "framer-motion": "^10.16.0", "lucide-react": "^0.292.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.0", "react-scripts": "5.0.1", "tailwindcss": "^3.3.0", "typescript": "^4.9.0", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^27.5.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}, "proxy": "http://localhost:8000"}