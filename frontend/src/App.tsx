import React from 'react';
import { motion } from 'framer-motion';
import ProcessFlow from './components/ProcessFlow';
import FileUploadSimple from './components/FileUploadSimple';
import Header from './components/Header';
import { ProcessFlowProvider, useProcessFlowContext } from './contexts/ProcessFlowContext';

// 内部组件，用于访问Context
const AppContent: React.FC = () => {
  const { processState } = useProcessFlowContext();

  // 检查是否有任何步骤正在处理中 - 从分析开始到结果下载完成
  const isProcessing = processState.steps.some(step =>
    (step.id === 'analyze' || step.id === 'generate' || step.id === 'download') &&
    (step.status === 'active' || step.status === 'completed')
  ) || (processState.overallProgress > 0);

  return (
    <div className="min-h-screen bg-tech-dark">
      {/* 背景网格效果 */}
      <div className="fixed inset-0 bg-tech-grid bg-tech-grid opacity-20 pointer-events-none" />

      {/* 主要内容 */}
      <div className="relative z-10">
        <Header isProcessing={isProcessing} />

          <main className="container mx-auto px-4 py-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 min-h-[calc(100vh-120px)]">
              {/* 左侧流程展示区域 */}
              <motion.div
                className="lg:col-span-1 flex flex-col"
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
              >
                <ProcessFlow />
              </motion.div>

              {/* 右侧文件上传和操作区域 */}
              <motion.div
                className="lg:col-span-2 flex flex-col"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <FileUploadSimple />
              </motion.div>
            </div>
          </main>
        </div>
      </div>
  );
};

function App() {
  return (
    <ProcessFlowProvider>
      <AppContent />
    </ProcessFlowProvider>
  );
}

export default App;
