import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useProcessFlowContext } from '../contexts/ProcessFlowContext';
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  Settings, 
  Play, 
  Download,
  Trash2,
  Eye,
  Zap
} from 'lucide-react';

interface UploadedFile {
  id: string;
  filename: string;
  file_type: 'household' | 'complaint';
  month: string;
  status: 'uploaded' | 'validated' | 'processing' | 'processed' | 'error';
  upload_time: string;
  file_path: string;
  error_message?: string;
}

const FileUploadSimple: React.FC = () => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState('2024-01');
  const [selectedFileType, setSelectedFileType] = useState<'household' | 'complaint'>('household');

  const { onFileUploaded, onFileValidation, onFileProcessing, onReportGeneration } = useProcessFlowContext();

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      Array.from(files).forEach(file => {
        const newFile: UploadedFile = {
          id: Math.random().toString(36).substr(2, 9),
          filename: file.name,
          file_type: selectedFileType,
          month: selectedMonth,
          status: 'uploaded',
          upload_time: new Date().toISOString(),
          file_path: `/uploads/${file.name}`
        };
        
        setUploadedFiles(prev => [...prev, newFile]);

        // 触发流程联动
        onFileUploaded();
      });
    }
  };

  const validateFile = (fileId: string) => {
    setUploadedFiles(prev =>
      prev.map(file =>
        file.id === fileId
          ? { ...file, status: 'validated' }
          : file
      )
    );

    // 触发验证流程联动
    onFileValidation();
  };

  const processFile = (fileId: string) => {
    setUploadedFiles(prev =>
      prev.map(file =>
        file.id === fileId
          ? { ...file, status: 'processing' }
          : file
      )
    );

    // 触发处理流程联动
    onFileProcessing();

    setTimeout(() => {
      setUploadedFiles(prev =>
        prev.map(file =>
          file.id === fileId
            ? { ...file, status: 'processed' }
            : file
        )
      );
    }, 3000);
  };

  const deleteFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const processAllFiles = () => {
    setIsProcessing(true);
    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');
    
    validatedFiles.forEach((file, index) => {
      setTimeout(() => {
        processFile(file.id);
      }, index * 1000);
    });

    setTimeout(() => {
      setIsProcessing(false);
    }, validatedFiles.length * 1000 + 2000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'uploaded': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/30';
      case 'validated': return 'text-tech-green bg-tech-green/10 border-tech-green/30';
      case 'processing': return 'text-tech-cyan bg-tech-cyan/10 border-tech-cyan/30';
      case 'processed': return 'text-tech-purple bg-tech-purple/10 border-tech-purple/30';
      case 'error': return 'text-red-400 bg-red-400/10 border-red-400/30';
      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploaded': return <Upload className="w-4 h-4" />;
      case 'validated': return <CheckCircle className="w-4 h-4" />;
      case 'processing': return <Settings className="w-4 h-4 animate-spin" />;
      case 'processed': return <FileText className="w-4 h-4" />;
      case 'error': return <AlertCircle className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6 h-full flex flex-col">
      {/* 文件上传区域 */}
      <motion.div
        className="tech-card p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-white">数据文件上传</h2>
          <div className="flex items-center space-x-2">
            <Zap className="w-5 h-5 text-tech-cyan" />
            <span className="text-tech-cyan text-sm">智能处理</span>
          </div>
        </div>

        {/* 文件类型和月份选择 */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              文件类型
            </label>
            <select
              value={selectedFileType}
              onChange={(e) => setSelectedFileType(e.target.value as 'household' | 'complaint')}
              className="tech-input w-full"
            >
              <option value="household">入户数据</option>
              <option value="complaint">投诉数据</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              月份
            </label>
            <input
              type="month"
              value={selectedMonth}
              onChange={(e) => setSelectedMonth(e.target.value)}
              className="tech-input w-full"
            />
          </div>
        </div>

        {/* 文件上传区域 */}
        <div className="border-2 border-dashed border-gray-600 hover:border-tech-cyan/50 rounded-lg p-8 text-center transition-all duration-300">
          <div className="flex flex-col items-center space-y-4">
            <div className="p-4 bg-tech-cyan/20 rounded-full">
              <Upload className="w-8 h-8 text-tech-cyan" />
            </div>
            <div>
              <p className="text-lg font-medium text-white">
                点击上传文件
              </p>
              <p className="text-sm text-gray-400 mt-1">
                支持 .xlsx, .xls 格式文件
              </p>
            </div>
            <input
              type="file"
              multiple
              accept=".xlsx,.xls"
              onChange={handleFileUpload}
              className="tech-button"
            />
          </div>
        </div>
      </motion.div>

      {/* 已上传文件列表 */}
      <motion.div
        className="tech-card p-6 flex-1 flex flex-col min-h-0"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">
            已上传文件 ({uploadedFiles.length})
          </h3>
          <div className="flex space-x-2">
            <button
              onClick={processAllFiles}
              disabled={isProcessing || uploadedFiles.filter(f => f.status === 'validated').length === 0}
              className="tech-button text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Play className="w-4 h-4 mr-2" />
              一键处理全部
            </button>
          </div>
        </div>

        <div className="space-y-3 flex-1 overflow-y-auto min-h-0">
          <AnimatePresence>
            {uploadedFiles.map((file) => (
              <motion.div
                key={file.id}
                className="tech-border p-4 rounded-lg"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                layout
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 flex-1">
                    <div className={`p-2 rounded-lg border ${getStatusColor(file.status)}`}>
                      {getStatusIcon(file.status)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-white truncate">
                        {file.filename}
                      </h4>
                      <div className="flex items-center space-x-4 text-sm text-gray-400">
                        <span>{file.file_type === 'household' ? '入户数据' : '投诉数据'}</span>
                        <span>{file.month}</span>
                        <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(file.status)}`}>
                          {file.status === 'uploaded' && '已上传'}
                          {file.status === 'validated' && '已验证'}
                          {file.status === 'processing' && '处理中'}
                          {file.status === 'processed' && '已处理'}
                          {file.status === 'error' && '错误'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {file.status === 'uploaded' && (
                      <button
                        onClick={() => validateFile(file.id)}
                        className="p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors"
                        title="验证文件"
                      >
                        <CheckCircle className="w-4 h-4" />
                      </button>
                    )}

                    {file.status === 'validated' && (
                      <button
                        onClick={() => processFile(file.id)}
                        className="p-2 text-tech-cyan hover:bg-tech-cyan/10 rounded-lg transition-colors"
                        title="智能处理"
                      >
                        <Play className="w-4 h-4" />
                      </button>
                    )}

                    {file.status === 'processed' && (
                      <>
                        <button
                          className="p-2 text-tech-purple hover:bg-tech-purple/10 rounded-lg transition-colors"
                          title="预览结果"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          className="p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors"
                          title="下载结果"
                        >
                          <Download className="w-4 h-4" />
                        </button>
                      </>
                    )}

                    <button
                      onClick={() => deleteFile(file.id)}
                      className="p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
                      title="删除文件"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {file.status === 'processing' && (
                  <motion.div
                    className="mt-3"
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                  >
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <motion.div
                        className="progress-bar h-2 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: '100%' }}
                        transition={{ duration: 3, ease: "easeInOut" }}
                      />
                    </div>
                    <p className="text-xs text-tech-cyan mt-1">正在进行智能分析...</p>
                  </motion.div>
                )}
              </motion.div>
            ))}
          </AnimatePresence>

          {uploadedFiles.length === 0 && (
            <motion.div
              className="text-center py-12 text-gray-400"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>暂无上传文件</p>
              <p className="text-sm mt-1">请先上传数据文件</p>
            </motion.div>
          )}
        </div>
      </motion.div>

      {/* 生成报表按钮 */}
      {uploadedFiles.some(f => f.status === 'processed') && (
        <motion.div
          className="tech-card p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-4">
              生成分析报表
            </h3>
            <button
              className="tech-button text-lg px-8 py-3"
              onClick={onReportGeneration}
            >
              <FileText className="w-5 h-5 mr-2" />
              生成智能分析报表
            </button>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default FileUploadSimple;
