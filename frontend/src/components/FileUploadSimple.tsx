import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useProcessFlowContext } from '../contexts/ProcessFlowContext';
import * as XLSX from 'xlsx';
import {
  Upload,
  FileText,
  CheckCircle,
  Settings,
  Play,
  Trash2,
  Terminal,
  Eye,
  X
} from 'lucide-react';
import { StreamOutput } from './StreamOutput';
import { configureStageOutput } from '../data/streamOutputs/configureStage';
import { analyzeStageOutput } from '../data/streamOutputs/analyzeStage';
import { generateStageOutput } from '../data/streamOutputs/generateStage';

interface UploadedFile {
  id: string;
  filename: string;
  file_type: string;
  size: number;
  status: 'uploaded' | 'validated' | 'processing' | 'processed' | 'error';
  upload_time: string;
  file_path: string;
  error_message?: string;
  preview_content?: string;
  excel_data?: any[][]; // Excel表格数据
  excel_headers?: string[]; // Excel表头
}

const FileUploadSimple: React.FC = () => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStreamStage, setCurrentStreamStage] = useState<'configure' | 'analyze' | 'generate' | null>(null);
  const [selectedFile, setSelectedFile] = useState<UploadedFile | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { onFileUploaded, onFileValidation, onFileProcessing, onReportGeneration, resetFlow, processState } = useProcessFlowContext();

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      for (const file of Array.from(files)) {
        const newFile: UploadedFile = {
          id: Math.random().toString(36).substr(2, 9),
          filename: file.name,
          file_type: file.type || 'text/plain',
          size: file.size,
          status: 'uploaded',
          upload_time: new Date().toISOString(),
          file_path: `/uploads/${file.name}`
        };

        // 根据文件类型读取内容用于预览
        try {
          const isExcelFile = file.name.toLowerCase().endsWith('.xlsx') ||
                             file.name.toLowerCase().endsWith('.xls') ||
                             file.name.toLowerCase().endsWith('.csv');

          if (isExcelFile) {
            const excelData = await readExcelFile(file);
            newFile.excel_data = excelData.data;
            newFile.excel_headers = excelData.headers;
          } else {
            const content = await readFileContent(file);
            newFile.preview_content = content.substring(0, 1000); // 只保存前1000字符用于预览
          }
        } catch (error) {
          console.error('读取文件内容失败:', error);
        }

        setUploadedFiles(prev => [...prev, newFile]);
        onFileUploaded(newFile.id, newFile.filename);
      }
    }
  };

  // 读取文件内容的辅助函数
  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.onerror = reject;
      reader.readAsText(file, 'UTF-8');
    });
  };

  // 读取Excel文件的辅助函数
  const readExcelFile = (file: File): Promise<{ data: any[][], headers: string[] }> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

          if (jsonData.length > 0) {
            const headers = jsonData[0] as string[];
            const data = jsonData.slice(1) as any[][];
            resolve({ data: data.slice(0, 50), headers }); // 只取前50行用于预览
          } else {
            resolve({ data: [], headers: [] });
          }
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = reject;
      reader.readAsArrayBuffer(file);
    });
  };

  const validateFile = (fileId: string) => {
    setUploadedFiles(prev => prev.map(file => 
      file.id === fileId ? { ...file, status: 'validated' } : file
    ));
    onFileValidation(fileId);
  };

  const processFile = (fileId: string) => {
    setUploadedFiles(prev => prev.map(file => 
      file.id === fileId ? { ...file, status: 'processing' } : file
    ));
    onFileProcessing(fileId);
    
    // 模拟处理完成
    setTimeout(() => {
      setUploadedFiles(prev => prev.map(file => 
        file.id === fileId ? { ...file, status: 'processed' } : file
      ));
    }, 5000);
  };

  const deleteFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
  };

  // 预览文件
  const previewFile = (file: UploadedFile) => {
    setSelectedFile(file);
    setShowPreview(true);
  };

  const processAllFiles = () => {
    setIsProcessing(true);
    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');
    
    // 开始流式输出 - 解析配置阶段
    setCurrentStreamStage('configure');
    
    // 模拟处理流程
    setTimeout(() => {
      // 解析配置完成，开始智能分析
      setCurrentStreamStage('analyze');
      
      // 同时开始处理文件
      validatedFiles.forEach((file, index) => {
        setTimeout(() => {
          processFile(file.id);
        }, index * 1000);
      });
      
      setTimeout(() => {
        // 智能分析完成，直接开始报表生成（不需要用户点击）
        setCurrentStreamStage('generate');
        onReportGeneration(); // 自动触发报表生成
        
        setTimeout(() => {
          // 所有处理完成
          setIsProcessing(false);
          setCurrentStreamStage(null);
        }, 8000); // 报表生成阶段时间
      }, 6000); // 智能分析阶段时间
    }, 4000); // 解析配置阶段时间
  };

  // 配置下载目录（可配置）
  const downloadDirectory = process.env.REACT_APP_DOWNLOAD_DIR || '/downloads';

  const downloadAnalysisReport = () => {
    // 下载预设的分析结果报表
    const link = document.createElement('a');
    link.href = `${downloadDirectory}/文本分析结果报表.pdf`; // 从配置目录下载
    link.download = '文本分析结果报表.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 下载完成后重置
    setTimeout(() => {
      setUploadedFiles([]);
      resetFlow();
    }, 1000);

    console.log('下载文本分析结果报表完成，系统已重置');
  };

  // 获取文件进度
  const getFileProgress = (fileId: string) => {
    return processState.fileProgresses.find(fp => fp.fileId === fileId);
  };

  return (
    <div className="space-y-6 h-full flex flex-col">
      {/* 顶部控制区域 */}
      <motion.div
        className="tech-card p-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-white">数据文件上传</h2>
          
          {/* 文件上传控制 */}
          <div className="flex items-center space-x-4">
            {/* 文件选择器 - 扩展长度 */}
            <div className="flex-1 max-w-md">
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept=".txt,.doc,.docx,.pdf,.xlsx,.xls,.csv"
                onChange={handleFileUpload}
                className="hidden"
              />
              <button
                onClick={() => fileInputRef.current?.click()}
                className="tech-button w-full px-6 py-3 text-base font-medium"
              >
                <Upload className="w-5 h-5 mr-3" />
                选择文本文件
              </button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* 主要内容区域 */}
      <div className="flex-1 space-y-6">
        {/* 已上传文件列表 - 横向扩充 */}
        <motion.div
          className="tech-card flex flex-col"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          style={{ height: '400px' }}
        >
          <div className="flex items-center justify-between p-4 border-b border-gray-700">
            <h3 className="font-semibold text-white">已上传文件 ({uploadedFiles.length})</h3>
            {uploadedFiles.filter(f => f.status === 'validated').length > 0 && (
              <button
                onClick={processAllFiles}
                disabled={isProcessing}
                className="tech-button text-sm px-4 py-2 disabled:opacity-50"
              >
                <Play className="w-4 h-4 mr-2" />
                {isProcessing ? '处理中...' : '一键处理全部'}
              </button>
            )}
          </div>
          
          <div className="flex-1 overflow-y-auto p-4">
            <AnimatePresence>
              {uploadedFiles.map((file) => (
                <motion.div
                  key={file.id}
                  className="bg-gray-800/50 rounded-lg p-4 mb-3 border border-gray-700"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  layout
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <FileText className="w-5 h-5 text-tech-cyan" />
                        <div>
                          <h4 className="font-medium text-white">{file.filename}</h4>
                          <p className="text-sm text-gray-400">
                            {(file.size / 1024).toFixed(1)} KB • {new Date(file.upload_time).toLocaleString()}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {/* 状态指示器 */}
                      <div className={`px-2 py-1 rounded text-xs ${
                        file.status === 'uploaded' ? 'bg-blue-900/50 text-blue-300' :
                        file.status === 'validated' ? 'bg-green-900/50 text-green-300' :
                        file.status === 'processing' ? 'bg-yellow-900/50 text-yellow-300' :
                        file.status === 'processed' ? 'bg-purple-900/50 text-purple-300' :
                        'bg-red-900/50 text-red-300'
                      }`}>
                        {file.status === 'uploaded' ? '已上传' :
                         file.status === 'validated' ? '已验证' :
                         file.status === 'processing' ? '处理中' :
                         file.status === 'processed' ? '已完成' : '错误'}
                      </div>
                      
                      {/* 操作按钮 */}
                      <button
                        onClick={() => previewFile(file)}
                        className="p-2 text-tech-purple hover:bg-tech-purple/10 rounded-lg transition-colors"
                        title="在线预览"
                      >
                        <Eye className="w-4 h-4" />
                      </button>

                      {file.status === 'uploaded' && (
                        <button
                          onClick={() => validateFile(file.id)}
                          className="p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors"
                          title="验证文件"
                        >
                          <CheckCircle className="w-4 h-4" />
                        </button>
                      )}

                      {file.status === 'validated' && (
                        <button
                          onClick={() => processFile(file.id)}
                          className="p-2 text-tech-cyan hover:bg-tech-cyan/10 rounded-lg transition-colors"
                          title="智能处理"
                        >
                          <Settings className="w-4 h-4" />
                        </button>
                      )}

                      <button
                        onClick={() => deleteFile(file.id)}
                        className="p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
                        title="删除文件"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  
                  {/* 文件级进度显示 */}
                  {(() => {
                    const fileProgress = getFileProgress(file.id);
                    if (fileProgress && fileProgress.stepProgress > 0) {
                      return (
                        <div className="mt-2">
                          <div className="flex items-center justify-between text-xs text-gray-400 mb-1">
                            <span>{fileProgress.currentStep === 'configure' ? '配置中' : 
                                   fileProgress.currentStep === 'analyze' ? '分析中' : 
                                   fileProgress.currentStep === 'generate' ? '生成中' : '处理中'}...</span>
                            <span>{fileProgress.stepProgress}%</span>
                          </div>
                          <div className="w-full bg-gray-700 rounded-full h-1.5">
                            <motion.div
                              className="progress-bar h-1.5 rounded-full"
                              initial={{ width: 0 }}
                              animate={{ width: `${fileProgress.stepProgress}%` }}
                              transition={{ duration: 0.3 }}
                            />
                          </div>
                        </div>
                      );
                    }
                    return null;
                  })()}
                </motion.div>
              ))}
            </AnimatePresence>

            {uploadedFiles.length === 0 && (
              <motion.div
                className="text-center py-12 text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>暂无上传文件</p>
                <p className="text-sm mt-1">请先上传数据文件</p>
              </motion.div>
            )}
          </div>
        </motion.div>

        {/* AI处理输出区域 - 移至原生成智能分析报表位置 */}
        <motion.div
          className="tech-card flex flex-col"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          style={{ height: '400px' }}
        >
          {currentStreamStage ? (
            <>
              {currentStreamStage === 'configure' && (
                <StreamOutput
                  content={configureStageOutput}
                  isActive={true}
                  title="解析配置阶段"
                  onComplete={() => {}}
                  speed={100}
                />
              )}
              {currentStreamStage === 'analyze' && (
                <StreamOutput
                  content={analyzeStageOutput}
                  isActive={true}
                  title="智能分析阶段"
                  onComplete={() => {}}
                  speed={120}
                />
              )}
              {currentStreamStage === 'generate' && (
                <StreamOutput
                  content={generateStageOutput}
                  isActive={true}
                  title="报表生成阶段"
                  onComplete={() => {}}
                  speed={80}
                />
              )}
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center text-gray-400">
              <div className="text-center">
                <Terminal className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>AI处理输出</p>
                <p className="text-sm">点击"一键处理全部"开始智能分析</p>
              </div>
            </div>
          )}
        </motion.div>
      </div>

      {/* 文件预览模态框 */}
      <AnimatePresence>
        {showPreview && selectedFile && (
          <motion.div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowPreview(false)}
          >
            <motion.div
              className="tech-card max-w-4xl w-full max-h-[80vh] flex flex-col"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* 预览头部 */}
              <div className="flex items-center justify-between p-4 border-b border-gray-700">
                <div>
                  <h3 className="text-lg font-semibold text-white">{selectedFile.filename}</h3>
                  <p className="text-sm text-gray-400">
                    {(selectedFile.size / 1024).toFixed(1)} KB • {new Date(selectedFile.upload_time).toLocaleString()}
                  </p>
                </div>
                <button
                  onClick={() => setShowPreview(false)}
                  className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* 预览内容 */}
              <div className="flex-1 overflow-auto p-4">
                {selectedFile.excel_data && selectedFile.excel_headers ? (
                  // Excel表格预览
                  <div className="bg-gray-900/50 rounded-lg p-4 overflow-auto">
                    <div className="text-sm text-gray-400 mb-3">
                      Excel表格预览 (显示前50行)
                    </div>
                    <div className="overflow-x-auto">
                      <table className="min-w-full text-sm">
                        <thead>
                          <tr className="bg-gray-800">
                            {selectedFile.excel_headers.map((header, index) => (
                              <th key={index} className="px-3 py-2 text-left text-gray-300 border border-gray-600">
                                {header || `列${index + 1}`}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {selectedFile.excel_data.map((row, rowIndex) => (
                            <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-gray-800/30' : 'bg-gray-800/10'}>
                              {selectedFile.excel_headers!.map((_, colIndex) => (
                                <td key={colIndex} className="px-3 py-2 text-gray-300 border border-gray-600">
                                  {row[colIndex] || ''}
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ) : (
                  // 文本文件预览
                  <div className="bg-gray-900/50 rounded-lg p-4 font-mono text-sm text-gray-300 whitespace-pre-wrap">
                    {selectedFile.preview_content || '无法预览此文件内容'}
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default FileUploadSimple;
