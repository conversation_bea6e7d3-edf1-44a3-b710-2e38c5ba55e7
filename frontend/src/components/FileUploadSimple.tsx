import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useProcessFlowContext } from '../contexts/ProcessFlowContext';
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  Settings, 
  Play, 
  Download,
  Trash2,
  Eye,
  Zap,
  Terminal
} from 'lucide-react';
import { StreamOutput } from './StreamOutput';
import { configureStageOutput } from '../data/streamOutputs/configureStage';
import { analyzeStageOutput } from '../data/streamOutputs/analyzeStage';
import { generateStageOutput } from '../data/streamOutputs/generateStage';

interface UploadedFile {
  id: string;
  filename: string;
  file_type: 'household' | 'complaint';
  month: string;
  status: 'uploaded' | 'validated' | 'processing' | 'processed' | 'error';
  upload_time: string;
  file_path: string;
  error_message?: string;
}

const FileUploadSimple: React.FC = () => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedFileType, setSelectedFileType] = useState<'household' | 'complaint'>('household');
  const [currentStreamStage, setCurrentStreamStage] = useState<'configure' | 'analyze' | 'generate' | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { onFileUploaded, onFileValidation, onFileProcessing, onReportGeneration, resetFlow, processState } = useProcessFlowContext();

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      Array.from(files).forEach(file => {
        const newFile: UploadedFile = {
          id: Math.random().toString(36).substr(2, 9),
          filename: file.name,
          file_type: selectedFileType,
          month: new Date().toISOString().slice(0, 7), // YYYY-MM格式
          status: 'uploaded',
          upload_time: new Date().toISOString(),
          file_path: `/uploads/${file.name}`
        };
        
        setUploadedFiles(prev => [...prev, newFile]);
        
        // 触发流程联动
        onFileUploaded(newFile.id, newFile.filename);
      });
    }
  };

  const validateFile = (fileId: string) => {
    setUploadedFiles(prev => 
      prev.map(file => 
        file.id === fileId 
          ? { ...file, status: 'validated' }
          : file
      )
    );
    
    // 触发验证流程联动
    onFileValidation(fileId);
  };

  const processFile = (fileId: string) => {
    setUploadedFiles(prev => 
      prev.map(file => 
        file.id === fileId 
          ? { ...file, status: 'processing' }
          : file
      )
    );

    // 触发处理流程联动
    onFileProcessing(fileId);

    setTimeout(() => {
      setUploadedFiles(prev => 
        prev.map(file => 
          file.id === fileId 
            ? { ...file, status: 'processed' }
            : file
        )
      );
    }, 3000);
  };

  const deleteFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const processAllFiles = () => {
    setIsProcessing(true);
    const validatedFiles = uploadedFiles.filter(file => file.status === 'validated');
    
    // 开始流式输出 - 解析配置阶段
    setCurrentStreamStage('configure');
    
    // 模拟处理流程
    setTimeout(() => {
      // 解析配置完成，开始智能分析
      setCurrentStreamStage('analyze');
      
      // 同时开始处理文件
      validatedFiles.forEach((file, index) => {
        setTimeout(() => {
          processFile(file.id);
        }, index * 1000);
      });
      
      setTimeout(() => {
        // 智能分析完成，开始报表生成
        setCurrentStreamStage('generate');
        
        setTimeout(() => {
          // 所有处理完成
          setIsProcessing(false);
          setCurrentStreamStage(null);
        }, 8000); // 报表生成阶段时间
      }, 6000); // 智能分析阶段时间
    }, 4000); // 解析配置阶段时间
  };

  // 下载分析结果
  const downloadAnalysisResult = (fileName: string, fileType: 'excel' | 'pdf' | 'json') => {
    // 模拟下载文件
    const link = document.createElement('a');
    link.href = '#'; // 实际项目中这里应该是后端API地址
    link.download = `${fileName}_analysis_result.${fileType}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 显示下载成功提示
    console.log(`下载 ${fileName} 的${fileType.toUpperCase()}分析结果`);
  };

  // 下载所有分析结果
  const downloadAllResults = () => {
    const processedFiles = uploadedFiles.filter(f => f.status === 'processed');
    
    processedFiles.forEach(file => {
      downloadAnalysisResult(file.filename, 'excel');
    });
    
    // 下载完成后重置流程
    setTimeout(() => {
      resetFlow();
      setUploadedFiles([]);
    }, 1000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'uploaded': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/30';
      case 'validated': return 'text-green-400 bg-green-400/10 border-green-400/30';
      case 'processing': return 'text-blue-400 bg-blue-400/10 border-blue-400/30';
      case 'processed': return 'text-purple-400 bg-purple-400/10 border-purple-400/30';
      case 'error': return 'text-red-400 bg-red-400/10 border-red-400/30';
      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploaded': return <Upload className="w-4 h-4" />;
      case 'validated': return <CheckCircle className="w-4 h-4" />;
      case 'processing': return <Settings className="w-4 h-4 animate-spin" />;
      case 'processed': return <FileText className="w-4 h-4" />;
      case 'error': return <AlertCircle className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  // 获取文件的流程进度信息
  const getFileProgress = (fileId: string) => {
    return processState.fileProgresses.find(fp => fp.fileId === fileId);
  };

  return (
    <div className="space-y-6 h-full flex flex-col">
      {/* 顶部控制区域 */}
      <motion.div
        className="tech-card p-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-white">数据文件上传</h2>
          
          {/* 文件上传控制 */}
          <div className="flex items-center space-x-4">
            {/* 文件类型选择 */}
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-300">类型:</label>
              <select
                value={selectedFileType}
                onChange={(e) => setSelectedFileType(e.target.value as 'household' | 'complaint')}
                className="tech-input text-sm px-3 py-1"
              >
                <option value="household">入户数据</option>
                <option value="complaint">投诉数据</option>
              </select>
            </div>
            
            {/* 上传按钮 */}
            <div>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept=".xlsx,.xls,.csv"
                onChange={handleFileUpload}
                className="hidden"
              />
              <button
                onClick={() => fileInputRef.current?.click()}
                className="tech-button text-sm px-4 py-2"
              >
                <Upload className="w-4 h-4 mr-2" />
                选择文件
              </button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* 主要内容区域 */}
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：已上传文件列表 */}
        <motion.div
          className="tech-card flex flex-col"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          style={{ height: '400px' }}
        >
          <div className="flex items-center justify-between p-4 border-b border-gray-700">
            <h3 className="font-semibold text-white">已上传文件 ({uploadedFiles.length})</h3>
            <div className="flex space-x-2">
              <button
                onClick={processAllFiles}
                disabled={isProcessing || uploadedFiles.filter(f => f.status === 'validated').length === 0}
                className="tech-button text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Play className="w-4 h-4 mr-2" />
                一键处理全部
              </button>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto space-y-3 p-4" style={{ maxHeight: '300px' }}>
            <AnimatePresence>
              {uploadedFiles.map((file) => (
                <motion.div
                  key={file.id}
                  className="tech-border p-4 rounded-lg"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  layout
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 flex-1">
                      <div className={`p-2 rounded-lg border ${getStatusColor(file.status)}`}>
                        {getStatusIcon(file.status)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-white truncate">
                          {file.filename}
                        </h4>
                        <div className="flex items-center space-x-4 text-sm text-gray-400">
                          <span>{file.file_type === 'household' ? '入户数据' : '投诉数据'}</span>
                          <span>{file.month}</span>
                          <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(file.status)}`}>
                            {file.status === 'uploaded' && '已上传'}
                            {file.status === 'validated' && '已验证'}
                            {file.status === 'processing' && '处理中'}
                            {file.status === 'processed' && '已处理'}
                            {file.status === 'error' && '错误'}
                          </span>
                        </div>
                        
                        {/* 文件级进度显示 */}
                        {(() => {
                          const fileProgress = getFileProgress(file.id);
                          if (fileProgress && fileProgress.stepProgress > 0) {
                            return (
                              <div className="mt-2">
                                <div className="flex justify-between text-xs text-gray-400 mb-1">
                                  <span>当前步骤: {fileProgress.currentStep}</span>
                                  <span>{fileProgress.stepProgress}%</span>
                                </div>
                                <div className="w-full bg-gray-700 rounded-full h-1">
                                  <motion.div
                                    className="bg-gradient-to-r from-blue-500 to-cyan-400 h-1 rounded-full"
                                    initial={{ width: 0 }}
                                    animate={{ width: `${fileProgress.stepProgress}%` }}
                                    transition={{ duration: 0.3 }}
                                  />
                                </div>
                              </div>
                            );
                          }
                          return null;
                        })()}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {file.status === 'uploaded' && (
                        <button
                          onClick={() => validateFile(file.id)}
                          className="p-2 text-tech-green hover:bg-tech-green/10 rounded-lg transition-colors"
                          title="验证文件"
                        >
                          <CheckCircle className="w-4 h-4" />
                        </button>
                      )}
                      
                      {file.status === 'validated' && (
                        <button
                          onClick={() => processFile(file.id)}
                          className="p-2 text-tech-blue hover:bg-tech-blue/10 rounded-lg transition-colors"
                          title="处理文件"
                        >
                          <Settings className="w-4 h-4" />
                        </button>
                      )}
                      
                      <button
                        onClick={() => deleteFile(file.id)}
                        className="p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
                        title="删除文件"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>

            {uploadedFiles.length === 0 && (
              <div className="flex-1 flex items-center justify-center text-gray-400">
                <div className="text-center">
                  <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>暂无上传文件</p>
                  <p className="text-sm">请先上传数据文件</p>
                </div>
              </div>
            )}
          </div>
        </motion.div>

        {/* 右侧：流式输出区域 */}
        <motion.div
          className="tech-card flex flex-col"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          style={{ height: '400px' }}
        >
          {currentStreamStage ? (
            <>
              {currentStreamStage === 'configure' && (
                <StreamOutput
                  content={configureStageOutput}
                  isActive={true}
                  title="解析配置阶段"
                  onComplete={() => {}}
                  speed={100}
                />
              )}
              {currentStreamStage === 'analyze' && (
                <StreamOutput
                  content={analyzeStageOutput}
                  isActive={true}
                  title="智能分析阶段"
                  onComplete={() => {}}
                  speed={120}
                />
              )}
              {currentStreamStage === 'generate' && (
                <StreamOutput
                  content={generateStageOutput}
                  isActive={true}
                  title="报表生成阶段"
                  onComplete={() => {}}
                  speed={80}
                />
              )}
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center text-gray-400">
              <div className="text-center">
                <Terminal className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>AI处理输出</p>
                <p className="text-sm">点击"一键处理全部"开始智能分析</p>
              </div>
            </div>
          )}
        </motion.div>
      </div>

      {/* 底部操作区域 */}
      <motion.div
        className="tech-card p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <div className="flex items-center justify-center">
          <div className="flex flex-col space-y-4">
            <button
              className="tech-button text-lg px-8 py-3"
              onClick={onReportGeneration}
            >
              <FileText className="w-5 h-5 mr-2" />
              生成智能分析报表
            </button>

            {/* 分析结果下载区域 */}
            {uploadedFiles.some(f => f.status === 'processed') && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="tech-card p-4 bg-green-900/20 border-green-500/30"
              >
                <h4 className="text-green-400 font-semibold mb-3 flex items-center">
                  <CheckCircle className="w-5 h-5 mr-2" />
                  分析结果下载
                </h4>

                <div className="space-y-2">
                  {uploadedFiles.filter(f => f.status === 'processed').map(file => (
                    <div key={file.id} className="flex items-center justify-between bg-gray-800/50 p-3 rounded-lg">
                      <span className="text-gray-300">{file.filename}</span>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => downloadAnalysisResult(file.filename, 'excel')}
                          className="text-xs bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded transition-colors"
                        >
                          <Download className="w-3 h-3 mr-1 inline" />
                          Excel
                        </button>
                        <button
                          onClick={() => downloadAnalysisResult(file.filename, 'pdf')}
                          className="text-xs bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded transition-colors"
                        >
                          <Download className="w-3 h-3 mr-1 inline" />
                          PDF
                        </button>
                        <button
                          onClick={() => downloadAnalysisResult(file.filename, 'json')}
                          className="text-xs bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded transition-colors"
                        >
                          <Download className="w-3 h-3 mr-1 inline" />
                          JSON
                        </button>
                      </div>
                    </div>
                  ))}

                  <button
                    onClick={downloadAllResults}
                    className="w-full mt-4 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-2 px-4 rounded-lg transition-all duration-300 flex items-center justify-center"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    下载所有分析结果并重置
                  </button>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default FileUploadSimple;
