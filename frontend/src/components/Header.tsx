import React from 'react';
import { motion } from 'framer-motion';
import { Brain, Zap } from 'lucide-react';

const Header: React.FC = () => {
  return (
    <motion.header 
      className="bg-tech-blue/30 backdrop-blur-sm border-b border-tech-cyan/20"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <motion.div
              className="p-2 bg-gradient-to-r from-tech-cyan to-tech-purple rounded-lg"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Brain className="w-8 h-8 text-white" />
            </motion.div>
            <div>
              <h1 className="text-2xl font-bold glow-text">
                智能文本分析工具
              </h1>
              <p className="text-gray-400 text-sm">
                Intelligent Text Analysis Tool
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <motion.div 
              className="flex items-center space-x-2 text-tech-cyan"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <Zap className="w-5 h-5" />
              <span className="text-sm font-medium">AI 智能分析</span>
            </motion.div>
            
            <div className="text-right">
              <div className="text-sm text-gray-400">状态</div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-tech-green rounded-full animate-pulse"></div>
                <span className="text-tech-green text-sm font-medium">系统正常</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.header>
  );
};

export default Header;
