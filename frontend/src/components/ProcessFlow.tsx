import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Upload,
  CheckCircle,
  Settings,
  Brain,
  FileText,
  Download,
  Clock,
  AlertCircle,
  Zap
} from 'lucide-react';
import { useProcessFlowContext } from '../contexts/ProcessFlowContext';

const ProcessFlow: React.FC = () => {
  const { processState } = useProcessFlowContext();

  // 检查是否有已处理的文件（用于显示下载界面）
  const hasProcessedFiles = true; // 简化逻辑，总是显示下载选项

  // 配置下载目录（可配置）
  const downloadDirectory = process.env.REACT_APP_DOWNLOAD_DIR || '/downloads';

  // 下载分析结果报表
  const downloadAnalysisReport = () => {
    // 下载预设的分析结果报表
    const link = document.createElement('a');
    link.href = `${downloadDirectory}/文本分析结果报表.pdf`; // 从配置目录下载
    link.download = '文本分析结果报表.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log('下载文本分析结果报表完成');
  };

  const getStepIcon = (stepId: string) => {
    switch (stepId) {
      case 'upload': return <Upload className="w-6 h-6" />;
      case 'validate': return <CheckCircle className="w-6 h-6" />;
      case 'configure': return <Settings className="w-6 h-6" />;
      case 'analyze': return <Brain className="w-6 h-6" />;
      case 'generate': return <FileText className="w-6 h-6" />;
      case 'download': return <Download className="w-6 h-6" />;
      default: return <FileText className="w-6 h-6" />;
    }
  };



  const getStepColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-tech-green border-tech-green bg-tech-green/10';
      case 'active':
        return 'text-tech-cyan border-tech-cyan bg-tech-cyan/10 animate-glow';
      case 'error':
        return 'text-red-400 border-red-400 bg-red-400/10';
      default:
        return 'text-gray-400 border-gray-600 bg-gray-600/10';
    }
  };

  const getConnectorColor = (currentStatus: string, nextStatus: string) => {
    if (currentStatus === 'completed') {
      return 'bg-tech-green';
    } else if (currentStatus === 'active') {
      return 'bg-gradient-to-b from-tech-cyan to-gray-600';
    }
    return 'bg-gray-600';
  };

  return (
    <div className="tech-card p-6 h-full flex flex-col">
      <div className="flex items-center space-x-3 mb-6">
        <motion.div
          className="p-2 bg-gradient-to-r from-tech-cyan to-tech-purple rounded-lg"
          animate={{ rotate: [0, 360] }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        >
          <Zap className="w-6 h-6 text-white" />
        </motion.div>
        <div>
          <h2 className="text-xl font-bold text-white">处理流程</h2>
          <p className="text-gray-400 text-sm">实时监控分析进度</p>
        </div>
      </div>

      <div className="space-y-4 flex-1">
        {processState.steps.map((step, index) => (
          <motion.div
            key={step.id}
            className="relative"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            {/* 连接线 */}
            {index < processState.steps.length - 1 && (
              <div className="absolute left-6 top-12 w-0.5 h-8 z-0">
                <div
                  className={`w-full h-full ${getConnectorColor(step.status, processState.steps[index + 1].status)} transition-all duration-500`}
                />
              </div>
            )}

            {/* 步骤卡片 */}
            <motion.div
              className={`relative z-10 flex items-start space-x-4 p-4 rounded-lg border transition-all duration-300 ${getStepColor(step.status)}`}
              whileHover={{ scale: 1.02 }}
              layout
            >
              {/* 图标 */}
              <div className={`flex-shrink-0 p-2 rounded-lg border ${getStepColor(step.status)}`}>
                {step.status === 'active' ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  >
                    {getStepIcon(step.id)}
                  </motion.div>
                ) : step.status === 'completed' ? (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  >
                    <CheckCircle className="w-6 h-6" />
                  </motion.div>
                ) : step.status === 'error' ? (
                  <AlertCircle className="w-6 h-6" />
                ) : (
                  getStepIcon(step.id)
                )}
              </div>

              {/* 内容 */}
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-white">{step.title}</h3>
                <p className="text-sm text-gray-400 mt-1">{step.description}</p>
                
                {/* 进度条 */}
                <AnimatePresence>
                  {step.status === 'active' && step.progress !== undefined && (
                    <motion.div
                      className="mt-3"
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                    >
                      <div className="flex items-center justify-between text-xs text-gray-400 mb-1">
                        <span>处理中...</span>
                        <span>{step.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <motion.div
                          className="progress-bar h-2 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${step.progress}%` }}
                          transition={{ duration: 0.3 }}
                        />
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* 时间戳 */}
                {step.status === 'completed' && (
                  <motion.div
                    className="flex items-center space-x-1 mt-2 text-xs text-tech-green"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                  >
                    <Clock className="w-3 h-3" />
                    <span>完成于 {new Date().toLocaleTimeString()}</span>
                  </motion.div>
                )}

                {/* 结果下载步骤的特殊内容 */}
                {step.id === 'download' && step.status === 'completed' && hasProcessedFiles && (
                  <motion.div
                    className="mt-3 p-3 bg-green-900/20 border border-green-500/30 rounded-lg"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <div className="space-y-2">
                      {/* 文件名与下载按钮在一行 */}
                      <div className="bg-gray-800/50 p-2 rounded-lg flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <FileText className="w-4 h-4 text-red-400" />
                          <span className="text-gray-300 text-sm">文本分析结果报表.pdf</span>
                        </div>
                        <button
                          onClick={downloadAnalysisReport}
                          className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-1 px-3 rounded transition-all duration-300 flex items-center text-xs"
                        >
                          <Download className="w-3 h-3 mr-1" />
                          下载
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>
            </motion.div>
          </motion.div>
        ))}
      </div>

      {/* 总体进度 - 调整位置使其与右侧AI输出模块底部对齐 */}
      <motion.div
        className="mt-2 p-4 tech-card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <div className="flex items-center justify-between text-sm text-gray-400 mb-2">
          <span>总体进度</span>
          <span>{Math.round(processState.overallProgress)}%</span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2">
          <motion.div
            className="progress-bar h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${processState.overallProgress}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </motion.div>
    </div>
  );
};

export default ProcessFlow;
