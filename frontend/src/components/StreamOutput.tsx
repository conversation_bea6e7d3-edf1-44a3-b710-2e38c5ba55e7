import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Terminal, Copy, Download, Pause, Play, RotateCcw } from 'lucide-react';

interface StreamOutputProps {
  content: string[];
  isActive: boolean;
  title: string;
  onComplete?: () => void;
  speed?: number; // 每行显示间隔时间（毫秒）
}

export const StreamOutput: React.FC<StreamOutputProps> = ({
  content,
  isActive,
  title,
  onComplete,
  speed = 150
}) => {
  const [displayedLines, setDisplayedLines] = useState<string[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  const scrollToBottom = () => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  };

  // 开始流式输出
  const startStreaming = () => {
    if (currentIndex >= content.length) return;
    
    setIsPlaying(true);
    intervalRef.current = setInterval(() => {
      setCurrentIndex(prev => {
        const nextIndex = prev + 1;
        if (nextIndex >= content.length) {
          setIsPlaying(false);
          setIsCompleted(true);
          onComplete?.();
          return prev;
        }
        return nextIndex;
      });
    }, speed);
  };

  // 暂停流式输出
  const pauseStreaming = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setIsPlaying(false);
  };

  // 重置流式输出
  const resetStreaming = () => {
    pauseStreaming();
    setCurrentIndex(0);
    setDisplayedLines([]);
    setIsCompleted(false);
  };

  // 复制内容
  const copyContent = () => {
    const textContent = displayedLines.join('\n');
    navigator.clipboard.writeText(textContent);
  };

  // 下载内容
  const downloadContent = () => {
    const textContent = displayedLines.join('\n');
    const blob = new Blob([textContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title}_output.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 监听激活状态
  useEffect(() => {
    if (isActive && !isPlaying && !isCompleted) {
      startStreaming();
    }
  }, [isActive]);

  // 更新显示的行
  useEffect(() => {
    setDisplayedLines(content.slice(0, currentIndex));
    scrollToBottom();
  }, [currentIndex, content]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // 格式化文本行
  const formatLine = (line: string, index: number) => {
    // 处理Markdown样式
    if (line.startsWith('**') && line.endsWith('**')) {
      return (
        <div key={index} className="font-bold text-tech-green my-2">
          {line.slice(2, -2)}
        </div>
      );
    }
    
    if (line.startsWith('# ')) {
      return (
        <div key={index} className="text-xl font-bold text-white my-3">
          {line.slice(2)}
        </div>
      );
    }
    
    if (line.startsWith('```')) {
      return (
        <div key={index} className="bg-gray-800 border border-gray-600 rounded px-2 py-1 my-1 font-mono text-sm">
          {line === '```' ? '' : line.slice(3)}
        </div>
      );
    }
    
    if (line.startsWith('- ') || line.startsWith('• ')) {
      return (
        <div key={index} className="text-gray-300 ml-4 my-1">
          <span className="text-tech-blue mr-2">•</span>
          {line.slice(2)}
        </div>
      );
    }
    
    if (line.includes('✅') || line.includes('✓')) {
      return (
        <div key={index} className="text-green-400 my-1">
          {line}
        </div>
      );
    }
    
    if (line.includes('⚠️') || line.includes('🔴')) {
      return (
        <div key={index} className="text-yellow-400 my-1">
          {line}
        </div>
      );
    }
    
    if (line.includes('🔧') || line.includes('🎯') || line.includes('📊')) {
      return (
        <div key={index} className="text-tech-blue my-1">
          {line}
        </div>
      );
    }
    
    return (
      <div key={index} className="text-gray-300 my-1">
        {line || '\u00A0'} {/* 空行显示为不间断空格 */}
      </div>
    );
  };

  return (
    <motion.div
      className="tech-card h-full flex flex-col"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* 头部控制栏 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-700">
        <div className="flex items-center space-x-3">
          <Terminal className="w-5 h-5 text-tech-green" />
          <h3 className="font-semibold text-white">{title}</h3>
          {isPlaying && (
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-green-400">输出中...</span>
            </div>
          )}
          {isCompleted && (
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <span className="text-xs text-blue-400">已完成</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {isPlaying ? (
            <button
              onClick={pauseStreaming}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
              title="暂停"
            >
              <Pause className="w-4 h-4" />
            </button>
          ) : (
            <button
              onClick={startStreaming}
              disabled={isCompleted}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors disabled:opacity-50"
              title="播放"
            >
              <Play className="w-4 h-4" />
            </button>
          )}
          
          <button
            onClick={resetStreaming}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
            title="重置"
          >
            <RotateCcw className="w-4 h-4" />
          </button>
          
          <button
            onClick={copyContent}
            disabled={displayedLines.length === 0}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors disabled:opacity-50"
            title="复制"
          >
            <Copy className="w-4 h-4" />
          </button>
          
          <button
            onClick={downloadContent}
            disabled={displayedLines.length === 0}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors disabled:opacity-50"
            title="下载"
          >
            <Download className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* 输出内容区域 */}
      <div
        ref={containerRef}
        className="flex-1 p-4 overflow-y-auto bg-gray-900/50 font-mono text-sm leading-relaxed"
        style={{ maxHeight: '400px' }}
      >
        <AnimatePresence>
          {displayedLines.map((line, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.2 }}
            >
              {formatLine(line, index)}
            </motion.div>
          ))}
        </AnimatePresence>
        
        {/* 光标效果 */}
        {isPlaying && (
          <motion.div
            className="inline-block w-2 h-4 bg-tech-green ml-1"
            animate={{ opacity: [1, 0] }}
            transition={{ duration: 0.8, repeat: Infinity }}
          />
        )}
      </div>

      {/* 底部状态栏 */}
      <div className="px-4 py-2 border-t border-gray-700 text-xs text-gray-400">
        <div className="flex justify-between items-center">
          <span>
            行数: {displayedLines.length} / {content.length}
          </span>
          <span>
            进度: {Math.round((currentIndex / content.length) * 100)}%
          </span>
        </div>
      </div>
    </motion.div>
  );
};
