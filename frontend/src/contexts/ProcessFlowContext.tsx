import React, { createContext, useContext, ReactNode } from 'react';
import { useProcessFlow, ProcessFlowState } from '../hooks/useProcessFlow';

interface ProcessFlowContextType {
  processState: ProcessFlowState;
  activateStep: (stepId: string) => void;
  updateStepProgress: (stepId: string, progress: number) => void;
  completeStep: (stepId: string) => void;
  setStepError: (stepId: string, errorMessage?: string) => void;
  resetFlow: () => void;
  onFileUploaded: () => void;
  onFileValidation: () => void;
  onFileProcessing: () => void;
  onReportGeneration: () => void;
}

const ProcessFlowContext = createContext<ProcessFlowContextType | undefined>(undefined);

export const ProcessFlowProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const processFlow = useProcessFlow();

  return (
    <ProcessFlowContext.Provider value={processFlow}>
      {children}
    </ProcessFlowContext.Provider>
  );
};

export const useProcessFlowContext = () => {
  const context = useContext(ProcessFlowContext);
  if (context === undefined) {
    throw new Error('useProcessFlowContext must be used within a ProcessFlowProvider');
  }
  return context;
};
