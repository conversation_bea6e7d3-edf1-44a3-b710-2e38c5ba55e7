import { useState, useCallback } from 'react';

export interface ProcessStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'active' | 'completed' | 'error';
  progress?: number;
}

export interface FileProgress {
  fileId: string;
  fileName: string;
  currentStep: string;
  stepProgress: number;
}

export interface ProcessFlowState {
  currentStep: string;
  steps: ProcessStep[];
  overallProgress: number;
  fileProgresses: FileProgress[];
}

const initialSteps: ProcessStep[] = [
  {
    id: 'upload',
    title: '文件上传',
    description: '上传入户数据和投诉数据文件',
    status: 'pending'
  },
  {
    id: 'validate',
    title: '内容检测',
    description: '验证文件格式和必要字段',
    status: 'pending'
  },
  {
    id: 'configure',
    title: '解析配置',
    description: '配置智能解析规则',
    status: 'pending'
  },
  {
    id: 'analyze',
    title: '智能分析',
    description: '提取转写文本并进行智能解析',
    status: 'pending'
  },
  {
    id: 'generate',
    title: '报表生成',
    description: '生成分析报表和可视化图表',
    status: 'pending'
  },
  {
    id: 'download',
    title: '结果下载',
    description: '下载分析结果和报表文件',
    status: 'pending'
  }
];

export const useProcessFlow = () => {
  const [processState, setProcessState] = useState<ProcessFlowState>({
    currentStep: '',
    steps: initialSteps,
    overallProgress: 0,
    fileProgresses: []
  });

  // 智能分析状态
  const [analysisStage, setAnalysisStage] = useState<'configure' | 'analyze' | 'generate' | null>(null);

  // 激活某个步骤
  const activateStep = useCallback((stepId: string) => {
    setProcessState(prev => ({
      ...prev,
      currentStep: stepId,
      steps: prev.steps.map(step => ({
        ...step,
        status: step.id === stepId ? 'active' : 
                step.status === 'active' ? 'pending' : step.status,
        progress: step.id === stepId ? 0 : step.progress
      }))
    }));
  }, []);

  // 更新步骤进度
  const updateStepProgress = useCallback((stepId: string, progress: number) => {
    setProcessState(prev => ({
      ...prev,
      steps: prev.steps.map(step => 
        step.id === stepId ? { ...step, progress } : step
      )
    }));
  }, []);

  // 完成某个步骤
  const completeStep = useCallback((stepId: string) => {
    setProcessState(prev => {
      const updatedSteps = prev.steps.map(step =>
        step.id === stepId ? { ...step, status: 'completed' as const, progress: 100 } : step
      );

      // 重新计算总体进度：基于步骤完成情况和文件进度
      const completedSteps = updatedSteps.filter(s => s.status === 'completed').length;
      const activeSteps = updatedSteps.filter(s => s.status === 'active');

      // 计算活跃步骤的平均进度
      const activeStepsProgress = activeSteps.reduce((sum, step) => sum + step.progress, 0);
      const totalStepsProgress = completedSteps * 100 + activeStepsProgress;
      const overallProgress = Math.round(totalStepsProgress / updatedSteps.length);

      return {
        ...prev,
        steps: updatedSteps,
        overallProgress: Math.min(100, overallProgress)
      };
    });
  }, []);

  // 设置步骤错误
  const setStepError = useCallback((stepId: string, errorMessage?: string) => {
    setProcessState(prev => ({
      ...prev,
      steps: prev.steps.map(step => 
        step.id === stepId ? { 
          ...step, 
          status: 'error' as const, 
          progress: 0 
        } : step
      )
    }));
  }, []);

  // 添加文件进度跟踪
  const addFileProgress = useCallback((fileId: string, fileName: string) => {
    setProcessState(prev => ({
      ...prev,
      fileProgresses: [
        ...prev.fileProgresses.filter(fp => fp.fileId !== fileId),
        {
          fileId,
          fileName,
          currentStep: 'upload',
          stepProgress: 0
        }
      ]
    }));
  }, []);

  // 更新文件进度
  const updateFileProgress = useCallback((fileId: string, stepId: string, progress: number) => {
    setProcessState(prev => {
      const updatedFileProgresses = prev.fileProgresses.map(fp =>
        fp.fileId === fileId ? { ...fp, currentStep: stepId, stepProgress: progress } : fp
      );

      // 计算整体进度
      const totalFiles = updatedFileProgresses.length;
      if (totalFiles === 0) return prev;

      const stepWeights = {
        'upload': 1,
        'validate': 2,
        'configure': 3,
        'analyze': 4,
        'generate': 5,
        'download': 6
      };

      const totalProgress = updatedFileProgresses.reduce((sum, fp) => {
        const stepWeight = stepWeights[fp.currentStep as keyof typeof stepWeights] || 1;
        // 修正计算公式：每个步骤占总进度的1/6，当前步骤内的进度按比例计算
        // 当文件完成所有步骤时，应该达到100%
        const maxWeight = 6; // download步骤的权重
        const fileProgress = ((stepWeight - 1) * 100 + fp.stepProgress) / maxWeight;
        return sum + fileProgress;
      }, 0);

      // 修正总体进度计算：确保能达到100%
      const overallProgress = totalFiles > 0 ? Math.min(100, (totalProgress / totalFiles)) : 0;

      // 更新步骤状态
      const updatedSteps = prev.steps.map(step => {
        const filesInThisStep = updatedFileProgresses.filter(fp => fp.currentStep === step.id);
        const filesCompletedThisStep = updatedFileProgresses.filter(fp => {
          const stepWeight = stepWeights[fp.currentStep as keyof typeof stepWeights] || 1;
          const currentStepWeight = stepWeights[step.id as keyof typeof stepWeights] || 1;
          return stepWeight > currentStepWeight || (stepWeight === currentStepWeight && fp.stepProgress === 100);
        });

        if (step.id === 'validate') {
          // 验证步骤特殊处理：基于已验证文件数量计算进度
          const validatedFiles = updatedFileProgresses.filter(fp =>
            fp.currentStep === 'validate' && fp.stepProgress === 100
          ).length;
          const validatingFiles = updatedFileProgresses.filter(fp =>
            fp.currentStep === 'validate' && fp.stepProgress > 0 && fp.stepProgress < 100
          ).length;

          if (validatedFiles === totalFiles && totalFiles > 0) {
            return { ...step, status: 'completed' as const, progress: 100 };
          } else if (validatedFiles > 0 || validatingFiles > 0) {
            const progress = totalFiles > 0 ? Math.round((validatedFiles / totalFiles) * 100) : 0;
            return { ...step, status: 'active' as const, progress };
          } else {
            return { ...step, status: 'pending' as const, progress: 0 };
          }
        } else if (step.id === 'analyze') {
          // 智能分析步骤特殊处理：基于已分析文件数量计算进度
          const analyzedFiles = updatedFileProgresses.filter(fp =>
            fp.currentStep === 'analyze' && fp.stepProgress === 100
          ).length;
          const analyzingFiles = updatedFileProgresses.filter(fp =>
            fp.currentStep === 'analyze' && fp.stepProgress > 0 && fp.stepProgress < 100
          ).length;

          if (analyzedFiles === totalFiles && totalFiles > 0) {
            return { ...step, status: 'completed' as const, progress: 100 };
          } else if (analyzedFiles > 0 || analyzingFiles > 0) {
            const progress = totalFiles > 0 ? Math.round((analyzedFiles / totalFiles) * 100) : 0;
            return { ...step, status: 'active' as const, progress };
          } else {
            return { ...step, status: 'pending' as const, progress: 0 };
          }
        } else if (filesInThisStep.length > 0) {
          const avgProgress = filesInThisStep.reduce((sum, fp) => sum + fp.stepProgress, 0) / filesInThisStep.length;
          return { ...step, status: 'active' as const, progress: Math.round(avgProgress) };
        } else if (filesCompletedThisStep.length === totalFiles && totalFiles > 0) {
          return { ...step, status: 'completed' as const, progress: 100 };
        } else {
          return { ...step, status: 'pending' as const, progress: 0 };
        }
      });

      return {
        ...prev,
        fileProgresses: updatedFileProgresses,
        steps: updatedSteps,
        overallProgress: Math.round(overallProgress)
      };
    });
  }, []);

  // 重置所有步骤
  const resetFlow = useCallback(() => {
    setProcessState({
      currentStep: '',
      steps: initialSteps,
      overallProgress: 0,
      fileProgresses: []
    });
    // 重置智能分析状态
    setAnalysisStage(null);
  }, []);

  // 完整重置函数（供外部调用）
  const resetAll = useCallback(() => {
    resetFlow();
  }, [resetFlow]);

  // 文件上传时的联动
  const onFileUploaded = useCallback((fileId: string, fileName: string) => {
    addFileProgress(fileId, fileName);
    updateFileProgress(fileId, 'upload', 100);
  }, [addFileProgress, updateFileProgress]);

  // 文件验证时的联动
  const onFileValidation = useCallback((fileId: string) => {
    updateFileProgress(fileId, 'validate', 0);

    // 模拟验证过程
    let progress = 0;
    const interval = setInterval(() => {
      progress += 20;
      updateFileProgress(fileId, 'validate', progress);
      if (progress >= 100) {
        clearInterval(interval);
      }
    }, 200);
  }, [updateFileProgress]);

  // 文件处理时的联动
  const onFileProcessing = useCallback((fileId: string) => {
    updateFileProgress(fileId, 'configure', 0);

    // 模拟配置过程
    let configProgress = 0;
    const configInterval = setInterval(() => {
      configProgress += 25;
      updateFileProgress(fileId, 'configure', configProgress);
      if (configProgress >= 100) {
        clearInterval(configInterval);

        // 开始分析过程
        updateFileProgress(fileId, 'analyze', 0);
        let analyzeProgress = 0;
        const analyzeInterval = setInterval(() => {
          analyzeProgress += 10;
          updateFileProgress(fileId, 'analyze', analyzeProgress);
          if (analyzeProgress >= 100) {
            clearInterval(analyzeInterval);
          }
        }, 300);
      }
    }, 250);
  }, [updateFileProgress]);

  // 报表生成时的联动
  const onReportGeneration = useCallback(() => {
    // 激活报表生成步骤
    activateStep('generate');

    // 为所有已处理的文件生成报表
    processState.fileProgresses.forEach(fp => {
      if (fp.currentStep === 'analyze' && fp.stepProgress === 100) {
        updateFileProgress(fp.fileId, 'generate', 0);

        // 模拟报表生成过程
        let progress = 0;
        const interval = setInterval(() => {
          progress += 15;
          updateFileProgress(fp.fileId, 'generate', progress);
          if (progress >= 100) {
            clearInterval(interval);
            updateFileProgress(fp.fileId, 'download', 100);
          }
        }, 400);
      }
    });

    // 模拟报表生成完成后自动激活下载步骤
    setTimeout(() => {
      completeStep('generate');
      activateStep('download');

      // 再延迟一点完成下载步骤，显示下载界面
      setTimeout(() => {
        completeStep('download');
      }, 1000);
    }, 6000); // 6秒后完成报表生成
  }, [processState.fileProgresses, updateFileProgress, activateStep, completeStep]);

  return {
    processState,
    activateStep,
    updateStepProgress,
    completeStep,
    setStepError,
    resetFlow,
    addFileProgress,
    updateFileProgress,
    // 联动方法
    onFileUploaded,
    onFileValidation,
    onFileProcessing,
    onReportGeneration,
    // 智能分析状态
    analysisStage,
    setAnalysisStage,
    // 完整重置
    resetAll
  };
};
