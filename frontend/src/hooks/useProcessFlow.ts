import { useState, useCallback } from 'react';

export interface ProcessStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'active' | 'completed' | 'error';
  progress?: number;
}

export interface ProcessFlowState {
  currentStep: string;
  steps: ProcessStep[];
  overallProgress: number;
}

const initialSteps: ProcessStep[] = [
  {
    id: 'upload',
    title: '文件上传',
    description: '上传入户数据和投诉数据文件',
    status: 'pending'
  },
  {
    id: 'validate',
    title: '内容检测',
    description: '验证文件格式和必要字段',
    status: 'pending'
  },
  {
    id: 'configure',
    title: '解析配置',
    description: '配置智能解析规则',
    status: 'pending'
  },
  {
    id: 'analyze',
    title: '智能分析',
    description: '提取转写文本并进行智能解析',
    status: 'pending'
  },
  {
    id: 'generate',
    title: '报表生成',
    description: '生成分析报表和可视化图表',
    status: 'pending'
  },
  {
    id: 'download',
    title: '结果下载',
    description: '下载分析结果和报表文件',
    status: 'pending'
  }
];

export const useProcessFlow = () => {
  const [processState, setProcessState] = useState<ProcessFlowState>({
    currentStep: '',
    steps: initialSteps,
    overallProgress: 0
  });

  // 激活某个步骤
  const activateStep = useCallback((stepId: string) => {
    setProcessState(prev => ({
      ...prev,
      currentStep: stepId,
      steps: prev.steps.map(step => ({
        ...step,
        status: step.id === stepId ? 'active' : 
                step.status === 'active' ? 'pending' : step.status,
        progress: step.id === stepId ? 0 : step.progress
      }))
    }));
  }, []);

  // 更新步骤进度
  const updateStepProgress = useCallback((stepId: string, progress: number) => {
    setProcessState(prev => ({
      ...prev,
      steps: prev.steps.map(step => 
        step.id === stepId ? { ...step, progress } : step
      )
    }));
  }, []);

  // 完成某个步骤
  const completeStep = useCallback((stepId: string) => {
    setProcessState(prev => {
      const updatedSteps = prev.steps.map(step => 
        step.id === stepId ? { ...step, status: 'completed' as const, progress: 100 } : step
      );
      
      const completedCount = updatedSteps.filter(s => s.status === 'completed').length;
      const overallProgress = (completedCount / updatedSteps.length) * 100;
      
      return {
        ...prev,
        steps: updatedSteps,
        overallProgress
      };
    });
  }, []);

  // 设置步骤错误
  const setStepError = useCallback((stepId: string, errorMessage?: string) => {
    setProcessState(prev => ({
      ...prev,
      steps: prev.steps.map(step => 
        step.id === stepId ? { 
          ...step, 
          status: 'error' as const, 
          progress: 0 
        } : step
      )
    }));
  }, []);

  // 重置所有步骤
  const resetFlow = useCallback(() => {
    setProcessState({
      currentStep: '',
      steps: initialSteps,
      overallProgress: 0
    });
  }, []);

  // 文件上传时的联动
  const onFileUploaded = useCallback(() => {
    activateStep('upload');
    setTimeout(() => completeStep('upload'), 1000);
  }, [activateStep, completeStep]);

  // 文件验证时的联动
  const onFileValidation = useCallback(() => {
    activateStep('validate');
    // 模拟验证过程
    let progress = 0;
    const interval = setInterval(() => {
      progress += 20;
      updateStepProgress('validate', progress);
      if (progress >= 100) {
        clearInterval(interval);
        completeStep('validate');
      }
    }, 200);
  }, [activateStep, updateStepProgress, completeStep]);

  // 文件处理时的联动
  const onFileProcessing = useCallback(() => {
    activateStep('configure');
    setTimeout(() => {
      completeStep('configure');
      activateStep('analyze');
      
      // 模拟分析过程
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        updateStepProgress('analyze', progress);
        if (progress >= 100) {
          clearInterval(interval);
          completeStep('analyze');
        }
      }, 300);
    }, 1000);
  }, [activateStep, completeStep, updateStepProgress]);

  // 报表生成时的联动
  const onReportGeneration = useCallback(() => {
    activateStep('generate');
    // 模拟报表生成过程
    let progress = 0;
    const interval = setInterval(() => {
      progress += 15;
      updateStepProgress('generate', progress);
      if (progress >= 100) {
        clearInterval(interval);
        completeStep('generate');
        activateStep('download');
        setTimeout(() => completeStep('download'), 500);
      }
    }, 400);
  }, [activateStep, updateStepProgress, completeStep]);

  return {
    processState,
    activateStep,
    updateStepProgress,
    completeStep,
    setStepError,
    resetFlow,
    // 联动方法
    onFileUploaded,
    onFileValidation,
    onFileProcessing,
    onReportGeneration
  };
};
